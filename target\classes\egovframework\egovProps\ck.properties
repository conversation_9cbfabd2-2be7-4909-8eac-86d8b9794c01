# CKEditor Image File Upload

#optional
ck.image.type.allow=jpg,jpeg,gif,bmp,png
#optional
#ck.image.save.class=Implementation of egovframework.com.utl.wed.filter.FileSaveManager 

# if you use "separate image server" or other case. (apache, cdn, nas...)
#ck.image.dir=/www/images.mydomain.com/upload
#ck.image.url=http://images.mydomain.com/upload/

# if you use "web application"'s deployed directory.
#ck.image.dir=/upload_dir : define Globals.fileStorePath(globals.properties) sub directory => Do not start with "/".
#ck.image.url=/contextRoot/upload/
ck.image.dir=ck_image
ck.image.url=/ckUploadImage