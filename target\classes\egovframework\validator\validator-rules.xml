<!DOCTYPE form-validation PUBLIC
          "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.0//EN"
          "http://jakarta.apache.org/commons/dtds/validator_1_0.dtd">
<!--

   This file contains the default Struts Validator pluggable validator
   definitions.  It should be placed somewhere under /WEB-INF and
   referenced in the struts-config.xml under the plug-in element
   for the ValidatorPlugIn.

      <plug-in className="org.apache.struts.validator.ValidatorPlugIn">
        <set-property property="pathnames" value="/WEB-INF/validator-rules.xml,
                                                  /WEB-INF/validation.xml"/>
      </plug-in>

   These are the default error messages associated with
   each validator defined in this file.  They should be
   added to your projects ApplicationResources.properties
   file or you can associate new ones by modifying the
   pluggable validators msg attributes in this file.

   # Struts Validator Error Messages
   errors.required={0} is required.
   errors.minlength={0} can not be less than {1} characters.
   errors.maxlength={0} can not be greater than {1} characters.
   errors.invalid={0} is invalid.

   errors.byte={0} must be a byte.
   errors.short={0} must be a short.
   errors.integer={0} must be an integer.
   errors.long={0} must be a long.
   errors.float={0} must be a float.
   errors.double={0} must be a double.

   errors.date={0} is not a date.
   errors.range={0} is not in the range {1} through {2}.
   errors.creditcard={0} is an invalid credit card number.
   errors.email={0} is an invalid e-mail address.

-->

<form-validation>

   <global>

      <validator name="required"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateRequired"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"
                  msg="errors.required">

         <javascript><![CDATA[
            function validateRequired(form) {
                var isValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oRequired = new required();                                
                for (x in oRequired) {
                var field = form[oRequired[x][0]];                 

           if ((field.type == 'hidden' ||
               field.type == 'text' ||
               field.type == 'textarea' ||
               field.type == 'file' ||
               field.type == 'radio' ||
               field.type == 'checkbox' ||
               field.type == 'select-one' ||
               field.type == 'password')) { 
               var value = '';
               // get field's value
               if (field.type == "select-one") {
                   var si = field.selectedIndex;
                   if (si >= 0) {
                       value = field.options[si].value;
                   }
               } else if (field.type == 'radio' || field.type == 'checkbox') {
                   if (field.checked) {
                       value = field.value;
                   }
               } else {
                   value = field.value;
               }
               if (trim(value).length == 0) {
                   if ((i == 0) && (field.type != 'hidden')) {
                       focusField = field;
                   }
                   fields[i++] = oRequired[x][1];
                   isValid = false;
               }
           } else if (field.type == "select-multiple") { 
               var numOptions = field.options.length;
               lastSelected=-1;
               for(loop=numOptions-1;loop>=0;loop--) {
                   if(field.options[loop].selected) {
                       lastSelected = loop;
                       value = field.options[loop].value;
                       break;
                   }
               }
               if(lastSelected < 0 || trim(value).length == 0) {
                   if(i == 0) {
                       focusField = field;
                   }
                   fields[i++] = oRequired[x][1];
                   isValid=false;
               }
           } else if ((field.length > 0) && (field[0].type == 'radio' || field[0].type == 'checkbox')) {
               isChecked=-1;
               for (loop=0;loop < field.length;loop++) {
                   if (field[loop].checked) {
                       isChecked=loop;
                       break; // only one needs to be checked
                   }
               }
               if (isChecked < 0) {
                   if (i == 0) {
                       focusField = field[0];
                   }
                   fields[i++] = oRequired[x][1];
                   isValid=false;
               }
           }
                }
                if (fields.length > 0) {
                   		try{
                   			fields.focus();
                   	    }catch(e){
                    	    console.log(e);
                        }
                   alert(fields.join('\n'));
                }
                return isValid;
            }
            
            // Trim whitespace from left and right sides of s.
            function trim(s) {
                return s.replace( /^\s*/, "" ).replace( /\s*$/, "" );
            }
            
            ]]>
         </javascript>

      </validator>


      <validator name="requiredif"
                 classname="org.springmodules.validation.commons.FieldChecks"
                 method="validateRequiredIf"
                 methodParams="java.lang.Object,
                               org.apache.commons.validator.ValidatorAction,
                               org.apache.commons.validator.Field,
                               org.springframework.validation.Errors,
                               org.apache.commons.validator.Validator"

                 msg="errors.required">
      </validator>

      <validator name="validwhen"
                 classname="org.springmodules.validation.commons.FieldChecks"
                 method="validateValidWhen"
                 methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors,
                       org.apache.commons.validator.Validator"
                 msg="errors.required"/>

      <validator name="minlength"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateMinLength"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.minlength">

         <javascript><![CDATA[
            function validateMinLength(form) {
                var isValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oMinLength = new minlength();
                for (x in oMinLength) {
                    var field = form[oMinLength[x][0]];

                    if (field.type == 'text' ||
                        field.type == 'textarea') {

                        var iMin = parseInt(oMinLength[x][2]("minlength"));
                        if ((trim(field.value).length > 0) && (field.value.length < iMin)) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oMinLength[x][1];
                            isValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return isValid;
            }]]>
         </javascript>

      </validator>


      <validator name="maxlength"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateMaxLength"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.maxlength">

         <javascript><![CDATA[
            function validateMaxLength(form) {
                var isValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oMaxLength = new maxlength();
                for (x in oMaxLength) {
                    var field = form[oMaxLength[x][0]];

                    if (field.type == 'text' ||
                        field.type == 'textarea') {

                        var iMax = parseInt(oMaxLength[x][2]("maxlength"));
                        if (field.value.length > iMax) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oMaxLength[x][1];
                            isValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return isValid;
            }]]>
         </javascript>

      </validator>


      <validator name="mask"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateMask"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.invalid">

         <javascript><![CDATA[
            function validateMask(form) {
                var isValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oMasked = new mask();
                for (x in oMasked) {
                    var field = form[oMasked[x][0]];

                    if ((field.type == 'text' ||
                         field.type == 'textarea') &&
                         (field.value.length > 0)) {

                        if (!matchPattern(field.value, oMasked[x][2]("mask"))) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oMasked[x][1];
                            isValid = false;
                        }
                    }
                }

                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return isValid;
            }

            function matchPattern(value, mask) {
               return mask.exec(value);
            }]]>
         </javascript>

      </validator>


      <validator name="byte"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateByte"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.byte"
       jsFunctionName="ByteValidations">

         <javascript><![CDATA[
            function validateByte(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oByte = new ByteValidations();
                for (x in oByte) {
                	var field = form[oByte[x][0]];

                    if (field.type == 'text' ||
                        field.type == 'textarea' ||
                        field.type == 'select-one' ||
						field.type == 'radio') {

						var value = '';
						// get field's value
						if (field.type == "select-one") {
							var si = field.selectedIndex;
							if (si >= 0) {
								value = field.options[si].value;
							}
						} else {
							value = field.value;
						}

                        if (value.length > 0) {
                            if (!isAllDigits(value)) {
                                bValid = false;
                                if (i == 0) {
                                    focusField = field;
                                }
                                fields[i++] = oByte[x][1];

                            } else {

	                            var iValue = parseInt(value);
	                            if (isNaN(iValue) || !(iValue >= -128 && iValue <= 127)) {
	                                if (i == 0) {
	                                    focusField = field;
	                                }
	                                fields[i++] = oByte[x][1];
	                                bValid = false;
	                            }
                            }
						}

                    }
                }
                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return bValid;
            }]]>
         </javascript>

      </validator>


      <validator name="short"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateShort"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.short"
       jsFunctionName="ShortValidations">

         <javascript><![CDATA[
            function validateShort(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oShort = new ShortValidations();
                for (x in oShort) {
                	var field = form[oShort[x][0]];

                    if (field.type == 'text' ||
                        field.type == 'textarea' ||
                        field.type == 'select-one' ||
                        field.type == 'radio') {

                        var value = '';
						// get field's value
						if (field.type == "select-one") {
							var si = field.selectedIndex;
							if (si >= 0) {
								value = field.options[si].value;
							}
						} else {
							value = field.value;
						}

                        if (value.length > 0) {
                            if (!isAllDigits(value)) {
                                bValid = false;
                                if (i == 0) {
                                    focusField = field;
                                }
                                fields[i++] = oShort[x][1];

                            } else {

	                            var iValue = parseInt(value);
	                            if (isNaN(iValue) || !(iValue >= -32768 && iValue <= 32767)) {
	                                if (i == 0) {
	                                    focusField = field;
	                                }
	                                fields[i++] = oShort[x][1];
	                                bValid = false;
	                            }
	                       }
                       }
                    }
                }
                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return bValid;
            }]]>
         </javascript>

      </validator>


      <validator name="integer"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateInteger"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.integer"
       jsFunctionName="IntegerValidations">

         <javascript><![CDATA[
            function validateInteger(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oInteger = new IntegerValidations();
                for (x in oInteger) {
                	var field = form[oInteger[x][0]];

                    if (field.type == 'text' ||
                        field.type == 'textarea' ||
                        field.type == 'select-one' ||
                        field.type == 'radio') {

                        var value = '';
						// get field's value
						if (field.type == "select-one") {
							var si = field.selectedIndex;
						    if (si >= 0) {
							    value = field.options[si].value;
						    }
						} else {
							value = field.value;
						}

                        if (value.length > 0) {

                            if (!isAllDigits(value)) {
                                bValid = false;
                                if (i == 0) {
	                                focusField = field;
	                            }
						        fields[i++] = oInteger[x][1];

                            } else {
	                            var iValue = parseInt(value);
	                            if (isNaN(iValue) || !(iValue >= -2147483648 && iValue <= 2147483647)) {
	                                if (i == 0) {
	                                    focusField = field;
	                                }
	                                fields[i++] = oInteger[x][1];
	                                bValid = false;
	                           }
                           }
                       }
                    }
                }
                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return bValid;
            }

            function isAllDigits(argvalue) {
                argvalue = argvalue.toString();
                var validChars = "0123456789";
                var startFrom = 0;
                if (argvalue.substring(0, 2) == "0x") {
                   validChars = "0123456789abcdefABCDEF";
                   startFrom = 2;
                } else if (argvalue.charAt(0) == "0") {
                   validChars = "01234567";
                   startFrom = 1;
                } else if (argvalue.charAt(0) == "-") {
                    startFrom = 1;
                }

                for (var n = startFrom; n < argvalue.length; n++) {
                    if (validChars.indexOf(argvalue.substring(n, n+1)) == -1) return false;
                }
                return true;
            }]]>
         </javascript>

      </validator>


      <validator name="long"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateLong"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.long"/>


      <validator name="float"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateFloat"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.float"
       jsFunctionName="FloatValidations">

         <javascript><![CDATA[
            function validateFloat(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oFloat = new FloatValidations();
                for (x in oFloat) {
                	var field = form[oFloat[x][0]];

                    if (field.type == 'text' ||
                        field.type == 'textarea' ||
                        field.type == 'select-one' ||
                        field.type == 'radio') {

                    	var value = '';
						// get field's value
						if (field.type == "select-one") {
							var si = field.selectedIndex;
							if (si >= 0) {
							    value = field.options[si].value;
							}
						} else {
							value = field.value;
						}

                        if (value.length > 0) {
                            // remove '.' before checking digits
                            var tempArray = value.split('.');
                            var joinedString= tempArray.join('');

                            if (!isAllDigits(joinedString)) {
                                bValid = false;
                                if (i == 0) {
                                    focusField = field;
                                }
                                fields[i++] = oFloat[x][1];

                            } else {
	                            var iValue = parseFloat(value);
	                            if (isNaN(iValue)) {
	                                if (i == 0) {
	                                    focusField = field;
	                                }
	                                fields[i++] = oFloat[x][1];
	                                bValid = false;
	                            }
                            }
                        }
                    }
                }
                if (fields.length > 0) {
                   focusField.focus();
                   alert(fields.join('\n'));
                }
                return bValid;
            }]]>
         </javascript>

      </validator>


      <validator name="double"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateDouble"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.double"/>


      <validator name="date"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateDate"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.date"
       jsFunctionName="DateValidations">

         <javascript><![CDATA[
            function validateDate(form) {
               var bValid = true;
               var focusField = null;
               var i = 0;
               var fields = new Array();
               oDate = new DateValidations();
               for (x in oDate) {
                   var value = form[oDate[x][0]].value;
                   var datePattern = oDate[x][2]("datePatternStrict");
                   if ((form[oDate[x][0]].type == 'text' ||
                        form[oDate[x][0]].type == 'textarea') &&
                       (value.length > 0) &&
                       (datePattern.length > 0)) {
                     var MONTH = "MM";
                     var DAY = "dd";
                     var YEAR = "yyyy";
                     var orderMonth = datePattern.indexOf(MONTH);
                     var orderDay = datePattern.indexOf(DAY);
                     var orderYear = datePattern.indexOf(YEAR);
                     if ((orderDay < orderYear && orderDay > orderMonth)) {
                         var iDelim1 = orderMonth + MONTH.length;
                         var iDelim2 = orderDay + DAY.length;
                         var delim1 = datePattern.substring(iDelim1, iDelim1 + 1);
                         var delim2 = datePattern.substring(iDelim2, iDelim2 + 1);
                         if (iDelim1 == orderDay && iDelim2 == orderYear) {
                            dateRegexp = new RegExp("^(\\d{2})(\\d{2})(\\d{4})$");
                         } else if (iDelim1 == orderDay) {
                            dateRegexp = new RegExp("^(\\d{2})(\\d{2})[" + delim2 + "](\\d{4})$");
                         } else if (iDelim2 == orderYear) {
                            dateRegexp = new RegExp("^(\\d{2})[" + delim1 + "](\\d{2})(\\d{4})$");
                         } else {
                            dateRegexp = new RegExp("^(\\d{2})[" + delim1 + "](\\d{2})[" + delim2 + "](\\d{4})$");
                         }
                         var matched = dateRegexp.exec(value);
                         if(matched != null) {
                            if (!isValidDate(matched[2], matched[1], matched[3])) {
                               if (i == 0) {
                                   focusField = form[oDate[x][0]];
                               }
                               fields[i++] = oDate[x][1];
                               bValid =  false;
                            }
                         } else {
                            if (i == 0) {
                                focusField = form[oDate[x][0]];
                            }
                            fields[i++] = oDate[x][1];
                            bValid =  false;
                         }
                     } else if ((orderMonth < orderYear && orderMonth > orderDay)) {
                         var iDelim1 = orderDay + DAY.length;
                         var iDelim2 = orderMonth + MONTH.length;
                         var delim1 = datePattern.substring(iDelim1, iDelim1 + 1);
                         var delim2 = datePattern.substring(iDelim2, iDelim2 + 1);
                         if (iDelim1 == orderMonth && iDelim2 == orderYear) {
                             dateRegexp = new RegExp("^(\\d{2})(\\d{2})(\\d{4})$");
                         } else if (iDelim1 == orderMonth) {
                             dateRegexp = new RegExp("^(\\d{2})(\\d{2})[" + delim2 + "](\\d{4})$");
                         } else if (iDelim2 == orderYear) {
                             dateRegexp = new RegExp("^(\\d{2})[" + delim1 + "](\\d{2})(\\d{4})$");
                         } else {
                             dateRegexp = new RegExp("^(\\d{2})[" + delim1 + "](\\d{2})[" + delim2 + "](\\d{4})$");
                         }
                         var matched = dateRegexp.exec(value);
                         if(matched != null) {
                             if (!isValidDate(matched[1], matched[2], matched[3])) {
                                 if (i == 0) {
                                     focusField = form[oDate[x][0]];
                                 }
                                 fields[i++] = oDate[x][1];
                                 bValid =  false;
                              }
                         } else {
                             if (i == 0) {
                                 focusField = form[oDate[x][0]];
                             }
                             fields[i++] = oDate[x][1];
                             bValid =  false;
                         }
                     } else if ((orderMonth > orderYear && orderMonth < orderDay)) {
                         var iDelim1 = orderYear + YEAR.length;
                         var iDelim2 = orderMonth + MONTH.length;
                         var delim1 = datePattern.substring(iDelim1, iDelim1 + 1);
                         var delim2 = datePattern.substring(iDelim2, iDelim2 + 1);
                         if (iDelim1 == orderMonth && iDelim2 == orderDay) {
                             dateRegexp = new RegExp("^(\\d{4})(\\d{2})(\\d{2})$");
                         } else if (iDelim1 == orderMonth) {
                             dateRegexp = new RegExp("^(\\d{4})(\\d{2})[" + delim2 + "](\\d{2})$");
                         } else if (iDelim2 == orderDay) {
                             dateRegexp = new RegExp("^(\\d{4})[" + delim1 + "](\\d{2})(\\d{2})$");
                         } else {
                             dateRegexp = new RegExp("^(\\d{4})[" + delim1 + "](\\d{2})[" + delim2 + "](\\d{2})$");
                         }
                         var matched = dateRegexp.exec(value);
                         if(matched != null) {
                             if (!isValidDate(matched[3], matched[2], matched[1])) {
                                 if (i == 0) {
                                     focusField = form[oDate[x][0]];
                                  }
                                  fields[i++] = oDate[x][1];
                                  bValid =  false;
                              }
                          } else {
                              if (i == 0) {
                                  focusField = form[oDate[x][0]];
                              }
                              fields[i++] = oDate[x][1];
                              bValid =  false;
                          }
                     } else {
                         if (i == 0) {
                             focusField = form[oDate[x][0]];
                         }
                         fields[i++] = oDate[x][1];
                         bValid =  false;
                     }
                  }
               }
               if (fields.length > 0) {
                  focusField.focus();
                  alert(fields.join('\n'));
               }
               return bValid;
            }

	    function isValidDate(day, month, year) {
	        if (month < 1 || month > 12) {
                    return false;
                }
                if (day < 1 || day > 31) {
                    return false;
                }
                if ((month == 4 || month == 6 || month == 9 || month == 11) &&
                    (day == 31)) {
                    return false;
                }
                if (month == 2) {
                    var leap = (year % 4 == 0 &&
                               (year % 100 != 0 || year % 400 == 0));
                    if (day>29 || (day == 29 && !leap)) {
                        return false;
                    }
                }
                return true;
            }]]>
         </javascript>

      </validator>

<!-- range is deprecated use intRange instead -->
      <validator name="range"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateIntRange"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends="integer"
                  msg="errors.range">

         <javascript><![CDATA[
            function validateRange(form) {
                return validateIntRange(form);
            }]]>
         </javascript>

      </validator>

      <validator name="intRange"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateIntRange"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends="integer"
                  msg="errors.range">

         <javascript><![CDATA[
            function validateIntRange(form) {
                var isValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oRange = new intRange();
                for (x in oRange) {
                    var field = form[oRange[x][0]];

                    if ((field.type == 'text' ||
                         field.type == 'textarea') &&
                        (field.value.length > 0)) {

                        var iMin = parseInt(oRange[x][2]("min"));
                        var iMax = parseInt(oRange[x][2]("max"));
                        var iValue = parseInt(field.value);
                        if (!(iValue >= iMin && iValue <= iMax)) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oRange[x][1];
                            isValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                    focusField.focus();
                    alert(fields.join('\n'));
                }
                return isValid;
            }]]>
         </javascript>

      </validator>

      <validator name="floatRange"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateFloatRange"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends="float"
                  msg="errors.range">

         <javascript><![CDATA[
            function validateFloatRange(form) {
                var isValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oRange = new floatRange();
                for (x in oRange) {
                    var field = form[oRange[x][0]];

                    if ((field.type == 'text' ||
                         field.type == 'textarea') &&
                        (field.value.length > 0)) {

                        var fMin = parseFloat(oRange[x][2]("min"));
                        var fMax = parseFloat(oRange[x][2]("max"));
                        var fValue = parseFloat(field.value);
                        if (!(fValue >= fMin && fValue <= fMax)) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oRange[x][1];
                            isValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                    focusField.focus();
                    alert(fields.join('\n'));
                }
                return isValid;
            }]]>
         </javascript>

      </validator>

      <validator name="creditCard"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateCreditCard"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.creditcard">

         <javascript><![CDATA[
            function validateCreditCard(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oCreditCard = new creditCard();
                for (x in oCreditCard) {
                    if ((form[oCreditCard[x][0]].type == 'text' ||
                         form[oCreditCard[x][0]].type == 'textarea') &&
                        (form[oCreditCard[x][0]].value.length > 0)) {
                        if (!luhnCheck(form[oCreditCard[x][0]].value)) {
                            if (i == 0) {
                                focusField = form[oCreditCard[x][0]];
                            }
                            fields[i++] = oCreditCard[x][1];
                            bValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                    focusField.focus();
                    alert(fields.join('\n'));
                }
                return bValid;
            }

            /**
             * Reference: http://www.ling.nwu.edu/~sburke/pub/luhn_lib.pl
             */
            function luhnCheck(cardNumber) {
                if (isLuhnNum(cardNumber)) {
                    var no_digit = cardNumber.length;
                    var oddoeven = no_digit & 1;
                    var sum = 0;
                    for (var count = 0; count < no_digit; count++) {
                        var digit = parseInt(cardNumber.charAt(count));
                        if (!((count & 1) ^ oddoeven)) {
                            digit *= 2;
                            if (digit > 9) digit -= 9;
                        };
                        sum += digit;
                    };
                    if (sum == 0) return false;
                    if (sum % 10 == 0) return true;
                };
                return false;
            }

            function isLuhnNum(argvalue) {
                argvalue = argvalue.toString();
                if (argvalue.length == 0) {
                    return false;
                }
                for (var n = 0; n < argvalue.length; n++) {
                    if ((argvalue.substring(n, n+1) < "0") ||
                        (argvalue.substring(n,n+1) > "9")) {
                        return false;
                    }
                }
                return true;
            }]]>
         </javascript>

      </validator>


      <validator name="email"
            classname="org.springmodules.validation.commons.FieldChecks"
               method="validateEmail"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.email">

         <javascript><![CDATA[
            function validateEmail(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oEmail = new email();
                for (x in oEmail) {
                    if ((form[oEmail[x][0]].type == 'text' ||
                         form[oEmail[x][0]].type == 'textarea') &&
                        (form[oEmail[x][0]].value.length > 0)) {
                        if (!checkEmail(form[oEmail[x][0]].value)) {
                            if (i == 0) {
                                focusField = form[oEmail[x][0]];
                            }
                            fields[i++] = oEmail[x][1];
                            bValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                    focusField.focus();
                    alert(fields.join('\n'));
                }
                return bValid;
            }

            /**
             * Reference: Sandeep V. Tamhankar (<EMAIL>),
             * http://javascript.internet.com
             */
            function checkEmail(emailStr) {
               if (emailStr.length == 0) {
                   return true;
               }
               var emailPat=/^(.+)@(.+)$/;
               var specialChars="\\(\\)<>@,;:\\\\\\\"\\.\\[\\]";
               var validChars="\[^\\s" + specialChars + "\]";
               var quotedUser="(\"[^\"]*\")";
               var ipDomainPat=/^(\d{1,3})[.](\d{1,3})[.](\d{1,3})[.](\d{1,3})$/;
               var atom=validChars + '+';
               var word="(" + atom + "|" + quotedUser + ")";
               var userPat=new RegExp("^" + word + "(\\." + word + ")*$");
               var domainPat=new RegExp("^" + atom + "(\\." + atom + ")*$");
               var matchArray=emailStr.match(emailPat);
               if (matchArray == null) {
                   return false;
               }
               var user=matchArray[1];
               var domain=matchArray[2];
               if (user.match(userPat) == null) {
                   return false;
               }
               var IPArray = domain.match(ipDomainPat);
               if (IPArray != null) {
                   for (var i = 1; i <= 4; i++) {
                      if (IPArray[i] > 255) {
                         return false;
                      }
                   }
                   return true;
               }
               var domainArray=domain.match(domainPat);
               if (domainArray == null) {
                   return false;
               }
               var atomPat=new RegExp(atom,"g");
               var domArr=domain.match(atomPat);
               var len=domArr.length;
               if ((domArr[domArr.length-1].length < 2) ||
                   (domArr[domArr.length-1].length > 3)) {
                   return false;
               }
               if (len < 2) {
                   return false;
               }
               return true;
            }]]>
         </javascript>

      </validator>

      <!--
      	주민등록번호 validation.
      	Commons Validator가 제공하는 validator가 아니라 추가된 validator이므로
      	실행을 위해서는 반드시  org.egovframe.rte.ptl.mvcXXXXX.jar가 필요하다.
      	2020-10-16 신용호 - 주민번호 남녀구분 관련 수정
      -->
      <validator name="ihidnum"
            classname="org.egovframe.rte.ptl.mvc.validation.RteFieldChecks"
               method="validateIhIdNum"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"

              depends=""
                  msg="errors.ihidnum">
           <javascript><![CDATA[
           function validateIhIdNum(form) {
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oIhidnum = new ihidnum();
                for (x in oIhidnum) {
                	var field = form[oIhidnum[x][0]];
                	if (field.type == 'text' ||
                    	field.type == 'hidden' ||
                        field.type == 'textarea') {
                        if (trim(field.value).length==0 || !checkIhIdNum(field.value)) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oIhidnum[x][1];
                            bValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                    focusField.focus();
                    alert(fields.join('\n'));
                }
                return bValid;
            }

            /**
             * Reference: JS Guide
             * http://jsguide.net/ver2/articles/frame.php?artnum=002
             */
            function checkIhIdNum(ihidnum){

            	fmt = /^\d{13}$/;
            	if(!fmt.test(ihidnum)){
            		return false;
            	}

            	switch(ihidnum.charAt(6)*1) {
					case 9,0:
						birthYear = "18";
						break;
					case 1,2,5,6: // 외국인 5,6
						birthYear = "19";
						break;
					case 3,4,7,8: // 외국인 7,8
						birthYear = "20";
						break;
					default:
						birthYear = "19";
				}
            	
				birthYear += ihidnum.substr(0, 2);
				birthMonth = ihidnum.substr(2, 2) - 1;
				birthDate = ihidnum.substr(4, 2);
				birth = new Date(birthYear, birthMonth, birthDate);

				if( birth.getYear() % 100 != ihidnum.substr(0, 2) ||
				    birth.getMonth() != birthMonth ||
				    birth.getDate() != birthDate) {
				    return false;
				}

            	var arrDivide = [2, 3, 4, 5, 6, 7, 8, 9, 2, 3, 4, 5];
            	var checkdigit = 0;
            	for(var i=0;i<ihidnum.length-1;i++){
            		checkdigit += parseInt(ihidnum.charAt(i)) * parseInt(arrDivide[i]);
            	}
            	checkdigit = (11 - (checkdigit%11))%10;
            	if(checkdigit != ihidnum.charAt(12)){
            		return false;
            	}else{
            		return true;
            	}
            }
           ]]>
           </javascript>
      </validator>
	  <validator name="korean"
            classname="org.egovframe.rte.ptl.mvc.validation.RteFieldChecks"
               method="validateKorean"
         methodParams="java.lang.Object,
                       org.apache.commons.validator.ValidatorAction,
                       org.apache.commons.validator.Field,
                       org.springframework.validation.Errors"
                  msg="errors.korean">
          <javascript><![CDATA[
          function validateKorean(form){
                var bValid = true;
                var focusField = null;
                var i = 0;
                var fields = new Array();
                oKorean = new korean();
                for (x in oKorean) {
                	var field = form[oKorean[x][0]];
                    if (field.type == 'text' || field.type == 'textarea') {
                        if (trim(field.value).length==0 || !checkKorean(field.value)) {
                            if (i == 0) {
                                focusField = field;
                            }
                            fields[i++] = oKorean[x][1];
                            bValid = false;
                        }
                    }
                }
                if (fields.length > 0) {
                    focusField.focus();
                    alert(fields.join('\n'));
                }
                return bValid;
          }

          function checkKorean(koreanStr){
          		for(var i=0;i<koreanStr.length;i++){
          			var koreanChar = koreanStr.charCodeAt(i);
          			if( !( 0xAC00 <= koreanChar && koreanChar <= 0xD7A3 ) && !( 0x3131 <= koreanChar && koreanChar <= 0x318E ) ) {
          				return false;
          			}
          		}
          		return true;
          }
          ]]>
          </javascript>
          
      </validator>
      
   </global>

</form-validation>
