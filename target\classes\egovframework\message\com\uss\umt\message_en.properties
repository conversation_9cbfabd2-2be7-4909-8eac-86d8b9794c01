#Login Provision Management#
comUssUmt.stplatCnfirmt.title=Provision Confirmation
comUssUmt.stplatCnfirmt.useStplatCn=Provision Contents
comUssUmt.stplatCnfirmt.infoProvdAgreCn=Informational Contents Agreement Contents

comUssUmt.stplatCnfirmtMsg.useStplat=Agree to the Terms and Conditions of the content.
comUssUmt.stplatCnfirmtMsg.infoProvdAgre=Agree with the use of information.

comUssUmt.stplatCnfirmtValidate.disagreeStplatCnfirmt=You can not sign up if you don't agree to the Provision Contents.

comUssUmt.common.lockAt=Login authorization limit status
comUssUmt.common.lockAtBtn=Release limits for login authentication
comUssUmt.common.lockAtConfirm=Are you sure you want to disable the login limit?

#department Management#
comUssUmt.deptManage.title=Department Management

comUssUmt.deptManageList.deptId=Department ID
comUssUmt.deptManageList.deptName=Department Name
comUssUmt.deptManageList.deptDc=Description
comUssUmt.deptManageSearchCondition.searchKeywordText=Department Name

comUssUmt.deptManageRegist.deptId=Department ID
comUssUmt.deptManageRegist.deptName=Department Name
comUssUmt.deptManageRegist.deptDc=Description

#Member Management#
comUssUmt.userManage.title=Member Management

comUssUmt.userManageSsearch.sbscrbSttusTitle=Selection to the Condition of Registration Status
comUssUmt.userManageSsearch.sbscrbSttusAll=Status(All)
comUssUmt.userManageSsearch.sbscrbSttusA=Sign Up
comUssUmt.userManageSsearch.sbscrbSttusD=Delete
comUssUmt.userManageSsearch.sbscrbSttusP=Approval
comUssUmt.userManageSsearch.searchConditioTitle=Select Condition of Inquiry
comUssUmt.userManageSsearch.searchConditionId=ID
comUssUmt.userManageSsearch.searchConditionName=Name

comUssUmt.userManageList.id=ID
comUssUmt.userManageList.name=User Name
comUssUmt.userManageList.email=User Email
comUssUmt.userManageList.phone=Phone Number
comUssUmt.userManageList.sbscrbSttus=Registration Status

comUssUmt.userManageRegist.id=User ID
comUssUmt.userManageRegist.name=Member User Name
comUssUmt.userManageRegist.pass=Password
comUssUmt.userManageRegist.passConfirm=Password Confirmation
comUssUmt.userManageRegist.passHit=Password Hint
comUssUmt.userManageRegist.passOk=Password Answer
comUssUmt.userManageRegist.saxTypeCode=Gender Identification Code
comUssUmt.userManageRegist.tel=Phone Number
comUssUmt.userManageRegist.fax=Fax Number
comUssUmt.userManageRegist.phone=Mobile Phone Number
comUssUmt.userManageRegist.email=Email
comUssUmt.userManageRegist.post=Postal Code
comUssUmt.userManageRegist.addr=Address
comUssUmt.userManageRegist.addrDetail=Detail Address
comUssUmt.userManageRegist.groupId=Group ID
comUssUmt.userManageRegist.status=User Status Code
comUssUmt.userManageRegistBtn.idSearch=Search Duplicate ID 

comUssUmt.userManageRegistModal.title=Confirm Duplicate ID
comUssUmt.userManageRegistModal.userIsId=Usable Id
comUssUmt.userManageRegistModal.initStatus=Result : Check for Duplicate ID.

comUssUmt.userManageRegistModal.result=Result
comUssUmt.userManageRegistModal.useMsg=This ID can not be used.
comUssUmt.userManageRegistModal.notUseMsg=This ID is available.
comUssUmt.userManageRegistModal.noIdMsg=This ID can not be created.

comUssUmt.userManageModifyBtn.passwordChange=Password Change

comUssUmt.userManagePasswordUpdt.title=Password Change
comUssUmt.userManagePasswordUpdt.id=User ID
comUssUmt.userManagePasswordUpdt.oldPass=Current Password
comUssUmt.userManagePasswordUpdt.pass=Password
comUssUmt.userManagePasswordUpdt.passConfirm=Password Confirmation


#User Management#
comUssUmt.deptUserManage.title=User Management

comUssUmt.deptUserManageRegist.id=User ID
comUssUmt.deptUserManageRegist.name=User Name
comUssUmt.deptUserManageRegist.pass=Password
comUssUmt.deptUserManageRegist.passConfirm=Password Confirmation
comUssUmt.deptUserManageRegist.passHit=Password Hint
comUssUmt.deptUserManageRegist.passOk=Password Answer
comUssUmt.deptUserManageRegist.saxTypeCode=Gender Identification Code
comUssUmt.deptUserManageRegist.tel=Phone Number
comUssUmt.deptUserManageRegist.fax=Fax Number
comUssUmt.deptUserManageRegist.phone=Mobile Phone Number
comUssUmt.deptUserManageRegist.email=Email
comUssUmt.deptUserManageRegist.post=Postal Code
comUssUmt.deptUserManageRegist.addr=Address
comUssUmt.deptUserManageRegist.addrDetail=Detail Address 
comUssUmt.deptUserManageRegist.groupId=Group ID
comUssUmt.deptUserManageRegist.status=User Status Code
comUssUmt.deptUserManageRegistBtn.idSearch=Search Duplicate ID 
comUssUmt.deptUserManageRegist.subDn=User DN

comUssUmt.deptUserManageRegist.insttCode=Organization Code
comUssUmt.deptUserManageRegist.orgnztId=Organization ID
comUssUmt.deptUserManageRegist.ofcps=Position
comUssUmt.deptUserManageRegist.emplNum=Employee Number
comUssUmt.deptUserManageRegist.brth=Birthday
comUssUmt.deptUserManageRegist.areaNo=Home Phone Number
comUssUmt.deptUserManageRegist.offmTelno=Office Phone Number
comUssUmt.deptUserManageRegist.fxNum=Fax Number
comUssUmt.deptUserManageRegist.emailAdres=Email
comUssUmt.deptUserManageRegistBtn.Search=Search

#Corporate Member Management#
comUssUmt.entrprsUserManage.title=Corporate Member Management
comUssUmt.entrprsUserManageList.id=Corporate Member ID
comUssUmt.entrprsUserManageList.company=Corporate Name
comUssUmt.entrprsUserManageList.name=User Name
comUssUmt.entrprsUserManageList.regName=Registration Name
comUssUmt.entrprsUserManageList.regMail=Registration Email
comUssUmt.entrprsUserManageList.email=Email
comUssUmt.entrprsUserManageList.phone=Phone Number
comUssUmt.entrprsUserManageList.sbscrbSttus=Registration Status

comUssUmt.entrprsUserManageRegist.id=Corporate Member ID
comUssUmt.entrprsUserManageRegist.name=Corporate Name
comUssUmt.entrprsUserManageRegist.pass=Password
comUssUmt.entrprsUserManageRegist.passConfirm=Password Confirmation
comUssUmt.entrprsUserManageRegist.passHit=Password Hint
comUssUmt.entrprsUserManageRegist.passOk=Password Answer
comUssUmt.entrprsUserManageRegist.tel=Corprate Phone Number
comUssUmt.entrprsUserManageRegist.fax=Fax Number
comUssUmt.entrprsUserManageRegist.post=Postal Code
comUssUmt.entrprsUserManageRegist.addr=Address
comUssUmt.entrprsUserManageRegist.addrDetail=Detail Address 
comUssUmt.entrprsUserManageRegist.groupId=Group ID
comUssUmt.entrprsUserManageRegist.status=Corporate Member Status Code
comUssUmt.entrprsUserManageRegist.subDn=Corporate Member DN

comUssUmt.entrprsUserManageRegist.indutyCode=Business Field
comUssUmt.entrprsUserManageRegist.entrprsSeCode=Corporate Category
comUssUmt.entrprsUserManageRegist.cxfc=CEO Name
comUssUmt.entrprsUserManageRegist.bizrno=Business Registration Number
comUssUmt.entrprsUserManageRegist.jurirno=Corporate Registration Number

comUssUmt.entrprsUserManageRegistBtn.idSearch=Search Duplicate ID
comUssUmt.entrprsUserManageRegistBtn.Search=Search

