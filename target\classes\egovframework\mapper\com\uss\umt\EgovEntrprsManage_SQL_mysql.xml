<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="entrprsManageDAO">

    <resultMap id="stplatMap" type="egovframework.com.uss.umt.service.StplatVO">
        <result property="useStplatId" column="USE_STPLAT_ID"/>
        <result property="useStplatCn" column="USE_STPLAT_CN"/>
        <result property="infoProvdAgeCn" column="INFO_PROVD_AGRE_CN"/>
    </resultMap>
       
    <insert id="insertEntrprs_S">
        
            INSERT INTO COMTNENTRPRSMBER 
                (   ESNTL_ID                      ,
                    ENTRPRS_MBER_ID               ,
                    ENTRPRS_SE_CODE              ,
                    BIZRNO                       ,
                    JURIRNO                      ,
                    CMPNY_NM                     ,
                    CXFC                         ,
                    ZIP                          ,
                    ADRES                        ,
                    ENTRPRS_MIDDLE_TELNO         ,
                    FXNUM                        ,
                    INDUTY_CODE                  ,
                    APPLCNT_NM                   ,
                    SBSCRB_DE                    ,
                    ENTRPRS_MBER_STTUS           ,
                    ENTRPRS_MBER_PASSWORD        ,
                    ENTRPRS_MBER_PASSWORD_HINT   ,
                    ENTRPRS_MBER_PASSWORD_CNSR   ,
                    GROUP_ID                     ,
                    DETAIL_ADRES                 ,
                    ENTRPRS_END_TELNO            ,
                    AREA_NO                      ,
                    APPLCNT_EMAIL_ADRES          ,
                    APPLCNT_IHIDNUM              ,
                    CHG_PWD_LAST_PNTTM			)
            VALUES (
                    #{uniqId},
                    #{entrprsmberId},
                    #{entrprsSeCode},
                    #{bizrno},
                    #{jurirno},
	                #{cmpnyNm},
	                #{cxfc},
	                #{zip},
	                #{adres},
	                #{entrprsMiddleTelno},
	                #{fxnum},
	                #{indutyCode},
	                #{applcntNm},
	                sysdate(),
	                #{entrprsMberSttus},
	                #{entrprsMberPassword},
	                #{entrprsMberPasswordHint},
	                #{entrprsMberPasswordCnsr},
	                #{groupId},
	                #{detailAdres},
	                #{entrprsEndTelno},
	                #{areaNo},
	                #{applcntEmailAdres},
	                #{applcntIhidnum},
	                sysdate()        )
        
    </insert>
    
    <delete id="deleteEntrprs_S">
        
            DELETE FROM COMTNENTRPRSMBER 
            WHERE ESNTL_ID=#{delId}
        
    </delete>
    
    <select id="selectEntrprs_S" resultType="egovframework.com.uss.umt.service.EntrprsManageVO">
        
            SELECT
                ESNTL_ID                      uniqId,
                'USR02'                      userTy,
                ENTRPRS_MBER_ID               entrprsmberId,
                ENTRPRS_SE_CODE              entrprsSeCode,
                BIZRNO                       bizrno,
                JURIRNO                      jurirno,
                CMPNY_NM                     cmpnyNm,
                CXFC                         cxfc,
                ZIP                          zip,
                ADRES                        adres,
                ENTRPRS_MIDDLE_TELNO         entrprsMiddleTelno,
                FXNUM                        fxnum,
                INDUTY_CODE                  indutyCode,
                APPLCNT_NM                   applcntNm,
                DATE_FORMAT(SBSCRB_DE,'%Y-%m-%d') AS sbscrbDe,
                ENTRPRS_MBER_STTUS           entrprsMberSttus,
                ENTRPRS_MBER_PASSWORD        entrprsMberPassword,
                ENTRPRS_MBER_PASSWORD_HINT   entrprsMberPasswordHint,
                ENTRPRS_MBER_PASSWORD_CNSR   entrprsMberPasswordCnsr,
                GROUP_ID                     groupId,
                DETAIL_ADRES                 detailAdres,
                ENTRPRS_END_TELNO            entrprsEndTelno,
                AREA_NO                      areaNo,
                APPLCNT_IHIDNUM              applcntIhidnum,
                APPLCNT_EMAIL_ADRES          applcntEmailAdres,
                LOCK_AT        				 lockAt
            FROM COMTNENTRPRSMBER
            WHERE ESNTL_ID=#{uniqId}
        
    </select>

    <update id="updateEntrprs_S">
        
            UPDATE COMTNENTRPRSMBER 
            SET 
                ENTRPRS_MBER_ID               = #{entrprsmberId},
                ENTRPRS_SE_CODE              = #{entrprsSeCode},
                BIZRNO                       = #{bizrno},
                JURIRNO                      = #{jurirno},
                CMPNY_NM                     = #{cmpnyNm},
                CXFC                         = #{cxfc},
                ZIP                          = #{zip},
                ADRES                        = #{adres},
                ENTRPRS_MIDDLE_TELNO         = #{entrprsMiddleTelno},
                FXNUM                        = #{fxnum},
                INDUTY_CODE                  = #{indutyCode},
                APPLCNT_NM                   = #{applcntNm},
                ENTRPRS_MBER_STTUS           = #{entrprsMberSttus},
                ENTRPRS_MBER_PASSWORD_HINT   = #{entrprsMberPasswordHint},
                ENTRPRS_MBER_PASSWORD_CNSR   = #{entrprsMberPasswordCnsr},
                GROUP_ID                     = #{groupId},
                DETAIL_ADRES                 = #{detailAdres},
                ENTRPRS_END_TELNO            = #{entrprsEndTelno},
                AREA_NO                      = #{areaNo},
                APPLCNT_EMAIL_ADRES          = #{applcntEmailAdres}
            WHERE ESNTL_ID=#{uniqId}
        
    </update>
    
    <select id="selectStplat_S" resultMap="stplatMap">
        
            SELECT
                USE_STPLAT_ID           ,
                USE_STPLAT_CN           ,
                INFO_PROVD_AGRE_CN      
            FROM COMTNSTPLATINFO
            WHERE USE_STPLAT_ID=#{stplatId}
        
    </select>
    
    <update id="updatePassword_S">
        
            UPDATE COMTNENTRPRSMBER 
            SET 
                   ENTRPRS_MBER_PASSWORD   =  #{entrprsMberPassword}
                 , CHG_PWD_LAST_PNTTM = sysdate()
            WHERE  ESNTL_ID  = #{uniqId}
        
    </update>
    
    <select id="selectPassword_S" resultType="egovframework.com.uss.umt.service.EntrprsManageVO">
        
            SELECT
                    ENTRPRS_MBER_PASSWORD          entrprsMberPassword 
            FROM    COMTNENTRPRSMBER
            WHERE   ESNTL_ID=#{uniqId}
        
    </select>
    
    <select id="selectEntrprsMberList" resultType="egovMap">
        SELECT 
            uniqId, userTy, userId , cmpnyNm, userNm, emailAdres, areaNo, middleTelno, endTelno, moblphonNo, groupId, sttus, sbscrbDe
        FROM(
            SELECT 
                ESNTL_ID              as uniqId,
                'USR02'               as userTy,
                ENTRPRS_MBER_ID       as userId,
                CMPNY_NM              as cmpnyNm,
                APPLCNT_NM            as userNm,
                APPLCNT_EMAIL_ADRES   as emailAdres,
                AREA_NO               as areaNo,
                ENTRPRS_MIDDLE_TELNO  as middleTelno,
                ENTRPRS_END_TELNO     as endTelno,
                ''                    as moblphonNo,
                GROUP_ID              as groupId,
                ENTRPRS_MBER_STTUS    as sttus,
                DATE_FORMAT(SBSCRB_DE,'%Y-%m-%d') as sbscrbDe
            FROM    COMTNENTRPRSMBER
            ) A
        WHERE 1=1
	     <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
          AND sttus LIKE  #{sbscrbSttus}
		</if> 
        <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
        <if test="searchCondition == 0">AND
            userId LIKE '%' #{searchKeyword} '%'
        </if>
        <if test="searchCondition == 1">AND
            userNm LIKE '%' #{searchKeyword} '%'
        </if>
        </if>
        
        ORDER BY sbscrbDe DESC
        LIMIT #{recordCountPerPage} OFFSET #{firstIndex}
    </select>
    
    
        <select id="selectEntrprsMberListTotCnt" resultType="int">
            SELECT COUNT(1) totcnt
            FROM(
	            SELECT 
	                ESNTL_ID               uniqId,
	                'USR02'               userTy,
	                ENTRPRS_MBER_ID        userId,
	                CMPNY_NM              cmpnyNm,
	                APPLCNT_NM            userNm,
	                APPLCNT_EMAIL_ADRES   emailAdres,
	                AREA_NO               areaNo,
	                ENTRPRS_MIDDLE_TELNO  middleTelno,
	                ENTRPRS_END_TELNO     endTelno,
	                ''                    moblphonNo,
	                GROUP_ID              groupId,
	                ENTRPRS_MBER_STTUS    sttus,
	                SBSCRB_DE             sbscrbDe
	            FROM    COMTNENTRPRSMBER
	            ) A
	        WHERE 1=1
	        <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
            AND sttus LIKE  #{sbscrbSttus}
            </if>
            <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
            <if test="searchCondition == 0">AND
                userId LIKE '%' #{searchKeyword} '%'
            </if>
            <if test="searchCondition == 1">AND
                userNm LIKE '%' #{searchKeyword} '%'
            </if>
            </if>
            
    </select>
    
    <update id="updateLockIncorrect">
            UPDATE COMTNENTRPRSMBER 
 			   SET  LOCK_AT = NULL 
			     ,  LOCK_CNT  = NULL 
			     ,  LOCK_LAST_PNTTM = NULL 
            WHERE  ESNTL_ID  = #{uniqId}
    </update>

</mapper>