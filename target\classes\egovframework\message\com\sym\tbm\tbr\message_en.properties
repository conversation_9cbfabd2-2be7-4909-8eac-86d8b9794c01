
comSymTbmTbr.troblReqstList.title=Trouble Application List
comSymTbmTbr.troblReqstList.pageTop.title=Trouble Application Management
comSymTbmTbr.troblReqstList.troblId=Trouble ID
comSymTbmTbr.troblReqstList.troblNm=Trouble Name
comSymTbmTbr.troblReqstList.troblKndNm=Trouble Type
comSymTbmTbr.troblReqstList.troblOccrrncTime=Trouble Time
comSymTbmTbr.troblReqstList.troblRqesterNm=Registrant
comSymTbmTbr.troblReqstList.processSttusNm=Processing Status
comSymTbmTbr.troblReqstList.selectAll=All


comSymTbmTbr.troblReqstDetail.title=Detailed View Of Trouble Application
comSymTbmTbr.troblReqstDetail.pageTop.title=Detailed View Of Trouble Application
comSymTbmTbr.troblReqstDetail.validate.=Can not edit.
comSymTbmTbr.troblReqstDetail.validate.=It can not be deleted.
comSymTbmTbr.troblReqstDetail.validate.=Are you sure you want to delete?
comSymTbmTbr.troblReqstDetail.validate.=This is not a request.
comSymTbmTbr.troblReqstDetail.validate.request=Would you like to request it?
comSymTbmTbr.troblReqstDetail.validate.=This request is not subject to cancellation.
comSymTbmTbr.troblReqstDetail.validate.=Are you sure you want to cancel the request?
comSymTbmTbr.troblReqstDetail.troblId=Trouble ID
comSymTbmTbr.troblReqstDetail.troblNm=Trouble Name
comSymTbmTbr.troblReqstDetail.troblKndNm=Trouble Type
comSymTbmTbr.troblReqstDetail.troblDc=Trouble Description
comSymTbmTbr.troblReqstDetail.troblOccrrncTime=Trouble Time
comSymTbmTbr.troblReqstDetail.troblRqesterNm=Trouble Registrant
comSymTbmTbr.troblReqstDetail.troblRequstTime=Trouble Processing Request Time
comSymTbmTbr.troblReqstDetail.processSttusNm=Processing Status
comSymTbmTbr.troblReqstDetail.troblProcessResult=Processing Result
comSymTbmTbr.troblReqstDetail.troblProcessTime=Processing Time
comSymTbmTbr.troblReqstDetail.troblOpetrNm=Failback Processor
comSymTbmTbr.troblReqstDetail.request=Request
comSymTbmTbr.troblReqstDetail.cancelRequest=Cancel request


comSymTbmTbr.troblReqstUpdt.title=Modify Trouble Application
comSymTbmTbr.troblReqstUpdt.pageTop.title=Modify Trouble Application
comSymTbmTbr.troblReqstUpdt.validate.info=Trouble time must be set before the current time.
comSymTbmTbr.troblReqstUpdt.validate.save=Do you want to save it?
comSymTbmTbr.troblReqstUpdt.validate.delete=Are you sure you want to delete?
comSymTbmTbr.troblReqstUpdt.troblId=Trouble ID
comSymTbmTbr.troblReqstUpdt.troblNm=Trouble Name
comSymTbmTbr.troblReqstUpdt.troblKnd=Trouble Type
comSymTbmTbr.troblReqstUpdt.troblDc=Trouble Description
comSymTbmTbr.troblReqstUpdt.troblOccrrncTime=Trouble Time
comSymTbmTbr.troblReqstUpdt.troblRqesterNm=Trouble Registrant
comSymTbmTbr.troblReqstUpdt.hour=Hour
comSymTbmTbr.troblReqstUpdt.minute=Minute
comSymTbmTbr.troblReqstUpdt.second=Second


comSymTbmTbr.troblReqstRegist.title=Modify Trouble Application
comSymTbmTbr.troblReqstRegist.pageTop.title=Modify Trouble Application
comSymTbmTbr.troblReqstRegist.validate.info=Trouble time must be set before the current time.
comSymTbmTbr.troblReqstRegist.validate.save=Do you want to save it?
comSymTbmTbr.troblReqstRegist.troblId=Trouble ID
comSymTbmTbr.troblReqstRegist.troblNm=Trouble Name
comSymTbmTbr.troblReqstRegist.troblKnd=Trouble Type
comSymTbmTbr.troblReqstRegist.troblDc=Trouble Description
comSymTbmTbr.troblReqstRegist.troblOccrrncTime=Trouble Time
comSymTbmTbr.troblReqstRegist.troblRqesterNm=Trouble Registrant
comSymTbmTbr.troblReqstRegist.hour=Hour
comSymTbmTbr.troblReqstRegist.minute=Minute
comSymTbmTbr.troblReqstRegist.second=Second
