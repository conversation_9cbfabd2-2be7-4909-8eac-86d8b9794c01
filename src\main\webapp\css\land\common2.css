@charset "utf-8";
/* 기본 스타일 */
*{margin:0;padding:0;box-sizing:border-box;-webkit-font-smoothing:antialiased;word-break: keep-all;}*:before,*:after{box-sizing:border-box;}
html{font-size-adjust:none;}
html,body {vertical-align:baseline;}
div,table {border-collapse:collapse;border-spacing:0;table-layout: fixed;}
body,table,td {word-wrap:break-word;word-break:break-all;}
p {color:#000;word-wrap:break-word;word-break:break-all; font-size: 25px; line-height: 0px; letter-spacing: 0px;}

/* 변수 정의 */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36a3f7;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-bg: #f8f9fc;
}

/* 기본 레이아웃 */
body {
    font-family: '<PERSON>o Sans KR', sans-serif;
    background-color: var(--light-bg);
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 네비게이션 바 */
.navbar {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    padding: 0.5rem 1rem;
    flex-shrink: 0;
}

.navbar-brand {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.25rem;
}

.nav-link {
    color: var(--secondary-color);
}

.nav-link:hover {
    color: var(--primary-color);
}

/* 메인 컨텐츠 영역 */
.main-content {
    flex: 1;
    display: flex;
    gap: 1rem;
    padding: 1rem;
    overflow: hidden;
}

/* 뷰어 섹션 */
.viewer-section {
    flex: 8.5;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    overflow: hidden;
}

.image-container {
    flex: 5;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid #e3e6f0;
    outline: none !important;
}

#viewer {
    width: 100%;
    height: 100%;
    outline: none !important;
}

/* 데이터 테이블 컨테이너 */
.data-table-container {
    flex: 5;
    padding: 5px;
    overflow: auto;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

/* 템플릿 컨테이너 */
#template-container {
    width: 100%;
    height: 100%;
    overflow: auto;
}

/* 초기 메시지 */
.initial-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
    color: #6c757d;
    font-size: 1.2rem;
}

.initial-message p {
    max-width: 80%;
    line-height: 1.6;
}

/* 로딩 오버레이 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-bg);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 사이드바 */
.sidebar {
    flex: 1.5;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #e3e6f0;
    display: flex;
    gap: 0.5rem;
}

.sidebar-header button {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: 5px;
    background: var(--light-bg);
    color: var(--secondary-color);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.sidebar-header button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.sidebar-header button.active {
    background: var(--primary-color);
    color: white;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.list-group-item {
    border: none;
    border-radius: 5px !important;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.list-group-item:hover {
    background-color: var(--light-bg);
}

.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 테두리 제거 스타일 - 영역별로 제한 */
.viewer-section .image-container, 
.viewer-section #viewer, 
.openseadragon-canvas, 
.openseadragon-canvas canvas,
.openseadragon-container, 
.openseadragon-container *,
.sidebar .list-group-item, 
.sidebar .list-group-item:focus, 
.sidebar .list-group-item:active, 
.sidebar .list-group-item:hover,
#pageList .list-group-item, 
#kwonList .list-group-item,
#pageList .list-group-item:focus, 
#pageList .list-group-item:active, 
#pageList .list-group-item:hover,
#kwonList .list-group-item:focus, 
#kwonList .list-group-item:active, 
#kwonList .list-group-item:hover,
#pageList .list-group-item.active, 
#kwonList .list-group-item.active {
    outline: none !important;
    -webkit-tap-highlight-color: transparent;
    -webkit-focus-ring-color: transparent;
    box-shadow: none !important;
    border-color: transparent !important;
}

/* 추가 테두리 제거 스타일 - 사이드바 영역으로 제한 */
#pageList .list-group-item, #kwonList .list-group-item {
    border: none !important;
}

#pageList .list-group-item.active, #kwonList .list-group-item.active {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

#pageList {overflow-y: auto;}

/* 포커스 제거를 위한 추가 클래스 - 사이드바 영역으로 제한 */
.sidebar .no-outline {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.sidebar .no-outline:focus, 
.sidebar .no-outline:active, 
.sidebar .no-outline:hover {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* 데이터 처리 영역에 대한 예외 처리 */
#template-container [contenteditable="true"]:focus,
#template-container [contenteditable="true"]:active {
    outline: auto !important;
    border-color: inherit !important;
    box-shadow: inherit !important;
}

/* 토지대장 관련 스타일 */
/* .select {width: 100%; font-size: 30px; letter-spacing: 3px; line-height: 36.5px; padding-top: 2px;}
.ground {display: block; height: 100%; width: 100%; border: 1px solid #ddd; padding: 20px; margin: 0 auto;}
.bor-table {border: 4px solid #000;}
.bor-table .data-table {height: 100%; border-bottom: 1px solid #000;}
.data-table table tbody td{position: relative;border-right:1px solid #000;border-bottom:1px solid #000;color:#000;font-size: 32px;padding: 0px;text-align: center;writing-mode: vertical-rl;
    text-orientation: upright; vertical-align: top; line-height: 30px;}
.data-table table tbody td.index {border-right: 0;}    

.left-data {float: left; height: 100%; width: 49%;}
.right-data {float: left; height: 100%; width: 49%;}
.jh-data {height: 100%; width: 100%;}
.o-data {height: 100%; width: 100%; }
.title {position: relative; float: left; height: 100%; width: 1%; font: bold; border-right: 1px solid #000; writing-mode: vertical-rl; text-align: center;}
.subtitle {float: left; height: 100%; width: 106px;}
.index-data {height: 100%; width: 100%;}
.index-data tbody tr td {border-right: 0px;}
.warn {height: 55px; color: red; text-align: left; font-size: 2em; line-height: 56px; padding-left: 10px;}
.horizontal-tb{writing-mode: horizontal-tb !important; vertical-align : top !important}
.vertical-rl{writing-mode: vertical-rl !important; padding-left: 15px;}

.check-sasun{position: absolute;left:0px;top:0px; width: 30px;height:30px;} */
/* .title:before,
.title::after{content: '';position: absolute;width: 100px;height: 8px;background-color: #000;left: 0px;}
.title:before{top: 335px;}
.title::after{top: 355px;} */
/* 
.ground table tbody td.redLine::before,
.ground table tbody td.redLine::after{content: '';position: absolute;width: 2px;display: block;background-color: red;z-index: 1;transform: rotate(40deg);}
.ground table tbody td.redLine::before{height: 340px;top:-42px;left: 107px;}
.ground table tbody td.redLine::after{height: 340px;top:-35px;left: 119px;}

.ground table tbody td.redLine2::before,
.ground table tbody td.redLine2::after{content: '';position: absolute;width: 2px;display: block;background-color: red;z-index: 1;transform: rotate(40deg);}
.ground table tbody td.redLine2::before{height: 345px;top:-41px;left: 108px;}
.ground table tbody td.redLine2::after{height: 356px;top:-32px;left: 114px;}

p.cancel {text-decoration: line-through red;}
span.cancel {content: '';position: relative;display: block;background-color: red;width: 3px;height: 164px;z-index: 1;left: 31px;top: 7px;}

.ground table tbody td p.unit{display: block; position: absolute; text-align: right; width: 100px; height: 35px; right: 0px; bottom:5px; padding: 0;} */
/* .title p {font-size: 65px;padding-right: 7px;padding-bottom: 600px; letter-spacing: 100px; line-height: 85px;}
.title p.mark {position: absolute; writing-mode: initial; text-align: center; width: 2450px; font-size: 525px; left: -925px; top: 885px; transform: rotate(-35deg); color: gray; opacity: 20%; letter-spacing: 200px; font-family: serif; font-weight: bold;pointer-events: none;} */
/* .subtitle p {font-size: 35px; }
.index-data p {font-size: 35px; }

tbody tr.jimok td {height: 115px; position: relative; writing-mode: unset;}
.jijuk {height: 150px;}
tbody tr.jijuk td {height: inherit; writing-mode: unset;}
.grade1 {height: 170px;}
tbody tr.grade1 td {height: inherit; position: relative; writing-mode: unset; border-bottom: 1px dashed #000;}
.grade2 {height: 115px;}
tbody tr.grade2 td {height: inherit; writing-mode: unset;}
.hist {height: 670px;}
tbody tr.hist td {height: inherit; vertical-align: top; padding-right: 5px;}
tr.hist td p {text-align: left;}

.date {height: 175px;}
tbody tr.date td {height: inherit; position: relative;}
tbody tr.date td p.year {letter-spacing: 5px; font-size: 30px; padding: 2px 0 0 8px; letter-spacing: 1px;}
tbody tr.date td p.md {writing-mode: initial; font-size: 30px; padding-top: 10px; line-height: 30.5px; letter-spacing: -2px;}
.reason {height: 180px;}
tbody tr.reason td {height: inherit; padding-right: 5px;}
tr.reason td p {text-align: center; margin-right: -2px; margin-top: -2px;}
.addr {height: 325px;}
tbody tr.addr td {height: inherit; padding-right: 5px;}
tr.addr td p {text-align: left;}
.name {height: 250px;}
tbody tr.name td {height: inherit; border-bottom: 0px; padding-right: 5px;}
tr.name td p {text-align: left;}

p.jimeung {letter-spacing: 15px;}

p.h_f32 {font-size: 32px; letter-spacing: 3px; line-height: 36.5px; padding-top: 2px;}
p.h_f28 {font-size: 28px; letter-spacing: 5px; line-height: 31.5px; padding-top: 4px;}
p.h_f23 {font-size: 23px; letter-spacing: 3px; line-height: 27.5px; padding-top: 4px;}

p.r_f32 {font-size: 32px; letter-spacing: 0px; line-height: 38px;}
p.r_f27 {font-size: 27px; letter-spacing: 0px; line-height: 30px;}

p.a_f32 {font-size: 32px; letter-spacing: 1px; line-height: 36px; padding-top: 4px;}
p.a_f22 {font-size: 22px; letter-spacing: 1px; line-height: 29px; padding-top: 4px;}
p.a_f18 {font-size: 18px; letter-spacing: 2px; line-height: 22px; padding-top: 5px;}

p.n_f32 {font-size: 32px; letter-spacing: 1px; line-height: 38px; padding-top: 4px;}
p.n_f27 {font-size: 27px; letter-spacing: 2px; line-height: 32px; padding-top: 3px; margin-right: -5px;}

p.j_f30 {text-align: left; font-size: 30px; line-height: 32px; letter-spacing: 9px; padding-top: 10px; padding-left: 4px; padding-bottom: 20px;}

.jimeung p {font-size: 40px; letter-spacing: 10px; padding-top: 10px;}
.jibun p {font-size: 33px; letter-spacing: 2px; padding-top: 10px;}
.grade p {letter-spacing: 10px; padding-top: 10px;}
.jukyo p {letter-spacing: 10px; text-align: left; padding-top: 15px; line-height: 33px; padding-right: 2px;} */

.data-table {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;
  }
  
  /* 좌우 영역 */
  .left-data,
  .right-data {
    overflow: hidden; /* 내부 콘텐츠에 대해 스크롤 처리 */
    display: flex;
    height: 100%;
    flex-direction: column;
  }
  
  table.data {
    height: 100%;
    table-layout: fixed;
    box-sizing: border-box;
}
  
  /* 중간 구분선 */
  #title {
    width: 40px;
    background-color: #ccc;
    height: 100%;
  }

  .index-data {width: 15%; height: 100%; text-align: center;}
  .index-data tbody td {border: 1px solid #000; border-collapse: collapse; box-sizing: border-box; padding: 2px; overflow: hidden;} 
  .index-data tbody td > div[contenteditable="true"] {
    width: 100%;
    height: 100%;
    max-height: 100%;
    white-space: pre-wrap;      /* 줄바꿈 허용 */
    word-break: break-word;     /* 단어 단위 줄바꿈 */
    box-sizing: border-box;
    overflow: auto;
  }

  /* tr {height: 100%} */
  .data thead th {height: 100%; text-align: center; vertical-align: middle; padding: 1px; border: 0px;}
  .data tbody td {border: 1px solid #000; border-collapse: collapse; box-sizing: border-box; padding: 2px;}  
  .data tbody td > div[contenteditable="true"] {
    width: 100%;
    height: 100%;
    max-height: 100%;
    white-space: pre-wrap;      /* 줄바꿈 허용 */
    word-break: break-word;     /* 단어 단위 줄바꿈 */
    box-sizing: border-box;
    overflow: auto;
  }
  
  /* 서식지정 500px -> 100% */
  tr.jimok td {height: 18%;position: relative; text-align: center; vertical-align: middle;}
  tr.jijuk td {height: 30%; position: relative; text-align: center; vertical-align: middle;}
  tr.grade1 td {height: 30%; position: relative; text-align: center; vertical-align: middle;}
  tr.grade2 td {height: 18%; text-align: center; vertical-align: middle;}
    tr.jijuk td .jijukUnit, 
    tr.grade1 td .gradeUnit {position: absolute; bottom: 3px; right: 3px; width: 40px !important; height: 20px !important; text-align: right; overflow: hidden !important;}
    input.jimokLine, 
    input.gradeLine {position: absolute; top: 0px; left: 0px; width: 20px; height: 20px;}
    tr td.slash::before,
    tr td.slash::after{content: '';position: absolute;width: 2px;display: block;background-color: red;z-index: 1;transform: rotate(31deg);}
    tr td.slash::before{height: 223px; top:-16px;left: 56px;}
    tr td.slash::after{height: 216px; top: -3px; left: 64px;}
    tr td.slash2::before,
    tr td.slash2::after{content: '';position: absolute;width: 2px;display: block;background-color: red;z-index: 1;transform: rotate(30deg);}
    tr td.slash2::before{height: 220px;top:-15px;left: 53px;}
    tr td.slash2::after{height: 214px;top:-2px;left: 61px;}
  
  tr.hist td {height: 40%; writing-mode: vertical-rl; text-align: left; vertical-align: top; text-orientation: upright;}
    tr.hist td div {overflow: auto;}
  
  tr.date td {height: 20%; position:relative; text-align: center; vertical-align: middle; }
    tr.date td div[id^="year_"] {position: absolute; top: 0; left: 0; height: 20%; width: 100%; overflow: hidden;}
    tr.date td div[id^="date_"] {position: absolute; top: 20%; left: 0; height: 80%; width: 100%; writing-mode: vertical-rl; text-orientation: upright; align-content: center; overflow: hidden;}    
    tr.date td div[id^="year_d_"] {visibility: hidden;}
    tr.date td div[id^="date_d_"] {visibility: hidden;} 
  tr.reason td {height: 26%; text-align: center; vertical-align: middle; align-content: center;}
  /* tr.percent td {height: 15%; writing-mode: vertical-rl; text-align: center; vertical-align: middle;} */
  tr.addr td {height: 14%; writing-mode: vertical-rl; text-align: center; vertical-align: top; text-orientation: upright; align-content: center;}
  
  tr.name td {height: 40%; writing-mode: vertical-rl; text-align: center; vertical-align: middle;}

  tr.jimeung td {height: 50%; writing-mode: vertical-rl; text-align: center; vertical-align: middle;}
  tr.jibun td {height: 50%; writing-mode: vertical-rl; text-align: center; vertical-align: middle;}
  tr.grade td {height: 50%; writing-mode: vertical-rl; text-align: center; vertical-align: middle;}
  tr.jukyo td {height: 50%; writing-mode: vertical-rl; text-align: center; vertical-align: middle;}

  span.cancel {content: '';position: relative;display: block;background-color: red;width: 3px;height: 164px;z-index: 1;left: 31px;top: 7px;}
  
#template-container .data-table {
    display: flex;
    flex-direction: row;
    /* align-items: stretch; */
    height: 100%;
    width: 100%;
}

#template-container .left-data {
    flex: 1.05;
    float: none;
}

#template-container .right-data {
    flex: 0.95;
    float: none;
}

#template-container .data-table::after {
    content: "";
    display: table;
    clear: both;
}

/* 편집 가능한 요소 스타일 */
#template-container [contenteditable="true"] {
    cursor: text;
}

#template-container [contenteditable="true"]:focus {
    outline: 1px solid #4e73df;
}
