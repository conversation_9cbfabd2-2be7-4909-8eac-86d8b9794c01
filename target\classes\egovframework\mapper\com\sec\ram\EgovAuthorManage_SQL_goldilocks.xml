<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="authorManageDAO">

    <resultMap id="author" type="egovframework.com.sec.ram.service.AuthorManageVO">
        <result property="authorCode" column="AUTHOR_CODE"/>
        <result property="authorNm" column="AUTHOR_NM"/>
        <result property="authorDc" column="AUTHOR_DC"/>
        <result property="authorCreatDe" column="AUTHOR_CREAT_DE"/>
    </resultMap>

    <select id="selectAuthorList" parameterType="egovframework.com.sec.ram.service.AuthorManageVO" resultMap="author">
        
            SELECT  * 
              FROM  (
            SELECT ROWNUM RNUM, ALL_LIST.* 
              FROM  (

            SELECT
                   AUTHOR_CODE, AUTHOR_NM, AUTHOR_DC, AUTHOR_CREAT_DE
              FROM COMTNAUTHORINFO
             WHERE 1=1
                      
            <if test="searchCondition == 1">AND
                AUTHOR_NM LIKE '%'||#{searchKeyword}||'%'
            </if>
           <![CDATA[
            ORDER BY AUTHOR_CREAT_DE DESC

                    ) ALL_LIST
                    )
             WHERE  RNUM  > #{firstIndex}
               AND  RNUM <= #{firstIndex} + #{recordCountPerPage}
             ]]>            
    </select>

    <insert id="insertAuthor" parameterType="egovframework.com.sec.ram.service.AuthorManage">
        
            INSERT INTO COMTNAUTHORINFO 
                  ( AUTHOR_CODE
                  , AUTHOR_NM
                  , AUTHOR_DC
                  , AUTHOR_CREAT_DE )
           VALUES ( #{authorCode}
                  , #{authorNm}
                  , #{authorDc}
                  , SYSDATE)
          
    </insert>

    <update id="updateAuthor" parameterType="egovframework.com.sec.ram.service.AuthorManage">
        
            UPDATE COMTNAUTHORINFO 
               SET AUTHOR_NM=#{authorNm}
                 , AUTHOR_DC=#{authorDc}
             WHERE AUTHOR_CODE=#{authorCode}
        
    </update>
    
    <delete id="deleteAuthor">
        
            DELETE FROM COMTNAUTHORINFO 
             WHERE AUTHOR_CODE=#{authorCode}
        
    </delete>
    
    <select id="selectAuthor" resultMap="author">
        
            SELECT AUTHOR_CODE, AUTHOR_NM, AUTHOR_DC, AUTHOR_CREAT_DE
              FROM COMTNAUTHORINFO 
             WHERE AUTHOR_CODE=#{authorCode}
        
    </select>
    
    <select id="selectAuthorListTotCnt" parameterType="egovframework.com.sec.ram.service.AuthorManageVO" resultType="int">
            SELECT COUNT(*) totcnt
            FROM COMTNAUTHORINFO
            WHERE 1=1
            <if test="searchCondition == 1">AND
                AUTHOR_NM LIKE '%'||#{searchKeyword}||'%'
            </if>
    </select>
    
    <select id="selectAuthorAllList" parameterType="egovframework.com.sec.ram.service.AuthorManageVO" resultMap="author">
            SELECT
                   AUTHOR_CODE, AUTHOR_NM, AUTHOR_DC, AUTHOR_CREAT_DE
              FROM COMTNAUTHORINFO
    </select>
</mapper>