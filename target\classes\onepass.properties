## Site info ##

############################################################ CHANGE INFO ################################################################################
# Site URL : \uc0ac\uc774\ud2b8\uc758 URL
onepass.site.url=https://127.0.0.1:9443/egovframework-all-in-one/

# Site ID : \uc0ac\uc774\ud2b8\uc758 ID(\ub514\uc9c0\ud138\uc6d0\ud328\uc2a4\uc5d0\uc11c \ubc1c\uae09)
onepass.site.id=****

# AES secretKey(length:16) : AES \uc554\ud638\ud654 \ud0a4(\ub514\uc9c0\ud138\uc6d0\ud328\uc2a4\uc5d0\uc11c \ubc1c\uae09)
aes.secretKey=****************

# PublicKey path(\uc808\ub300\uacbd\ub85c)
keys.public.path=/Users/<USER>/eGovFrameDev-4.2.0/workspace/egovframework-all-in-one-AllNew/src/main/resources/egovframework/onepass/pubkey_share.der

# SSL jks path(\uc0c1\ub300\uacbd\ub85c)
ssl.keystore=egovframework/onepass/onepass.jks

# ACS path : SAML\ub85c\uadf8\uc778 \uc694\uccad \ud6c4 \uc751\ub2f5\ucc98\ub9ac \uacbd\ub85c(\uc0ac\uc774\ud2b8\uc758 \ub85c\uadf8\uc778 \ucc98\ub9ac\ub97c \uc704\ud55c \uacbd\ub85c)
onepass.site.path.acs=/uat/uia/onepass/onepassCallback.do

# IDP URL : \ub514\uc9c0\ud138\uc6d0\ud328\uc2a4 IDP URL
onepass.idp.url=https://saml.egaf2017.com

# Potal URL : API \uc11c\ubc84 URL
onepass.potal.url=https://www.egaf2017.com

# AP URL : AP \uc11c\ubc84 URL(\uac1c\ubc1c:**************, \uc6b4\uc601:ap.onepass.go.kr)
onepass.socket.host=**************
onepass.socket.port=53010


############################################################ CHANGE INFO ################################################################################

onepass.cookie.path=/egovframework-all-in-one

# PublicKey algorithm : 
keys.algorithm=RSA
keys.rsa.instance=RSA/ECB/PKCS1Padding

## SAML 2.0 ##
# Login : \ub514\uc9c0\ud138\uc6d0\ud328\uc2a4 \ub85c\uadf8\uc778 URL
onepass.idp.path.login=/login/check
# Logout : \ub514\uc9c0\ud138\uc6d0\ud328\uc2a4 \ub85c\uadf8\uc544\uc6c3 URL(\ub514\uc9c0\ud138\uc6d0\ud328\uc2a4 \uc138\uc158 \ud568\uaed8 \ub85c\uadf8\uc544\uc6c3)
onepass.idp.path.logout.site=/logout/site
# Logout : \ub514\uc9c0\ud138\uc6d0\ud328\uc2a4 \ub85c\uadf8\uc544\uc6c3 URL(\uc694\uccad \uc0ac\uc774\ud2b8\ub9cc \ub85c\uadf8\uc544\uc6c3 \ucc98\ub9ac, \ub514\uc9c0\ud138\uc6d0\ud328\uc2a4 \uc138\uc158 \uc720\uc9c0)
onepass.idp.path.logout.onepass=/logout/onepass
# IDP response attribute : \uc778\uc99d\ub808\ubca8 \ucf54\ub4dc
onepass.idp.attr.auth.level=AUTH_LEVEL
# IDP response attribute : \uc778\uc99d\uc218\ub2e8 \ucf54\ub4dc
onepass.idp.attr.crt.code=CRT_CODE
# IDP response attribute : API \uc5f0\ub3d9 \ud1a0\ud070
onepass.idp.attr.intf.token=INTF_TOKEN
# IDP response attribute : \ud68c\uc6d0\uc815\ubcf4 \uc218\uc815 \uc815\ud569\uc131 \ucf54\ub4dc
onepass.idp.attr.find.user=FIND_USER
# \uc694\uccad\uc804\ubb38 \uc720\ud6a8\uc2dc\uac04(\ubd84)
onepass.idp.valid.time.min=2

## API ##
# find user api path : \uc0ac\uc6a9\uc790 \uc815\ubcf4 \uc870\ud68c API \uacbd\ub85c
onepass.intf.user.path=/api/user
# session check : api \uc138\uc158 \uccb4\ud06c
onepass.idp.path.check=/api/check
# \uc5f0\ub3d9\ud574\uc81c \uc694\uccad url
onepass.interlock.release.path=/api/interlockRelease

# SSL context
ssl.context=TLS
ssl.verify.yn=Y

#xml parser
onepass.xml.parser=nano