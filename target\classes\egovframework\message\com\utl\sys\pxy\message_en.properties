# Proxy service #
comUtlSysPxy.proxyLog.title = Proxy log
comUtlSysPxy.proxySvc.title = Proxy Settings

comUtlSysPxy.proxySvc.proxyId.label = Proxy ID
comUtlSysPxy.proxySvc.proxyNm.label = proxy name
comUtlSysPxy.proxySvc.proxyIp.label = Proxy IP
comUtlSysPxy.proxySvc.proxyPort.label = Proxy Port
comUtlSysPxy.proxySvc.trgetSvcNm.label = target service name
comUtlSysPxy.proxySvc.svcDc.label = Service Description
comUtlSysPxy.proxySvc.svcIp.label = Service IP
comUtlSysPxy.proxySvc.svcPort.label = Service Port
comUtlSysPxy.proxySvc.svcSttusNm.label = Service Status

comUtlSysPxy.proxySvcList.proxyId.label = Proxy ID
comUtlSysPxy.proxySvcList.proxyNm.label = proxy name
comUtlSysPxy.proxySvcList.proxyPort.label = Proxy Port
comUtlSysPxy.proxySvcList.trgetSvcNm.label = target service name
comUtlSysPxy.proxySvcList.svcIp.label = Service IP/Port
comUtlSysPxy.proxySvcList.svcSttusNm.label = Status
comUtlSysPxy.proxySvcList.checknum = Date format is invalid
comUtlSysPxy.proxySvcList.strTmpFromDateEndDate = The start date can not be greater than the end date
comUtlSysPxy.proxySvcList.pmProxyLogVO.strStartDate = Proxy log start date
comUtlSysPxy.proxySvcList.pmProxyLogVO.strEndDate = Proxy log end date

comUtlSysPxy.proxyLog.period.label = duration
comUtlSysPxy.proxyLog.proxyId.label = Proxy ID
comUtlSysPxy.proxyLog.proxyNm.label = Target Service
comUtlSysPxy.proxyLog.clntPort.label = Proxy Port
comUtlSysPxy.proxyLog.clntIp.label = Client IP
comUtlSysPxy.proxyLog.conectTime.label =connected time
comUtlSysPxy.proxyLog.log = Log

comUtlSysPxy.proxySvcDetail.title=Proxy Detail View
comUtlSysPxy.proxySvcDetail.summary = Provides detailed information about proxy settings.

comUtlSysPxy.proxySvcUpdtl.title=Change Proxy Settings
comUtlSysPxy.proxySvcUpdt.summary = Modify the proxy configuration information.
comUtlSysPxy.proxySvcUpdt.ipValueException = is the exception ip.
comUtlSysPxy.proxySvcUpdt.ipFormalException = IP format mismatch.
comUtlSysPxy.proxySvcUpdt.svcSttus.run = Normal
comUtlSysPxy.proxySvcUpdt.svcSttus.stop = Stop

comUtlSysPxy.proxySvcRegist.ipValueException = is the exception ip.
comUtlSysPxy.proxySvcRegist.ipFormalException = IP format mismatch.
comUtlSysPxy.proxySvcRegist.svcSttus.run = Normal
comUtlSysPxy.proxySvcRegist.svcSttus.stop = Stop