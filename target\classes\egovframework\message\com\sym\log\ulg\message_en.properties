#User Log Management#
comSymLogUlg.userLog.title = User Log

comSymLogUlg.userLog.occrrncDe = Occurence Day
comSymLogUlg.seachWrd.searchBgnDe = Search Begin Date
comSymLogUlg.seachWrd.searchEndDe = Serach End Date
comSymLogUlg.userLog.user = User
comSymLogUlg.userLog.methodNm = Method Name
comSymLogUlg.userLog.serviceNm = Service Name
comSymLogUlg.userLog.create =  Create
comSymLogUlg.userLog.modify = Modify
comSymLogUlg.userLog.view = View
comSymLogUlg.userLog.delete = Delete
comSymLogUlg.userLog.detail = Detail

comSymLogUlg.validate.dateCheck = The start date of the search condition is later than the end date. Please check the search condition date!




