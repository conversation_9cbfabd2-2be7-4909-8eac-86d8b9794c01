#UnityLink Management#
uss.ion.ulm.unityLinkList.unityLinkList=Unity Link Management List
uss.ion.ulm.unityLinkList.unityLinkNum=num.
uss.ion.ulm.unityLinkList.unityLinkGroup=Group
uss.ion.ulm.unityLinkList.unityLinkNm=Unity Link Name
uss.ion.ulm.unityLinkList.unityLinkDc=Unity Link Description
uss.ion.ulm.unityLinkList.unityLinkUrl=Unity Link URL
uss.ion.ulm.unityLinkList.unityLinkRegNm=registrant
uss.ion.ulm.unityLinkList.unityLinkRegDate=Registration date

uss.ion.ulm.unityLinkDetail.unityLinkDetail=Unity Link Management Detail
uss.ion.ulm.unityLinkDetail.unityLinkGroup=Unity Link Group
uss.ion.ulm.unityLinkDetail.unityLinkNm=Unity Link Name
uss.ion.ulm.unityLinkDetail.unityLinkDc=Unity Link Description
uss.ion.ulm.unityLinkDetail.unityLinkUrl=Unity Link URL
uss.ion.ulm.unityLinkDetail.validate.deleteMsg=Will you delete it?

uss.ion.ulm.unityLinkRegist.unityLinkRegist=Unity Link Management Regist
uss.ion.ulm.unityLinkRegist.unityLinkGroup=Unity Link Group
uss.ion.ulm.unityLinkRegist.unityLinkNm=Unity Link Name
uss.ion.ulm.unityLinkRegist.unityLinkDc=Unity Link Description
uss.ion.ulm.unityLinkRegist.unityLinkUrl=Unity Link URL

uss.ion.ulm.unityLinkUpdt.unityLinkUpdate=Unity Link Management Update
uss.ion.ulm.unityLinkUpdt.unityLinkGroup=Unity Link Group
uss.ion.ulm.unityLinkUpdt.unityLinkNm=Unity Link Name
uss.ion.ulm.unityLinkUpdt.unityLinkDc=Unity Link Description
uss.ion.ulm.unityLinkUpdt.unityLinkUrl=Unity Link URL

uss.ion.ulm.unityLinkSample.unityLink=Unity Link
uss.ion.ulm.unityLinkSample.goUnityLinkSample=Move the integrated link sample page
uss.ion.ulm.unityLinkSample.validate.select=Please select Unity Link!
uss.ion.ulm.unityLinkSample.moveButton=Move