# Departmental Workplace Management
# EgovDeptJobBxList.jsp
comCopSmtDjm.deptJobBxList.title = View your departmental workbook management list
comCopSmtDjm.deptJobBxList.indictOrdrChanged = You can change the order of departments within the same department name.
comCopSmtDjm.deptJobBxList.searchCnd = You have not selected any query criteria.
comCopSmtDjm.deptJobBxList.ordrCnd = Please select the department you want to edit the order in.
comCopSmtDjm.deptJobBxList.deptNm = Department name
comCopSmtDjm.deptJobBxList.deptJobNm = Departmental job description
comCopSmtDjm.deptJobBxList.deptJobBxManage = Departmental Workbox Management
comCopSmtDjm.deptJobBxList.finalEditor = final sperm
comCopSmtDjm.deptJobBxList.finalEditDate = Last Modified Date

# EgovDeptJobRegist.jsp
comCopSmtDjm.deptJobRegist.title = Departmental Worker Registration
comCopSmtDjm.deptJobRegist.deptJobBxNmDuplicated = There is a duplicate department job in the department.
comCopSmtDjm.deptJobRegist.intIndictOrdr = You must enter a value of 1 or greater in the display order.
comCopSmtDjm.deptJobRegist.deptIndictOrdr = The maximum value of the departmental display order for this department is
Since comCopSmtDjm.deptJobRegist.deptIndictOrdrCause =
comCopSmtDjm.deptJobRegist.deptIndictOrdrInput = You must enter below.
comCopSmtDjm.deptJobRegist.deptNm = Department
comCopSmtDjm.deptJobRegist.deptJobBxNm = Departmental job description
comCopSmtDjm.deptJobRegist.indictOrdr = Display Order

# EgovDeptListPopup.jsp
comCopSmtDjm.deptListPopup.title = Department List

# EgovDeptList.jsp
comCopSmtDjm.deptList.title = View department list
comCopSmtDjm.deptList.deptSelect = Select department
comCopSmtDjm.deptList.deptExplain = Department description

# EgovDeptJobBxUpdt.jsp
comCopSmtDjm.deptJobBxUpdt.title = Departmental work order

#EgovDeptJobList.jsp
comCopSmtDjm.deptJobList.title = Department List
comCopSmtDjm.deptJobList.subject = Title
comCopSmtDjm.deptJobList.content = Content
comCopSmtDjm.deptJobList.charger = Contact
comCopSmtDjm.deptJobList.summary = Provides a list of departmental workspaces.
comCopSmtDjm.deptJobList.writerDate = Date created

# EgovDeptJob.xml
comCopSmtDjm.deptJobVO.validate.deptNm = Department
comCopSmtDjm.deptJobVO.validate.deptJobBxNm = Departmental job description
comCopSmtDjm.deptJobVO.validate.deptJobNm = Department job name
comCopSmtDjm.deptJobVO.validate.deptJobCn = Departmental business content
comCopSmtDjm.deptJobVO.validate.chargerNm = Business contact
comCopSmtDjm.deptJobVO.validate.priort = Priority

# EgovDeptJobRegist.jsp
comCopSmtDjm.deptJobRegist.title = Departmental business registration
comCopSmtDjm.deptJobRegist.high = High
comCopSmtDjm.deptJobRegist.medium = Medium
comCopSmtDjm.deptJobRegist.low = Low
comCopSmtDjm.deptJobRegist.fileAttach = Attach file

#EgovDeptJobBxListPopup.jsp
comCopSmtDjm.deptJobBxListPopup.title=Department Workbox List

# EgovDeptJobBxList.jsp
comCopSmtDjm.deptJobBxList.title = Departmental Worklist
comCopSmtDjm.deptJobBxList.userNm = username

# EgovChargerList.jsp
comCopSmtDjm.chargerList.title = View contact list
comCopSmtDjm.chargerList.chargerSelect = Select Contact
comCopSmtDjm.chargerList.deptNm = Department name
comCopSmtDjm.chargerList.emplyrNm = Employee name
comCopSmtDjm.chargerList.ofcpsNm = Position
comCopSmtDjm.chargerList.emplNo = employer Number

#EgovDeptJobDetail.jsp
comCopSmtDjm.deptJobDetail.title=View details of departmental work

# EgovDeptJobUpdt.jsp
comCopSmtDjm.deptJobUpdt.title = Modify Department Jobs