#RoughMap
#EgovRoughMapList.jsp
comUssIonRmm.roughMapList.title=List of Directions
comUssIonRmm.roughMapList.roughMapSj=Directions Title
comUssIonRmm.roughMapList.roughMapAddress=Directions address
comUssIonRmm.roughMapList.roughMapNo=NO
comUssIonRmm.roughMapList.roughMapDetailAddress=Details Address
comUssIonRmm.roughMapList.lastUpdtPnttm=Registration Date
comUssIonRmm.roughMapList.info=Map management requires a Daum Maps API key to be issued.
comUssIonRmm.roughMapList.info2=Daum Maps Web API Guide

#EgovRoughMapInfoRegist.jsp
comUssIonRmm.roughMapInfoRegist.title=Register Rough Map
comUssIonRmm.roughMapInfoRegist.validate.error=Error occurrence, error code
comUssIonRmm.roughMapInfoRegist.validate.msg=Message
comUssIonRmm.roughMapInfoRegist.validate.noAddress=No address available.
comUssIonRmm.roughMapInfoRegist.validate.searchAddress=Please search for the address.
comUssIonRmm.roughMapInfoRegist.express=Show directions
comUssIonRmm.roughMapInfoRegist.input=Enter Directions

#EgovRoughMapInfoDetail.jsp
comUssIonRmm.roughMapInfoDetail.title=Detail Rough Map
comUssIonRmm.roughMapInfoDetail.findRoughMap=Get Directions
comUssIonRmm.roughMapInfoDetail.magnifyRoughMap=View Lager

#EgovRoughMapUpdt.jsp
comUssIonRmm.roughMapUpdt.title=Edit Rough Map