#-----------------------------------------------------------------------
#
#   server.properties : \uc124\uce58\ub41c \uc11c\ubc84\uc758 \uc815\ubcf4\ub97c \uae30\ub85d\ud558\ub294 Config \ud30c\uc77c
#   
#-----------------------------------------------------------------------
#   1.  key = value \uad6c\uc870\uc785\ub2c8\ub2e4.
#   2.  key\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \ud3ec\ud568\ubd88\uac00, value\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \uac00\ub2a5
#   3.  key\uac12\uc73c\ub85c \ud55c\uae00\uc744 \uc0ac\uc6a9\ubd88\uac00,   value\uac12\uc740 \ud55c\uae00\uc0ac\uc6a9\uc774 \uac00\ub2a5
#   4.  \uc904\uc744 \ubc14\uafc0 \ud544\uc694\uac00 \uc788\uc73c\uba74 '\'\ub97c \ub77c\uc778\uc758 \ub05d\uc5d0 \ucd94\uac00(\ub9cc\uc57d  '\'\ubb38\uc790\ub97c \uc0ac\uc6a9\ud574\uc57c \ud558\ub294 \uacbd\uc6b0\ub294 '\\'\ub97c \uc0ac\uc6a9)
#   5.  Windows\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '\\' or '/'  ('\' \uc0ac\uc6a9\ud558\uba74 \uc548\ub428)
#   6.  Unix\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '/'
#   7.  \uc8fc\uc11d\ubb38 \ucc98\ub9ac\ub294  #\uc0ac\uc6a9
#   8.  value\uac12 \ub4a4\uc5d0 \uc2a4\ud398\uc774\uc2a4\uac00 \uc874\uc7ac\ud558\ub294 \uacbd\uc6b0 \uc11c\ube14\ub9bf\uc5d0\uc11c \ucc38\uc870\ud560\ub54c\ub294 \uc5d0\ub7ec\ubc1c\uc0dd\ud560 \uc218 \uc788\uc73c\ubbc0\ub85c trim()\ud558\uac70\ub098 \ub9c8\uc9c0\ub9c9 \uacf5\ubc31\uc5c6\uc774 properties \uac12\uc744 \uc124\uc815\ud560\uac83
#-----------------------------------------------------------------------

# \uc11c\ubc84\uc81c\ud488
SERVER_LIST = WEBLOGIC$JEUS$JBOSS
WEBLOGIC.VERSION = 9.2 MP3
JEUS.VERSION = 6.0
JBOSS.VERSION = 3.1
WEBLOGIC.PORT = 7001
JEUS.PORT = 9736
JBOSS.PORT = 8080

# \uc11c\ubc84\ubc84\uc804
SHELL.WINDOWS.getPrductVersion = prg/getPrductVersion.bat
SHELL.UNIX.getPrductVersion = prg/getPrductVersion.sh

# \uc11c\ubc84\uc2e4\ud589\uc0c1\ud0dc
SHELL.WINDOWS.getPrductStatus = prg/getPrductStatus.bat
SHELL.UNIX.getPrductStatus = prg/getPrductStatus.sh

# \uc2dc\uc2a4\ud15c OS\uc815\ubcf4
SHELL.WINDOWS.getOSInfo = prg/getOSInfo.bat
SHELL.UNIX.getOSInfo = prg/getOSInfo.sh

# \uc2dc\uc2a4\ud15c \ub514\uc2a4\ud06c \uc6a9\ub7c9 \uc815\ubcf4
SHELL.WINDOWS.getDiskInfo = prg/getDiskInfo.bat
SHELL.UNIX.getDiskInfo = prg/getDiskInfo.sh

# \uc2dc\uc2a4\ud15c \uba54\ubaa8\ub9ac \uc6a9\ub7c9 \uc815\ubcf4
SHELL.WINDOWS.getMoryInfo = prg/getMoryInfo.bat
SHELL.UNIX.getMoryInfo = prg/getMoryInfo.sh

# MAC
SHELL.UNIX.getNetWorkInfo = prg/getNetWorkInfo.sh
