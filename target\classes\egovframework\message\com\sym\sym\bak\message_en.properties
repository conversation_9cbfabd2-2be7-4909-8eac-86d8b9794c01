comSymSymBak.backupOpertList.title=Backup Job Management List
comSymSymBak.backupOpertList.pageTop.title=Backup Job Management List
comSymSymBak.backupOpertList.caption=Backup Job Management List
comSymSymBak.backupOpertList.validate.searchKeyword=Please select a search condition.
comSymSymBak.backupOpertList.searchCondition=Search Condition
comSymSymBak.backupOpertList.backupOpertId=Backup Job ID
comSymSymBak.backupOpertList.backupOpertNm=Backup Job Name
comSymSymBak.backupOpertList.backupOrginlDrctry=Backup Source Directory
comSymSymBak.backupOpertList.backupStreDrctry=Backup Storage Directory
comSymSymBak.backupOpertList.executCycleNm=Execution Cycle
comSymSymBak.backupOpertList.executSchdul=Execution Schedule
comSymSymBak.backupOpertList.searchKeyword=Search Keyword


comSymSymBak.backupOpertDetail.title=Backup Job Details
comSymSymBak.backupOpertDetail.pageTop.title=Backup Job Details
comSymSymBak.backupOpertDetail.backupOpertId=Backup Job ID
comSymSymBak.backupOpertDetail.backupOpertNm=Backup Job Name
comSymSymBak.backupOpertDetail.backupOrginlDrctry=Backup Source Directory
comSymSymBak.backupOpertDetail.backupStreDrctry=Backup Storage Directory
comSymSymBak.backupOpertDetail.executCycleNm=Execution Cycle
comSymSymBak.backupOpertDetail.cmprsSeNm=Compression Classification


comSymSymBak.backupOpertUpdt.title=Modifying Backup Jobs
comSymSymBak.backupOpertUpdt.pageTop.title=Modifying Backup Jobs
comSymSymBak.backupOpertUpdt.validate.executSchdulDfkSes.length=The weekday is the required input value when the execution cycle is selected as weekly.
comSymSymBak.backupOpertUpdt.validate.cycleMonth.executSchdulDay=When the execution cycle is monthly, the execution schedule (days) is a required input value.
comSymSymBak.backupOpertUpdt.validate.cycleYear.executSchdulMonth=When the execution cycle is annually, the execution schedule (month) is a required input value.
comSymSymBak.backupOpertUpdt.validate.cycleYear.executSchdulDay=When the execution cycle is annually, the execution schedule (days) is a mandatory input value.
comSymSymBak.backupOpertUpdt.validate.checkDate=The execution schedule (month / day) is not valid.
comSymSymBak.backupOpertUpdt.validate.executSchdulDeNm=The execution schedule (date) is a required input value when the execution cycle is only once.
comSymSymBak.backupOpertUpdt.validate.executSchdulDeNm.isDate=The execution schedule (date) is invalid.
comSymSymBak.backupOpertUpdt.backupOpertId=Backup Job ID
comSymSymBak.backupOpertUpdt.backupOpertNm=Backup Job Name
comSymSymBak.backupOpertUpdt.backupOrginlDrctry=Backup Source Directory
comSymSymBak.backupOpertUpdt.backupStreDrctry=Backup Storage Directory
comSymSymBak.backupOpertUpdt.executCycleNm=Execution Cycle
comSymSymBak.backupOpertUpdt.cmprsSeNm=Compression Classification
comSymSymBak.backupOpertUpdt.executSchdulDeNm=Execution schedule(Date)
comSymSymBak.backupOpertUpdt.executSchdulMonth=Execution schedule(Month)
comSymSymBak.backupOpertUpdt.executSchdulDay=Execution schedule(Day)
comSymSymBak.backupOpertUpdt.executSchdulHour=Execution schedule(Hour)
comSymSymBak.backupOpertUpdt.executSchdulMnt=Execution schedule(Minute)
comSymSymBak.backupOpertUpdt.executSchdulSecnd=Execution schedule(Second)
comSymSymBak.backupOpertUpdt.spnMonth=Month
comSymSymBak.backupOpertUpdt.spnDay=Day
comSymSymBak.backupOpertUpdt.spnHH=Hour
comSymSymBak.backupOpertUpdt.spnMM=Minute
comSymSymBak.backupOpertUpdt.spnSS=Second


comSymSymBak.backupOpertRegist.title=Registering Backup Jobs
comSymSymBak.backupOpertRegist.pageTop.title=Registering Backup Jobs
comSymSymBak.backupOpertRegist.validate.executSchdulDfkSes.length=The weekday is the required input value when the execution cycle is selected as weekly.
comSymSymBak.backupOpertRegist.validate.cycleMonth.executSchdulDay=When the execution cycle is monthly, the execution schedule (days) is a required input value.
comSymSymBak.backupOpertRegist.validate.cycleYear.executSchdulMonth=When the execution cycle is annually, the execution schedule (month) is a required input value.
comSymSymBak.backupOpertRegist.validate.cycleYear.executSchdulDay=When the execution cycle is annually, the execution schedule (days) is a mandatory input value.
comSymSymBak.backupOpertRegist.validate.checkDate=The execution schedule (month / day) is not valid.
comSymSymBak.backupOpertRegist.validate.executSchdulDeNm=The execution schedule (date) is a required input value when the execution cycle is only once.
comSymSymBak.backupOpertRegist.validate.executSchdulDeNm.isDate=The execution schedule (date) is invalid.
comSymSymBak.backupOpertRegist.backupOpertNm=Backup Job Name
comSymSymBak.backupOpertRegist.backupOrginlDrctry=Backup Source Directory
comSymSymBak.backupOpertRegist.backupStreDrctry=Backup Storage Directory
comSymSymBak.backupOpertRegist.executCycleNm=Execution Cycle
comSymSymBak.backupOpertRegist.cmprsSeNm=Compression Classification
comSymSymBak.backupOpertRegist.executSchdulDeNm=Execution schedule(Date)
comSymSymBak.backupOpertRegist.executSchdulMonth=Execution schedule(Month)
comSymSymBak.backupOpertRegist.executSchdulDay=Execution schedule(Day)
comSymSymBak.backupOpertRegist.executSchdulHour=Execution schedule(Hour)
comSymSymBak.backupOpertRegist.executSchdulMnt=Execution schedule(Minute)
comSymSymBak.backupOpertRegist.executSchdulSecnd=Execution schedule(Second)
comSymSymBak.backupOpertRegist.spnMonth=Month
comSymSymBak.backupOpertRegist.spnDay=Day
comSymSymBak.backupOpertRegist.spnHH=Hour
comSymSymBak.backupOpertRegist.spnMM=Minute
comSymSymBak.backupOpertRegist.spnSS=Second


comSymSymBak.backupResultList.title=List Of Backup Results
comSymSymBak.backupResultList.pageTop.title=List Of Backup Results
comSymSymBak.backupResultList.caption=List Of Backup Results
comSymSymBak.backupResultList.validate.searchKeyword=Please select a search condition.
comSymSymBak.backupResultList.validate.searchStartDate=Please enter search start date.
comSymSymBak.backupResultList.validate.searchEndDate=Please enter search end date.
comSymSymBak.backupResultList.validate.searchStartDate.name=Search start date
comSymSymBak.backupResultList.validate.searchEndDate.name=Search end date
comSymSymBak.backupResultList.validate.searchKeywordFromTo=The search end time can not be earlier than the search start time.
comSymSymBak.backupResultList.searchKeyword=Search Keyword
comSymSymBak.backupResultList.execStartDate=Execution Start Date
comSymSymBak.backupResultList.searchStartDate=Start Date
comSymSymBak.backupResultList.searchEndDate=End Date
comSymSymBak.backupResultList.searchCondition=Search Condition
comSymSymBak.backupResultList.sttusAll=All
comSymSymBak.backupResultList.sttusNormal=Normal
comSymSymBak.backupResultList.sttusAbnormal=Abnormal
comSymSymBak.backupResultList.sttusPerforming=Performing
comSymSymBak.backupResultList.backupResultId=Backup Result ID
comSymSymBak.backupResultList.backupOpertId=Backup Job ID
comSymSymBak.backupResultList.backupOpertNm=Backup Job Name
comSymSymBak.backupResultList.sttusNm=Condition
comSymSymBak.backupResultList.executBeginTime=Start Execution Time
comSymSymBak.backupResultList.executEndTime=Execution End Time


comSymSymBak.backupResultDetail.title=Detailed View Of Backup Result
comSymSymBak.backupResultDetail.pageTop.title=Detailed View Of Backup Result
comSymSymBak.backupResultDetail.backupResultId=Backup Result ID
comSymSymBak.backupResultDetail.backupOpertId=Backup Job ID
comSymSymBak.backupResultDetail.backupOpertNm=Backup Job Name
comSymSymBak.backupResultDetail.backupOrginlDrctry=Backup Source Directory
comSymSymBak.backupResultDetail.backupStreDrctry=Backup Storage Directory
comSymSymBak.backupResultDetail.backupFile=Backup File
comSymSymBak.backupResultDetail.sttusNm=Condition
comSymSymBak.backupResultDetail.errorInfo=Error Information
comSymSymBak.backupResultDetail.executBeginTime=Start Execution Time
comSymSymBak.backupResultDetail.executEndTime=End Execution Time



