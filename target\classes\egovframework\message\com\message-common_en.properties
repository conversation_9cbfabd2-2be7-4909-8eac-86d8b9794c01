fail.common.msg=error ocurred!
fail.common.sql=sql error ocurred! error code: {0}, error msg: {1}
info.nodata.msg=no data found.

#UI Common resource#
table.num=num.
table.regdate=reg.date
table.reger=registrant
table.select=select
title.html=egovframe common component
title.detail=Detail Inquiry
title.inquire=Inquire
title.update=Modify
title.create=Create
title.delete=Delete
title.save=Save
title.list=List
title.searchCondition=search condition
title.search=keyword
title.reply=reply
title.scrap=scrap
title.comment=comment
title.attachedFileSelect=attached file
title.attachedFileDelete=attached file delete
title.link=link
title.management=Management
title.all=All

input.select=Select
input.cSelect=Select
input.input=input
input.button=button
input.selectAll.title=Checkbox select all
input.yes=Yes
input.no=No

select.searchCondition=select condition select

button.select=select
button.search=Search
button.use=use
button.notUsed=Not used
button.inquire=inquire
button.update=update
button.create=create
button.delete=delete
button.deleteDatabase=Wiping
button.close=close
button.save=save
button.list=list
button.reset=reset
button.passwordUpdate=password update
button.subscribe=subscribe
button.realname=realname confirm
button.moveToGpin=move to gpin confirm
button.moveToIhidnum=move to ihidnum confirm
button.agree=agree
button.disagree=disagree
button.possible=possible
button.impossible=impossible
button.qnaregist=Q&A create
button.cnsltregist=Counsel create
button.preview=preview
button.next=nexut
button.add=add it now
button.confirm=confirm
button.back =back
button.yes =yes
button.no =no
button.home =home
button.user =user support
button.cop =cooperation
button.wrkstart = work start
button.wrkend = work end
button.reply = reply
button.scrap = scrap
button.comment = comment
button.excel = excel
button.init=init
button.acknowledgment=acknowledgment
button.cancelAcknowledgment=cancel acknowledgment
button.bulkUpload=bulk upload
button.log = log
button.set = set
button.move = move
button.answer = answer

#UI Common Message#
common.noScriptTitle.msg=I can't use all functions in browser JavaScript is not supported.
common.searchCondition.msg=This layout is a lower information searches made up of information.

common.summary.list={0} A list of the details of the output.
common.summary.regist={0} Registered by typing the possible contents of registers by clicking the button.
common.summary.update={0} Registered by typing the possible content of modification by clicking the button.
common.summary.inqire={0} Full inquiry details about the details of the output.

common.save.msg=confirm save?
common.regist.msg=confirm regist?
common.delete.msg=confirm delete?
common.update.msg=confirm update?
common.nodata.msg=There is no data. please choose another seach keyword
common.required.msg=is required field
common.acknowledgement.msg=confirm acknowledgement?
common.acknowledgementcancel.msg=confirm acknowledgement cancel?
common.nocomment.msg=There is no comment.
common.noguest.msg=There is no guest notice.

success.request.msg=you're request successfully done
success.common.select=successfully selected
success.common.insert=successfully inserted
success.common.update=successfully updated
success.common.delete=successfully deleted

common.imposbl.fileupload = cannot upload files
common.isConfmDe.msg=Please check the approval date box
common.isExist.msg = already exist

fail.common.insert = fail to insert.
fail.common.update = fail to update
fail.common.delete = fail to delete
fail.common.delete.upperMenuExist = fail to delete[upperMenuId foreign key error]
fail.common.select = fail to select
fail.common.login = login information is not correct
fail.common.loginIncorrect = login in more than {0} account will be locked!
fail.common.login.password = password information is not correct(password digit should be 8 to 20)
fail.common.idsearch = can not find id
fail.common.pwsearch = can not find password
fail.request.msg = Failed to handle the request
fail.common.login.ip = Login is refused because it is not a registered IP.

#UI User Message#
fail.user.passwordUpdate1=current password is not correct
fail.user.passwordUpdate2=password confirm is not correct
info.user.rlnmCnfirm=realname confirm ready
success.user.rlnmCnfirm=it is realname
fail.user.rlnmCnfirm=it is not realname
fail.user.connectFail=connection fail
info.user.rlnmPinCnfirm=Using the i-PIN ID, please check your real name
success.user.rlnmPinCnfirm=Consistent with the registration data of the i-PIN
fail.user.rlnmPinCnfirm=i-PIN does not match the registered data

#Vlidator Errors#
errors.prefix=<div class="error">
errors.suffix=</div><br/>

errors.required={0} is required.
errors.minlength={0} can not be less than {1} characters.
errors.maxlength={0} can not be greater than {1} characters.
errors.invalid={0} is invalid.

errors.byte={0} must be a byte.
errors.short={0} must be a short.
errors.integer={0} must be an integer.
errors.long={0} must be a long.
errors.float={0} must be a float.
errors.double={0} must be a double.

errors.date={0} is not a date.
errors.range={0} is not in the range {1} through {2}.
errors.creditcard={0} is an invalid credit card number.
errors.email={0} is an invalid e-mail address.

errors.ihidnum=Is not a valid Social Security number
errors.korean={0} should be entered with their Korean
errors.ip=Invalid IP address.
errors.english={0}is an invalid English character.
errors.notKorean={0} should not be entered with their Korean

errors.password1={0} must be entered within 8 to 20 characters.
errors.password2={0} is Korean, special characters, spaces are not allowed.
errors.password3={0} can not be used continuously for more than four sequential digits.
errors.password4={0} can not be used continuously for more than 4 letters or numbers that are repeated.
errors.pwdcheckcomb3={0} must be include as least 3 of English alphabet, digit number, special characters such as ~!@#$%^&*?
errors.pwdcheckcomb4={0} must be include as least 4 of English alphabet uppercase letter, English alphabet lowercase letter, digit number, special characters such as ~!@#$%^&*? 
errors.pwdcheckseries={0} can not be used continuously for more than 3 sequential letters or digits.
errors.pwdcheckrepeat={0} can not be used continuously for more than 3 letters or numbers that are repeated.

error.security.runtime.error = error

info.password.rule.password1 = - must be entered within 8 to 20 characters.
info.password.rule.pwdcheckcomb3 = - must be include as least 3 of English alphabet, digit number, special characters such as ~!@#$%^&*?
info.password.rule.pwdcheckcomb4 = - must be include as least 4 of English alphabet uppercase letter, English alphabet lowercase letter, digit number, special characters such as ~!@#$%^&*?
info.password.rule.pwdcheckseries = - can not be used continuously for more than 3 sequential letters or digits ex) kkk, 321 are not allowed.

#Auth Errors#
errors.auth.invalid=do not have permission to process that function..

#Xss Errors#
errors.xss.checkerUser=There is no use and disposal authority for its capabilities.

#File Upload / Download
errors.file.extension=It is not a supported file type.
errors.file.transfer=An error occurred during file transfer.
success.file.transfer=The file transfer is complete.
errors.file.extension.none=There is no file extension.
errors.file.extension.deny=Unacceptable file extension.
