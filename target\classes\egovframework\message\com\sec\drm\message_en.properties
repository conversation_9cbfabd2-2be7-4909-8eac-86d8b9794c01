comCopSecDrm.title=Department Authority Management

comCopSecDrm.searchCondition.searchKeywordText=Department

comCopSecDrm.btn.deptSelectPopup=Department Inquiry Popup

comCopSecDrm.list.userId=User ID
comCopSecDrm.list.userNm=User Name
comCopSecDrm.list.author=Authority
comCopSecDrm.list.regAt=Available Options About Register
comCopSecDrm.list.deptCd=Department Code
comCopSecDrm.list.deptNm=Department Name
comCopSecDrm.list.searchDept=Department Search

comCopSecDrm.validate.deptAuthorSelect=There is no selected user.
comCopSecDrm.validate.deptAuthorNoSelectResult=There is no viewed result.

comCopSecDrm.validate.deptSelect=Please select the viewed department.

comCopSecDrm.deptSearchPopup.title=Department Inquiry Popup
comCopSecDrm.deptSearchPopup.searchKeywordText=Department Name
comCopSecDrm.deptSearchPopupList.deptId=Department ID
comCopSecDrm.deptSearchPopupList.deptNm=Department Name
comCopSecDrm.deptSearchPopup.validate.alert.selectOne=Please select one department.
comCopSecDrm.deptSearchPopup.validate.alert.selectNothing=No items selected.
comCopSecDrm.deptSearchPopup.validate.alert.searchAfter=Please select after viewing.
