#Received Note Box Management Message#

comUssIonNtr.title=Received Note Box Management 

comUssIonNtr.list.seq=seq
comUssIonNtr.list.noteSj=subject
comUssIonNtr.list.rcverNm=sender
comUssIonNtr.list.rcverDateTime=sender time

comUssIonNtr.detail.noteSj=Note Subject
comUssIonNtr.detail.trnsmiterNm=Sender
comUssIonNtr.detail.trnsmiterPnttm=Sender Time
comUssIonNtr.detail.rcverNm=Receiver
comUssIonNtr.detail.rcverPnttm=Receiver Time
comUssIonNtr.detail.noteRecptn=Note Contents

comUssIonNtr.btn.replay=replay

comUssIonNtr.searchCondition.rcverDate=sender date
comUssIonNtr.searchCondition.NOTE_SJ=note subject 
comUssIonNtr.searchCondition.NOTE_CN=note contents
comUssIonNtr.searchCondition.RCVER_NM=sender
comUssIonNtr.searchCondition.searchFromDate=send start date
comUssIonNtr.searchCondition.searchToDate=send end date

comUssIonNtr.validate.deleteCnfirmt=Do you confirm the deletion of the selected Inbox?
comUssIonNtr.validate.noDelList=Select a list to delete!
comUssIonNtr.validate.dateFromCheck=Start date of search criteria is later than end date Check the date of the search term!