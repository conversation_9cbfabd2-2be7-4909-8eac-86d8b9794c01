#Facebook
# EgovFacebookHome.jsp
comUssIonFbk.facebookHome.title = Feature List
comUssIonFbk.facebookHome.subject = Facebook interlock
comUssIonFbk.facebookHome.viewFeed = View Wall
comUssIonFbk.facebookHome.viewDetailAlbum = View photo album details
comUssIonFbk.facebookHome.viewProfile = View profile information
comUssIonFbk.facebookHome.signOutFacebook = Sign out of Facebook

# EgovFacebookAlbum.jsp
comUssIonFbk.facebookAlbum.title = View your albums

# EgovFacebookAlbums.jsp
comUssIonFbk.facebookAlbums.title = View Album List

# EgovFacebookFeed.jsp
comUssIonFbk.facebookFeed.title = WallView

# EgovFacebookProfile.jsp
comUssIonFbk.facebookProfile.title = View your profile

# EgovFacebookSignin.jsp
comUssIonFbk.facebookSignin.title = Sign in
comUssIonFbk.facebookSignin.titleInfo=Working with Facebook
comUssIonFbk.facebookSignin.description=For functional testing, you must register the API in advance at https://developers.facebook.com.
comUssIonFbk.facebookSignin.description_2=For functional testing, Calls from HTTPS pages are required. 
