#User Absence Management List#
ussIonUas.userAbsnceList.userAbsnceList=User Absence Management List
ussIonUas.userAbsnceList.userNm=User Name
ussIonUas.userAbsnceList.userId=User ID
ussIonUas.userAbsnceList.userAbsnceAt=Absence
ussIonUas.userAbsnceList.regYn=Registration
ussIonUas.userAbsnceList.lastUpdusrPnttm=Registration Date
ussIonUas.userAbsnceList.all=ALL
ussIonUas.userAbsnceList.noUserSlct=No users selected.
ussIonUas.userAbsnceList.failInquir=No results were inquired.
ussIonUas.userAbsnceList.regiAftrView=Please register after viewing.
ussIonUas.userAbsnceList.alreadyRegis=Already registered.
ussIonUas.userAbsnceList.selectOnlyOne=Please select only one to register.
ussIonUas.userAbsnceList.noRegisTargetSlct=No registration target selected.
ussIonUas.userAbsnceList.goToregisPage=There is no registered user absence information. Do you want to go to registration page?
ussIonUas.userAbsnceList.deleteMsg=Will you delete it?

ussIonUas.userAbsnceRegist.userAbsnceRegist=User Absence Management Regist
ussIonUas.userAbsnceRegist.userNm=User Name
ussIonUas.userAbsnceRegist.userId=User ID
ussIonUas.userAbsnceRegist.userAbsnceAt=Absence
ussIonUas.userAbsnceRegist.lastUpdusrPnttm=Registration
ussIonUas.userAbsnceRegist.all=ALL
ussIonUas.userAbsnceRegist.saveMsg=Will you save it?
ussIonUas.userAbsnceRegist.deleteMsg=Will you delete it?

ussIonUas.userAbsnceUpdt.userAbsnceUpdt=User Absence Management
ussIonUas.userAbsnceUpdt.userNm=User Name
ussIonUas.userAbsnceUpdt.userId=User ID
ussIonUas.userAbsnceUpdt.userAbsnceAt=Absence
ussIonUas.userAbsnceUpdt.lastUpdusrPnttm=Registration
ussIonUas.userAbsnceUpdt.saveMsg=Will you save it?
ussIonUas.userAbsnceUpdt.deleteMsg=Will you delete it?
