#Main Screen Image Management#
uss.ion.msi.mainImageList.mainImageList=Main Screen Image List
uss.ion.msi.mainImageList.mainImageNm=Image Name
uss.ion.msi.mainImageList.mainImage=Image
uss.ion.msi.mainImageList.mainImageDc=Image Description
uss.ion.msi.mainImageList.mainImageReflctAt=Applicability
uss.ion.msi.mainImageList.noImage=No main image selected.
uss.ion.msi.mainImageList.failInquire=No results were inquired.
uss.ion.msi.mainImageList.deleteImage=Will you delete it?

uss.ion.msi.mainImageRegist.mainImageRegist=Main Screen Image Regist
uss.ion.msi.mainImageRegist.mainImageNm=Image Name
uss.ion.msi.mainImageRegist.mainImage=Image
uss.ion.msi.mainImageRegist.mainImageId=Image ID
uss.ion.msi.mainImageRegist.mainImageDc=Image Description
uss.ion.msi.mainImageRegist.mainImageReflctAt=Applicability
uss.ion.msi.mainImageRegist.mainImageregDate=Registration date
uss.ion.msi.mainImageRegist.saveImage=Do you want to save it?
uss.ion.msi.mainImageRegist.ImageReq=Image is required.
uss.ion.msi.mainImageRegist.deleteImage=Will you delete it?

uss.ion.msi.mainImageUpdt.mainImageUpdt=Main Screen Image Update
uss.ion.msi.mainImageUpdt.mainImageNm=Image Name
uss.ion.msi.mainImageUpdt.mainImage=Image
uss.ion.msi.mainImageUpdt.mainImageId=Image ID
uss.ion.msi.mainImageUpdt.mainImageDc=Image Description
uss.ion.msi.mainImageUpdt.mainImageReflctAt=Applicability
uss.ion.msi.mainImageUpdt.mainImageregDate=Registration date
uss.ion.msi.mainImageUpdt.saveImage=Do you want to save it?
uss.ion.msi.mainImageUpdt.ImageReq=Image is required.
uss.ion.msi.mainImageUpdt.deleteImage=Will you delete it?

uss.ion.msi.mainImageView.mainImageView=Main screen image reflected
uss.ion.msi.mainImageView.mainImageViewDc=This page is where 770. images registered in Main Image Management are reflected.