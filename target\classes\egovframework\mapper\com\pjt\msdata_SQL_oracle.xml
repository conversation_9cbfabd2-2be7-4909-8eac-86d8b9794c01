<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="MsData">

    <resultMap id="msdata" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
    </resultMap>

    <resultMap id="msdataMunserType" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="munserType" column="munser_type"/>
    </resultMap>

<!--     <resultMap id="msdataIndexLocation" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="hjdonName" column="hjdon_name"/>
        <result property="jibunDsp" column="jibun_dsp"/>
    </resultMap> -->

    <resultMap id="msdataFile" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="pathName" column="path_name"/>
        <result property="fileName" column="file_name"/>
    </resultMap>

    <resultMap id="msdataHistory" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="srNo" column="sr_no"/>
        <result property="history" column="history"/>
    </resultMap>

    <resultMap id="msdataBasicInfo" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="srNo" column="sr_no"/>
        <result property="jimok" column="jimok"/>
        <result property="jijuk" column="jijuk"/>
        <result property="jijukUnit" column="jijuk_unit"/>
        <result property="grade1" column="grade1"/>
        <result property="grideUnit" column="gride_unit"/>
        <result property="grade2" column="grade2"/>
        <result property="jimokLine" column="jimok_line"/>
        <result property="gradeLine" column="grade_line"/>
    </resultMap>

    <resultMap id="msdataDateInfo" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="srNo" column="sr_no"/>
        <result property="yearCode" column="era_code"/>
        <result property="hisDate" column="his_date"/>
        <result property="yearCode_d" column="era_code_d"/>
        <result property="hisDate_d" column="his_date_d"/>
        <result property="reason" column="reason"/>
        <result property="address" column="address"/>
        <result property="percent" column="percent"/>
    </resultMap>

    <resultMap id="msdataOwnerInfo" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="srNo" column="sr_no"/>
        <result property="name" column="names"/>
    </resultMap>

    <resultMap id="msdataIndexInfo" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="jimeung" column="jimeung"/>
        <result property="jibun" column="jibun"/>
        <result property="grade" column="grade"/>
        <result property="jukyo" column="jukyo"/>
    </resultMap>

    <resultMap id="msdataAll" type="egovframework.com.pjt.service.MsDataVO">
        <result property="rollNo" column="roll_no"/>
        <result property="frameNo" column="frame_no"/>
        <result property="srNo" column="sr_no"/>
        <result property="jimok" column="jimok"/>
        <result property="jijuk" column="jijuk"/>
        <result property="jijukUnit" column="jijuk_unit"/>
        <result property="grade1" column="grade1"/>
        <result property="grideUnit" column="gride_unit"/>
        <result property="grade2" column="grade2"/>
        <result property="history" column="history"/>
        <result property="yearCode" column="year_code"/>
        <result property="hisDate" column="era_code"/>
        <result property="reason" column="reason"/>
        <result property="address" column="address"/>
        <result property="name" column="names"/>
        <result property="jimeung" column="jimeung"/>
        <result property="jibun" column="jibun"/>
        <result property="grade" column="grade"/>
        <result property="jukyo" column="jukyo"/>
    </resultMap>

	<!-- 권 조회 -->
    <select id="rollList" resultMap="msdata">
        SELECT DISTINCT ROLL_NO
        FROM MS_DATA
        WHERE ROLL_NO IS NOT NULL
        AND   MUNSER_TYPE IS NOT NULL
        ORDER BY ROLL_NO
    </select>

    <select id="frameList" parameterType="egovframework.com.pjt.service.MsDataVO" resultMap="msdata">
	    SELECT DISTINCT frame_no
	    FROM MS_DATA
	    WHERE roll_no = #{rollNo}
	    AND   frame_no IS NOT NULL
        AND   MUNSER_TYPE IS NOT NULL
	    ORDER BY frame_no
	</select>

	<select id="selectFile" parameterType="egovframework.com.pjt.service.MsDataVO" resultMap="msdataFile">
	    SELECT DISTINCT
	    	   roll_no,
	           frame_no,
	           path_name,
	           file_name
	    FROM MS_DATA
	    WHERE roll_no = #{rollNo}
	      AND frame_no = #{frameNo}
	</select>

	<select id="currentUser" resultType="string">
    	SELECT USER FROM dual
    </select>

    <!-- 토지대장 기본정보 조회 -->
    <select id="selectLandBasicInfo" parameterType="map" resultMap="msdataBasicInfo">
        SELECT
            ROLL_NO,
            FRAME_NO,
            SR_NO,
            JIMOK,
            JIJUK,
            JIJUK_UNIT,
            GRADE1,
            GRIDE_UNIT,
            GRADE2,
            JIMOK_LINE,
            GRADE_LINE
        FROM
            MS_DATA_JIMOK
        WHERE
            ROLL_NO = #{rollNo}
            AND FRAME_NO = #{frameNo}
        ORDER BY
            SR_NO
    </select>

    <!-- 토지대장 연혁 조회 -->
    <select id="selectLandHistory" parameterType="map" resultMap="msdataHistory">
        SELECT
            ROLL_NO,
            FRAME_NO,
            SR_NO,
            REPLACE(HISTORY, CHR(13)||CHR(10), '') AS HISTORY
        FROM
            MS_DATA_HISTORY
        WHERE
            ROLL_NO = #{rollNo}
            AND FRAME_NO = #{frameNo}
        ORDER BY
            SR_NO
    </select>

    <!-- 토지대장 날짜/사고/주소/지분 조회 -->
    <select id="selectLandDateInfo" parameterType="map" resultMap="msdataDateInfo">
        SELECT
            ROLL_NO,
            FRAME_NO,
            SR_NO,
            ERA_CODE,
            HIS_DATE,
            ERA_CODE_D,
            HIS_DATE_D,
            REASON,
            ADDRESS,
            PERCENT
        FROM
            MS_DATA_OWN
        WHERE
            ROLL_NO = #{rollNo}
            AND FRAME_NO = #{frameNo}
        ORDER BY
            SR_NO
    </select>

    <!-- 토지대장 소유자 조회 -->
    <select id="selectLandOwnerInfo" parameterType="map" resultMap="msdataOwnerInfo">
        SELECT
            ROLL_NO,
            FRAME_NO,
            SR_NO,
            NAMES
        FROM
            MS_DATA_OWNER
        WHERE
            ROLL_NO = #{rollNo}
            AND FRAME_NO = #{frameNo}
        ORDER BY
            SR_NO
    </select>

    <!-- 토지대장 색인정보 조회 -->
    <select id="selectLandIndexInfo" parameterType="map" resultMap="msdataIndexInfo">
        SELECT
            ROLL_NO,
            FRAME_NO,
            JIMEUNG,
            JIBUN,
            GRADE,
            JUKYO
        FROM
            MS_DATA_JUKYO
        WHERE
            ROLL_NO = #{rollNo}
            AND FRAME_NO = #{frameNo}
    </select>

    <!-- 토지대장 기본정보 삭제 -->
    <delete id="deleteLandBasicInfo" parameterType="map">
        DELETE FROM MS_DATA_JIMOK
        WHERE ROLL_NO = #{rollNo}
        AND FRAME_NO = #{frameNo}
    </delete>

    <!-- 토지대장 기본정보 저장 -->
    <insert id="insertLandBasicInfo" parameterType="egovframework.com.pjt.service.MsDataVO">
        INSERT INTO MS_DATA_JIMOK (
            ROLL_NO, FRAME_NO, SR_NO,
            JIMOK, JIMOK_LINE, JIJUK, JIJUK_UNIT,
            GRADE1, GRADE_LINE, GRIDE_UNIT, GRADE2
        ) VALUES (
            #{rollNo}, #{frameNo}, #{srNo},
            #{jimok}, #{jimokLine}, #{jijuk}, #{jijukUnit},
            #{grade1}, #{gradeLine}, #{gradeUnit}, #{grade2}
        )
    </insert>

    <!-- 토지대장 연혁 삭제 -->
    <delete id="deleteLandHistory" parameterType="map">
        DELETE FROM MS_DATA_HISTORY
        WHERE ROLL_NO = #{rollNo}
        AND FRAME_NO = #{frameNo}
    </delete>

    <!-- 토지대장 연혁 저장 -->
    <insert id="insertLandHistory" parameterType="egovframework.com.pjt.service.MsDataVO">
        INSERT INTO MS_DATA_HISTORY (
            ROLL_NO, FRAME_NO, SR_NO, HISTORY
        ) VALUES (
            #{rollNo}, #{frameNo}, #{srNo}, #{history}
        )
    </insert>

    <!-- 토지대장 날짜/사고/주소 삭제 -->
    <delete id="deleteLandDateInfo" parameterType="map">
        DELETE FROM MS_DATA_OWN
        WHERE ROLL_NO = #{rollNo}
        AND FRAME_NO = #{frameNo}
    </delete>

    <!-- 토지대장 날짜/사고/주소 저장 -->
    <insert id="insertLandDateInfo" parameterType="egovframework.com.pjt.service.MsDataVO">
        INSERT INTO MS_DATA_OWN (
            ROLL_NO, FRAME_NO, SR_NO, ERA_CODE, HIS_DATE, REASON, ADDRESS
        ) VALUES (
            #{rollNo}, #{frameNo}, #{srNo}, #{yearCode}, #{hisDate}, #{reason}, #{address}
        )
    </insert>

    <!-- 토지대장 소유자 삭제 -->
    <delete id="deleteLandOwnerInfo" parameterType="map">
        DELETE FROM MS_DATA_OWNER
        WHERE ROLL_NO = #{rollNo}
        AND FRAME_NO = #{frameNo}
    </delete>

    <!-- 토지대장 소유자 저장 -->
    <insert id="insertLandOwnerInfo" parameterType="egovframework.com.pjt.service.MsDataVO">
        INSERT INTO MS_DATA_OWNER (
            ROLL_NO, FRAME_NO, SR_NO, NAMES
        ) VALUES (
            #{rollNo}, #{frameNo}, #{srNo}, #{name}
        )
    </insert>

    <!-- 토지대장 색인정보 삭제 -->
    <delete id="deleteLandIndexInfo" parameterType="map">
        DELETE FROM MS_DATA_JUKYO
        WHERE ROLL_NO = #{rollNo}
        AND FRAME_NO = #{frameNo}
    </delete>

    <!-- 토지대장 색인정보 저장 -->
    <insert id="insertLandIndexInfo" parameterType="egovframework.com.pjt.service.MsDataVO">
        INSERT INTO MS_DATA_JUKYO (
            ROLL_NO, FRAME_NO, SR_NO, JIMEUNG, JIBUN, GRADE, JUKYO
        ) VALUES (
            #{rollNo}, #{frameNo}, #{srNo}, #{jimeung}, #{jibun}, #{grade}, #{jukyo}
        )
    </insert>

    <!-- 지목 코드리스트 조회 -->
    <select id="selectJimokCodeList" resultType="egovMap">
        SELECT CODE2, CODE3, CODE_NAME
        FROM   CD_CODE
        WHERE  CODE1=0002
        ORDER BY CODE2, TO_NUMBER(CODE3)
    </select>

    <!-- 연혁 코드리스트 조회 -->
    <select id="selectHistoryCodeList" resultType="egovMap">
        SELECT LIST_NO, REPLACE(HISTORY, CHR(13)||CHR(10), '') AS HISTORY
        FROM   MS_DATA_HISTORY_LIST
        WHERE  HISTORY IS NOT NULL
        ORDER BY LIST_NO
    </select>

    <!-- 소유자 사고 코드리스트 조회 -->
    <select id="selectReasonCodeList" resultType="egovMap">
        SELECT CODE3, CODE_NAME
        FROM   CD_CODE
        WHERE  CODE1=0010 AND CODE2=0003
        ORDER BY TO_NUMBER(CODE3)
    </select>

    <!-- 소유자 연호 코드리스트 조회 -->
    <select id="selectYearCodeList" resultType="egovMap">
        SELECT YEAR_CODE, YEAR_NAME
        FROM   CD_YEAR_TYPE
        ORDER BY TO_NUMBER(YEAR_CODE)
    </select>

    <!-- 소유자 주소리스트 조회 -->
    <select id="selecAddressList" resultType="egovMap">
        SELECT DISTINCT REPLACE(REGEXP_REPLACE(ADDRESS, '[.*?]', ''),'\\','') AS ADDRESS
        FROM   MS_DATA_OWN
        WHERE  ADDRESS IS NOT NULL
        ORDER BY ADDRESS
    </select>

    <!-- 소유자 이름름리스트 조회 -->
    <select id="selectNameList" resultType="egovMap">
        SELECT DISTINCT REPLACE(REGEXP_REPLACE(NAMES, '[.*?]', ''),'\\','') AS NAMES
        FROM   MS_DATA_OWNER
        WHERE  NAMES IS NOT NULL
        ORDER BY NAMES
    </select>
    
    <!-- 조회한 페이지의 마지막 순번값 -->
    <select id="selectMaxSrnoByTable" resultType="int">
	    <if test="workcode == 1">
	        SELECT NVL(MAX(SR_NO), 0) AS SR_NO
	        FROM MS_DATA_JIMOK
	        WHERE ROLL_NO = #{rollNo}
	          AND FRAME_NO = #{frameNo}
	    </if>
	    <if test="workcode == 2">
	        SELECT NVL(MAX(SR_NO), 0) AS SR_NO
	        FROM MS_DATA_HISTORY
	        WHERE ROLL_NO = #{rollNo}
	          AND FRAME_NO = #{frameNo}
	    </if>
	    <if test="workcode == 3">
	        SELECT NVL(MAX(SR_NO), 0) AS SR_NO
	        FROM MS_DATA_OWN
	        WHERE ROLL_NO = #{rollNo}
	          AND FRAME_NO = #{frameNo}
	    </if>
	    <if test="workcode == 4">
	        SELECT NVL(MAX(SR_NO), 0) AS SR_NO
	        FROM MS_DATA_OWNER
	        WHERE ROLL_NO = #{rollNo}
	          AND FRAME_NO = #{frameNo}
	    </if>
	</select>

</mapper>
