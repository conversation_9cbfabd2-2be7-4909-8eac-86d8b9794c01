<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="entrprsManageDAO">

    <resultMap id="stplatMap" type="egovframework.com.uss.umt.service.StplatVO">
        <result property="useStplatId" column="USE_STPLAT_ID"/>
        <result property="useStplatCn" column="USE_STPLAT_CN"/>
        <result property="infoProvdAgeCn" column="INFO_PROVD_AGRE_CN"/>
    </resultMap>
       
    <insert id="insertEntrprs_S">
        
            INSERT INTO COMTNENTRPRSMBER 
                (   ESNTL_ID                      ,
                    ENTRPRS_MBER_ID               ,
                    ENTRPRS_SE_CODE              ,
                    BIZRNO                       ,
                    JURIRNO                      ,
                    CMPNY_NM                     ,
                    CXFC                         ,
                    ZIP                          ,
                    ADRES                        ,
                    ENTRPRS_MIDDLE_TELNO         ,
                    FXNUM                        ,
                    INDUTY_CODE                  ,
                    APPLCNT_NM                   ,
                    SBSCRB_DE                    ,
                    ENTRPRS_MBER_STTUS           ,
                    ENTRPRS_MBER_PASSWORD        ,
                    ENTRPRS_MBER_PASSWORD_HINT   ,
                    ENTRPRS_MBER_PASSWORD_CNSR   ,
                    GROUP_ID                     ,
                    DETAIL_ADRES                 ,
                    ENTRPRS_END_TELNO            ,
                    AREA_NO                      ,
                    APPLCNT_EMAIL_ADRES          ,
                    APPLCNT_IHIDNUM              ,
                    CHG_PWD_LAST_PNTTM			)
            VALUES (
                    #{uniqId},
                    #{entrprsmberId},
                    #{entrprsSeCode},
                    #{bizrno},
                    #{jurirno},
	                #{cmpnyNm},
	                #{cxfc},
	                #{zip},
	                #{adres},
	                #{entrprsMiddleTelno},
	                #{fxnum},
	                #{indutyCode},
	                #{applcntNm},
	                SYSDATETIME,
	                #{entrprsMberSttus},
	                #{entrprsMberPassword},
	                #{entrprsMberPasswordHint},
	                #{entrprsMberPasswordCnsr},
	                #{groupId},
	                #{detailAdres},
	                #{entrprsEndTelno},
	                #{areaNo},
	                #{applcntEmailAdres},
	                #{applcntIhidnum},
	                SYSDATETIME        )
        
    </insert>
    
    <delete id="deleteEntrprs_S">
        
            DELETE FROM COMTNENTRPRSMBER 
            WHERE ESNTL_ID=#{delId}
        
    </delete>
    
    <select id="selectEntrprs_S" resultType="egovframework.com.uss.umt.service.EntrprsManageVO">
        
            SELECT
                ESNTL_ID                      uniqId,
                'USR02'                      userTy,
                ENTRPRS_MBER_ID               entrprsmberId,
                ENTRPRS_SE_CODE              entrprsSeCode,
                BIZRNO                       bizrno,
                JURIRNO                      jurirno,
                CMPNY_NM                     cmpnyNm,
                CXFC                         cxfc,
                ZIP                          zip,
                ADRES                        adres,
                ENTRPRS_MIDDLE_TELNO         entrprsMiddleTelno,
                FXNUM                        fxnum,
                INDUTY_CODE                  indutyCode,
                APPLCNT_NM                   applcntNm,
                SBSCRB_DE                    sbscrbDe,
                ENTRPRS_MBER_STTUS           entrprsMberSttus,
                ENTRPRS_MBER_PASSWORD        entrprsMberPassword,
                ENTRPRS_MBER_PASSWORD_HINT   entrprsMberPasswordHint,
                ENTRPRS_MBER_PASSWORD_CNSR   entrprsMberPasswordCnsr,
                GROUP_ID                     groupId,
                DETAIL_ADRES                 detailAdres,
                ENTRPRS_END_TELNO            entrprsEndTelno,
                AREA_NO                      areaNo,
                APPLCNT_IHIDNUM              applcntIhidnum,
                APPLCNT_EMAIL_ADRES          applcntEmailAdres,
                LOCK_AT        				 lockAt
            FROM COMTNENTRPRSMBER
            WHERE ESNTL_ID=#{uniqId}
        
    </select>

    <update id="updateEntrprs_S">
        
            UPDATE COMTNENTRPRSMBER 
            SET 
                ENTRPRS_MBER_ID               = #{entrprsmberId},
                ENTRPRS_SE_CODE              = #{entrprsSeCode},
                BIZRNO                       = #{bizrno},
                JURIRNO                      = #{jurirno},
                CMPNY_NM                     = #{cmpnyNm},
                CXFC                         = #{cxfc},
                ZIP                          = #{zip},
                ADRES                        = #{adres},
                ENTRPRS_MIDDLE_TELNO         = #{entrprsMiddleTelno},
                FXNUM                        = #{fxnum},
                INDUTY_CODE                  = #{indutyCode},
                APPLCNT_NM                   = #{applcntNm},
                ENTRPRS_MBER_STTUS           = #{entrprsMberSttus},
                ENTRPRS_MBER_PASSWORD_HINT   = #{entrprsMberPasswordHint},
                ENTRPRS_MBER_PASSWORD_CNSR   = #{entrprsMberPasswordCnsr},
                GROUP_ID                     = #{groupId},
                DETAIL_ADRES                 = #{detailAdres},
                ENTRPRS_END_TELNO            = #{entrprsEndTelno},
                AREA_NO                      = #{areaNo},
                APPLCNT_EMAIL_ADRES          = #{applcntEmailAdres}
            WHERE ESNTL_ID=#{uniqId}
        
    </update>
    
    <select id="selectStplat_S" resultMap="stplatMap">
        
            SELECT
                USE_STPLAT_ID           ,
                USE_STPLAT_CN           ,
                INFO_PROVD_AGRE_CN      
            FROM COMTNSTPLATINFO
            WHERE USE_STPLAT_ID=#{stplatId}
        
    </select>
    
    <update id="updatePassword_S">
        
            UPDATE COMTNENTRPRSMBER 
            SET 
                   ENTRPRS_MBER_PASSWORD   =  #{entrprsMberPassword}
                 , CHG_PWD_LAST_PNTTM = SYSDATETIME
            WHERE  ESNTL_ID  = #{uniqId}
        
    </update>
    
    <select id="selectPassword_S" resultType="egovframework.com.uss.umt.service.EntrprsManageVO">
        
            SELECT
                    ENTRPRS_MBER_PASSWORD          entrprsMberPassword 
            FROM    COMTNENTRPRSMBER
            WHERE   ESNTL_ID=#{uniqId}
        
    </select>
    
    <select id="selectEntrprsMberList" resultType="egovMap">
        SELECT * FROM ( SELECT rownum rn, TB.* FROM (
            SELECT 
                ESNTL_ID              as UNIQ_ID,
                'USR02'               as USER_TY,
                ENTRPRS_MBER_ID       as USER_ID,
                CMPNY_NM              as CMPNY_NM,
                APPLCNT_NM            as USER_NM,
                APPLCNT_EMAIL_ADRES   as EMAIL_ADRES,
                AREA_NO               as AREA_NO,
                ENTRPRS_MIDDLE_TELNO  as MIDDLE_TELNO,
                ENTRPRS_END_TELNO     as END_TELNO,
                ''                    as MOBLPHON_NO,
                GROUP_ID              as GROUP_ID,
                ENTRPRS_MBER_STTUS    as STTUS,
                SBSCRB_DE             as SBSCRB_DE
            FROM    COMTNENTRPRSMBER
            WHERE 1=1
        <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
            AND ENTRPRS_MBER_STTUS LIKE  #{sbscrbSttus}
        </if>
        <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
        <if test="searchCondition == 0">
            AND ENTRPRS_MBER_ID LIKE '%' || #{searchKeyword} || '%'
        </if>
        <if test="searchCondition == 1">
            AND APPLCNT_NM LIKE '%' ||#{searchKeyword}|| '%'
        </if>
        </if>
        <![CDATA[
        ORDER BY 13 DESC
        ) TB ) Z WHERE rn BETWEEN #{firstIndex} + 1 AND #{firstIndex} + #{recordCountPerPage}
        ]]>
    </select>
    
    
        <select id="selectEntrprsMberListTotCnt" resultType="int">
            SELECT COUNT(1) totcnt
            FROM    COMTNENTRPRSMBER
	        WHERE 1=1
        <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
            AND ENTRPRS_MBER_STTUS LIKE  #{sbscrbSttus}
        </if>
        <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
        <if test="searchCondition == 0">
            AND ENTRPRS_MBER_ID LIKE '%' || #{searchKeyword} || '%'
        </if>
        <if test="searchCondition == 1">
            AND APPLCNT_NM LIKE '%' || #{searchKeyword} || '%'
        </if>
        </if>
    </select>
    
    <update id="updateLockIncorrect">
            UPDATE COMTNENTRPRSMBER 
 			   SET  LOCK_AT = NULL 
			     ,  LOCK_CNT  = NULL 
			     ,  LOCK_LAST_PNTTM = NULL 
            WHERE  ESNTL_ID  = #{uniqId}
    </update>

</mapper>