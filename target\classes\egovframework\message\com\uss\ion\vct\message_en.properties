# Vacation Management comUssIonVct

# EgovVcatnManage.xml
comUssIonVct.vcatnManage.validate.vcatnSe = Vacation breakdown
comUssIonVct.vcatnManage.validate.bgnde = Start Date
comUssIonVct.vcatnManage.validate.endde = End date
comUssIonVct.vcatnManage.validate.vcatnResn = Reason for vacation
comUssIonVct.vcatnManage.validate.sanctnDtNm = Certified

#common
comUssIonVct.common.validate.bgnde = Vacation start date does not exist. Check your vacation dates.
comUssIonVct.common.validate.endde = Vacation end date does not exist. Check your vacation dates.
comUssIonVct.common.validate.vcatnSe = Vacation Date The start date of the search term is later than the end date. Check your vacation dates.
comUssIonVct.common.calendar = calendar
comUssIonVct.common.endde = End date
comUssIonVct.common.noonSe1 = AM
comUssIonVct.common.noonSe2 = PM
comUssIonVct.common.noonSe = classpication
comUssIonVct.common.applcntNm = Applicant
comUssIonVct.common.orgnztNm = belongs
comUssIonVct.common.occrncYrycCo = Year of occurrence
comUssIonVct.common.useYrycCo = Annual use
comUssIonVct.common.remndrYrycCo = Remaining annual
comUssIonVct.common.vcatnSe = Vacation breakdown
comUssIonVct.common.startDate = Start date
comUssIonVct.common.endDate = End date
comUssIonVct.common.vcatnResn = Reason for vacation
comUssIonVct.common.infrmlSanctnRegist = Account holder
comUssIonVct.common.vcatnTotalInfo = Applicant Annual Information
comUssIonVct.common.sanctnDtNm = Approver
comUssIonVct.common.agree = Approval
comUssIonVct.common.disagree = companion
comUssIonVct.common.inputSuccess = Success
comUssIonVct.common.validate.vacationSelectError = Error specifying vacation date - Please check the start / end date of the vacation.
comUssIonVct.common.validate.thatYearOnly = Annual leave is only available for that year.
comUssIonVct.common.validate.vacationFail = Failure to register for annual leave (lack of remaining annual leave)
comUssIonVct.common.validate.halfVacationFail = Failed to register for Semi-Leave (lack of remaining annual leave)
comUssIonVct.common.validate.duplicate = Duplicate vacation date. Please confirm your vacation date.

# EgovVcatnManageList.jsp
comUssIonVct.vcatnManageList.title = Vacation Management List
comUssIonVct.vcatnManageList.userNm = username
comUssIonVct.vcatnManageList.vctYr = Leave Year
comUssIonVct.vcatnManageList.selectedAll = All
comUssIonVct.vcatnManageList.year = year
comUssIonVct.vcatnManageList.vcatnSeNm = Vacation breakdown
comUssIonVct.vcatnManageList.bgnde = Start Date
comUssIonVct.vcatnManageList.endde = End Date
comUssIonVct.vcatnManageList.vcatnResn = Reason for vacation
comUssIonVct.vcatnManageList.confmAt = Category
comUssIonVct.vcatnManageList.sanctnerNm = Approver
comUssIonVct.vcatnManageList.confmAt.A = Applying
comUssIonVct.vcatnManageList.confmAt.C = Agree
comUssIonVct.vcatnManageList.confmAt.R = Disagree
comUssIonVct.vcatnManageList.validate.guide =\u203b 902.The annual occurrence of personal annual management must be registered before it can be used.
comUssIonVct.vcatnManageList.validate.makeVcatn = There is no vacation. Personal vacation confirmation is required.
comUssIonVct.vcatnManageList.validate.access = Admin Role and organization ID are required for detailed screen access.
comUssIonVct.vcatnManageList.validate.move = \u203b Go to personal annual management components for personal vacation registration for vacation use

# EgovVcatnRegist.jsp
comUssIonVct.vcatnRegist.title = Leave application
comUssIonVct.vcatnRegist.vcatnAplyr = Leave Applicant

# EgovVcatnUpdt.jsp
comUssIonVct.vcatnUpdt.title = Edit vacation
comUssIonVct.vcatnUpdt.validate.length = The reason for the vacation reason entry exceeds 200 characters. If you use special characters('<','>','&','\\"', '\'') may be overridden by replacing the characters with ('&lt;','&gt;','&amp;','&quot;','&apos;')
comUssIonVct.vcatnUpdt.vcatnAplyr = Leave Applicant

# EgovVcatnManageDetail.jsp
comUssIonVct.vcatnManageDetail.title = Vacation Detail Views
comUssIonVct.vcatnManageDetail.vcatnApply = Leave application

# EgovVcatnConfm.jsp
comUssIonVct.vcatnConfm.title = Leave Approval
comUssIonVct.vcatnConfm.submit = Transfer
comUssIonVct.vcatnConfm.validate.length = The reason for the vacation reason entry exceeds 200 characters. If you use special characters('<','>','&','\\"', '\'') may be overridden by replacing the characters with ('&lt;','&gt;','&amp;','&quot;','&apos;')

# EgovVcatnConfmList.jsp
comUssIonVct.vcatnConfmList.title = Vacation Approval List
comUssIonVct.vcatnConfmList.validate.searchYear = You can not view the month in the entire year. Please select the year.
comUssIonVct.vcatnConfmList.searchConfmAt = Progress breakdown
comUssIonVct.vcatnConfmList.selectedAll = All
comUssIonVct.vcatnConfmList.searchConfmAt.A = Apply
comUssIonVct.vcatnConfmList.searchConfmAt.vcatnDay = Vacation Date
comUssIonVct.vcatnConfmList.month = month
comUssIonVct.vcatnConfmList.bgnde = Start Date
comUssIonVct.vcatnConfmList.endde = End Date
comUssIonVct.vcatnConfmList.confmAt.agreeProcess = Process Approval
comUssIonVct.vcatnConfmList.confmAt.detail = View details
comUssIonVct.vcatnConfmList.vcatnManageList = Vacation approval management list
comUssIonVct.vcatnConfmList.searchYear = Year
comUssIonVct.vcatnConfmList.search = Year