#\uc57d\ub3c4\uad00\ub9ac 
#EgovRoughMapList.jsp
comUssIonRmm.roughMapList.title=\uc57d\ub3c4 \ubaa9\ub85d
comUssIonRmm.roughMapList.roughMapSj=\uc57d\ub3c4\uc81c\ubaa9
comUssIonRmm.roughMapList.roughMapAddress=\uc57d\ub3c4\uc8fc\uc18c
comUssIonRmm.roughMapList.roughMapNo=\uc21c\ubc88
comUssIonRmm.roughMapList.roughMapDetailAddress=\uc57d\ub3c4\uc0c1\uc138\uc8fc\uc18c
comUssIonRmm.roughMapList.lastUpdtPnttm=\ub4f1\ub85d\uc77c\uc790
comUssIonRmm.roughMapList.info=\uc57d\ub3c4\uad00\ub9ac\ub294 Daum \uc9c0\ub3c4 API \ud0a4\ub97c \ubc1c\uae09\ubc1b\uc544\uc57c \ud569\ub2c8\ub2e4.
comUssIonRmm.roughMapList.info2=Daum \uc9c0\ub3c4 API \uac00\uc774\ub4dc

#EgovRoughMapInfoRegist.jsp
comUssIonRmm.roughMapInfoRegist.title=\uc57d\ub3c4 \ub4f1\ub85d
comUssIonRmm.roughMapInfoRegist.validate.error=\uc5d0\ub7ec\ubc1c\uc0dd, \uc5d0\ub7ec\ucf54\ub4dc
comUssIonRmm.roughMapInfoRegist.validate.msg=\uba54\uc2dc\uc9c0
comUssIonRmm.roughMapInfoRegist.validate.noAddress=\uc57d\ub3c4\uc8fc\uc18c\uac00 \uc5c6\uc2b5\ub2c8\ub2e4.
comUssIonRmm.roughMapInfoRegist.validate.searchAddress=\uc57d\ub3c4\uc8fc\uc18c\ub97c \uac80\uc0c9\ud574\uc8fc\uc138\uc694.
comUssIonRmm.roughMapInfoRegist.express=\uc57d\ub3c4\ud45c\uc2dc
comUssIonRmm.roughMapInfoRegist.input=\uc57d\ub3c4\uc785\ub825

#EgovRoughMapInfoDetail.jsp
comUssIonRmm.roughMapInfoDetail.title=\uc57d\ub3c4 \uc0c1\uc138
comUssIonRmm.roughMapInfoDetail.findRoughMap=\uae38\ucc3e\uae30
comUssIonRmm.roughMapInfoDetail.magnifyRoughMap=\ud06c\uac8c\ubcf4\uae30

#EgovRoughMapUpdt.jsp
comUssIonRmm.roughMapUpdt.title=\uc57d\ub3c4 \uc218\uc815