#DB Monitoring#
comUtlSysDbm.dbMntrngList.title = DB Service Monitoring List
comUtlSysDbm.dbMntrngDetail.title = DB Service Monitoring Details
comUtlSysDbm.dbMntrngDetail.summary = Provides details about registered DB service monitoring.
comUtlSysDbm.dbMntrngModify.title = Modify DB Service Monitoring
comUtlSysDbm.dbMntrngModify.summary = DB service monitoring modification function is provided.
comUtlSysDbm.dbMntrngRegist.title = Register DB Service Monitoring
comUtlSysDbm.dbMntrngLogList.title = DB Service Monitoring Log List
comUtlSysDbm.dbMntrngLogDetail.title = DB Service Monitoring Log Details
comUtlSysDbm.dbMntrngLogDetail.summary = Provides details about the registered DB ServiceMonitor.
         
comUtlSysDbm.dbMntrng.period = Period
comUtlSysDbm.dbMntrng.seq = No
comUtlSysDbm.dbMntrng.logID = LogID
comUtlSysDbm.dbMntrng.dataSourceName = Data Source
comUtlSysDbm.dbMntrng.serverName = Server Name
comUtlSysDbm.dbMntrng.dbms = DBMS
comUtlSysDbm.dbMntrng.managerName = Manager Name
comUtlSysDbm.dbMntrng.managerEmail = Email
comUtlSysDbm.dbMntrng.createdDateTime = Created Datetime
comUtlSysDbm.dbMntrng.status = Status
comUtlSysDbm.dbMntrng.checkSQL = Check SQL
comUtlSysDbm.dbMntrng.monitoringDatetime = Monitoring Datetime
comUtlSysDbm.dbMntrng.logInfo = Log Info.

comUtlSysDbm.dbMntrng.searchStartDate = Search start date
comUtlSysDbm.dbMntrng.searchStartHour = Search start hour
comUtlSysDbm.dbMntrng.searchStartMin = Search start minute
comUtlSysDbm.dbMntrng.searchEndDate = Search end date
comUtlSysDbm.dbMntrng.searchEndHour = Search end hour
comUtlSysDbm.dbMntrng.searchEndMin = Search end minute