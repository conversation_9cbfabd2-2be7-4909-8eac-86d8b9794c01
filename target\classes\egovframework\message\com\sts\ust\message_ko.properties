#   \uc0ac\uc6a9\uc790 \ud1b5\uacc4 \uac80\uc0c9
comStsUst.userStats.title = \uc0ac\uc6a9\uc790 \ud1b5\uacc4 \uac80\uc0c9
comStsUst.userStats.fromDate = \uc2dc\uc791\uc77c\uc790
comStsUst.userStats.toDate = \uc885\ub8cc\uc77c\uc790
comStsUst.userStats.periodKind = \uae30\uac04\uad6c\ubd84
comStsUst.userStats.select = \uc120\ud0dd
comStsUst.userStats.byYear = \uc5f0\ub3c4\ubcc4
comStsUst.userStats.byMonth = \uc6d4\ubcc4
comStsUst.userStats.byDay = \uc77c\ubcc4

comStsUst.userStats.statKind = \ud1b5\uacc4\uad6c\ubd84
comStsUst.userStats.selectPlease = \uc120\ud0dd\ud558\uc138\uc694
comStsUst.userStats.statKind1 = \ud68c\uc6d0\uc720\ud615\ubcc4
comStsUst.userStats.statKind2 = \ud68c\uc6d0\uc0c1\ud0dc\ubcc4
comStsUst.userStats.statKind3 = \uc131\ubcc4

comStsUst.userStats.statResult = \uc0ac\uc6a9\uc790 \ud1b5\uacc4 \uacb0\uacfc

comStsUst.userStats.subTitle1 = \uadf8\ub798\ud504 (\ub2e8\uc704, \uba85)
comStsUst.userStats.subTitle2 = \ud14d\uc2a4\ud2b8 (\ub2e8\uc704, \uba85)

comStsUst.validate.fromDateCheck = \uae30\uac04 \uc2dc\uc791\uc77c\uc790\ub97c \uc785\ub825\ud558\uc138\uc694
comStsUst.validate.toDateCheck = \uae30\uac04 \uc885\ub8cc\uc77c\uc790\ub97c \uc785\ub825\ud558\uc138\uc694
comStsUst.validate.periodKindCheck = \uae30\uac04 \uad6c\ubd84\uc744 \uc120\ud0dd\ud558\uc138\uc694
comStsUst.validate.statKindCheck = \ud1b5\uacc4 \uad6c\ubd84\uc744 \uc120\ud0dd\ud558\uc138\uc694
