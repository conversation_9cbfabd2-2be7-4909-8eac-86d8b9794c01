<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="authorGroupDAO">


    <resultMap id="authorGroup" type="egovframework.com.sec.rgm.service.AuthorGroupVO">
        <result property="userId" column="USER_ID"/>
        <result property="userNm" column="USER_NM"/>
        <result property="groupId" column="GROUP_ID"/>
        <result property="mberTyCode" column="MBER_TY_CODE"/>
        <result property="mberTyNm" column="MBER_TY_NM"/>
        <result property="authorCode" column="AUTHOR_CODE"/>
        <result property="regYn" column="REG_YN"/>
        <result property="uniqId" column="ESNTL_ID"/>
    </resultMap>

    <select id="selectAuthorGroupList" parameterType="egovframework.com.sec.rgm.service.AuthorGroupVO" resultMap="authorGroup">
             
            SELECT  * 
              FROM  (
            SELECT ROWNUM RNUM, ALL_LIST.* 
              FROM  (
            SELECT A.USER_ID, 
                   A.USER_NM, 
                   A.GROUP_ID,
                   A.MBER_TY_CODE,
                   (SELECT CODE_NM
                      FROM COMTCCMMNDETAILCODE 
                     WHERE CODE_ID = 'COM012'
                       AND CODE = A.MBER_TY_CODE
                       AND USE_AT = 'Y') AS MBER_TY_NM,
                   B.AUTHOR_CODE, 
                   (CASE WHEN B.SCRTY_DTRMN_TRGET_ID IS NULL THEN 'N'
                         ELSE 'Y' 
                     END) AS REG_YN,
                    ESNTL_ID
              FROM (SELECT MBER_ID USER_ID, MBER_NM USER_NM, GROUP_ID, 'USR01' MBER_TY_CODE, ESNTL_ID
                      FROM COMTNGNRLMBER
                     UNION ALL
                    SELECT ENTRPRS_MBER_ID USER_ID, CMPNY_NM USER_NM, GROUP_ID, 'USR02' MBER_TY_CODE, ESNTL_ID 
                      FROM COMTNENTRPRSMBER
                     UNION ALL
                    SELECT EMPLYR_ID USER_ID, USER_NM USER_NM, GROUP_ID, 'USR03' MBER_TY_CODE, ESNTL_ID 
                      FROM COMTNEMPLYRINFO ) A LEFT OUTER JOIN COMTNEMPLYRSCRTYESTBS B
                ON A.ESNTL_ID = B.SCRTY_DTRMN_TRGET_ID
             WHERE 1 = 1    
               
            <if test="searchKeyword != null and searchKeyword != ''">
                <if test="searchCondition == 1">AND
                       A.USER_ID LIKE '%'||#{searchKeyword}||'%'
                </if>
	            <if test="searchCondition == 2">AND
                       A.USER_NM LIKE '%'||#{searchKeyword}||'%'
	            </if>
	            <if test="searchCondition == 3">AND
	                   A.GROUP_ID = #{searchKeyword}
	            </if>
            </if>
             <![CDATA[
                    ) ALL_LIST
                    )
             WHERE  RNUM  > #{firstIndex}
               AND  RNUM <= #{firstIndex} + #{recordCountPerPage}
               ]]>

               
    </select>

	<insert id="insertAuthorGroup" parameterType="egovframework.com.sec.rgm.service.AuthorGroup">
		
			INSERT INTO COMTNEMPLYRSCRTYESTBS 
				  ( SCRTY_DTRMN_TRGET_ID
                  , MBER_TY_CODE
				  , AUTHOR_CODE)
		   VALUES ( #{uniqId}
                  , #{mberTyCode}
                  , #{authorCode})
		
	</insert>

	<update id="updateAuthorGroup" parameterType="egovframework.com.sec.rgm.service.AuthorGroup">
		
			UPDATE COMTNEMPLYRSCRTYESTBS 
			   SET MBER_TY_CODE=#{mberTyCode}
                 , AUTHOR_CODE=#{authorCode}
		  	 WHERE SCRTY_DTRMN_TRGET_ID=#{uniqId}
		
	</update>

	<delete id="deleteAuthorGroup">
		
			DELETE FROM COMTNEMPLYRSCRTYESTBS 
			 WHERE SCRTY_DTRMN_TRGET_ID=#{uniqId}
		
	</delete>

	<select id="selectAuthorGroupListTotCnt" parameterType="egovframework.com.sec.rgm.service.AuthorGroupVO" resultType="int">

            SELECT COUNT(*) AS totcnt
              FROM (SELECT MBER_ID USER_ID, MBER_NM USER_NM, GROUP_ID, 'USR01' MBER_TY_CODE 
                      FROM COMTNGNRLMBER
                     UNION ALL
                    SELECT ENTRPRS_MBER_ID USER_ID, CMPNY_NM USER_NM, GROUP_ID, 'USR02' MBER_TY_CODE 
                      FROM COMTNENTRPRSMBER
                     UNION ALL
                    SELECT EMPLYR_ID USER_ID, USER_NM USER_NM, GROUP_ID, 'USR03' MBER_TY_CODE 
                      FROM COMTNEMPLYRINFO ) A LEFT OUTER JOIN COMTNEMPLYRSCRTYESTBS B
                ON A.USER_ID = B.SCRTY_DTRMN_TRGET_ID
             WHERE 1 = 1   
            <if test="searchKeyword != null and searchKeyword != ''">
                <if test="searchCondition == 1">AND
                       A.USER_ID LIKE '%'||#{searchKeyword}||'%'
                </if>
                <if test="searchCondition == 2">AND
                       A.USER_NM LIKE '%'||#{searchKeyword}||'%'
                </if>
                <if test="searchCondition == 3">AND
                       A.GROUP_ID = #{searchKeyword}
                </if>
            </if>
	</select>

</mapper>