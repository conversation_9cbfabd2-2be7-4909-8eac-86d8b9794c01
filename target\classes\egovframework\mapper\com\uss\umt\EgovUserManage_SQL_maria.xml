<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="userManageDAO">

    <select id="selectUserList_S" resultType="egovMap">
SELECT 
    uniqId, userTy, userId , userNm, emailAdres, areaNo, middleTelno, endTelno, moblphonNo, groupId, sttus, sbscrbDe
FROM(
    SELECT
        ESNTL_ID              as uniqId,
        'USR03'               as userTy,
        EMPLYR_ID             as userId,
        USER_NM               as userNm,
        EMAIL_ADRES           as emailAdres,
        AREA_NO               as areaNo,
        HOUSE_MIDDLE_TELNO    as middleTelno,
        HOUSE_END_TELNO       as endTelno,
        MBTLNUM               as moblphonNo,
        G<PERSON>UP_ID              as groupId,
        EMPLYR_STTUS_CODE     as sttus,
        DATE_FORMAT(SBSCRB_DE,'%Y-%m-%d') as sbscrbDe
    FROM    COMTNEMPLYRINFO
    ) A 
        WHERE 1=1
	     <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
          AND sttus LIKE  #{sbscrbSttus}
		</if> 
        <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
        <if test="searchCondition == 0">AND
            userId LIKE '%' #{searchKeyword} '%'
        </if>
        <if test="searchCondition == 1">AND
            userNm LIKE '%' #{searchKeyword} '%'
        </if>
        </if>
        
        ORDER BY sbscrbDe DESC
        LIMIT #{recordCountPerPage} OFFSET #{firstIndex}
    </select>
    
    <select id="selectUserListTotCnt_S" resultType="int">
            SELECT COUNT(1) totcnt
            FROM(
            SELECT
                ESNTL_ID               uniqId,
                'USR03'               userTy,
                EMPLYR_ID             userId,
                USER_NM             userNm,
                EMAIL_ADRES           emailAdres,
                AREA_NO               areaNo,
                HOUSE_MIDDLE_TELNO      middleTelno,
                HOUSE_END_TELNO         endTelno,
                MBTLNUM           moblphonNo,
                GROUP_ID              groupId,
                EMPLYR_STTUS_CODE     sttus,
                SBSCRB_DE             sbscrbDe
            FROM    COMTNEMPLYRINFO
            ) A
        WHERE 1=1
	        <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
            AND sttus LIKE  #{sbscrbSttus}
            </if>
            <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
            <if test="searchCondition == 0">AND
                userId LIKE '%' #{searchKeyword} '%'
            </if>
            <if test="searchCondition == 1">AND
                userNm LIKE '%' #{searchKeyword} '%'
            </if>
            </if>
    </select>
    
    <insert id="insertUser_S">
        
            INSERT INTO COMTNEMPLYRINFO 
                (   ESNTL_ID             ,
                    EMPLYR_ID           ,
                    USER_NM           ,
                    PASSWORD            ,
                    PASSWORD_HINT       ,
                    PASSWORD_CNSR       ,
                    EMPL_NO             ,
                    IHIDNUM             ,
                    SEXDSTN_CODE        ,
                    BRTHDY                ,
                    AREA_NO             ,
                    HOUSE_MIDDLE_TELNO    ,
                    HOUSE_END_TELNO       ,
                    FXNUM               ,
                    HOUSE_ADRES           ,
                    DETAIL_ADRES        ,
                    ZIP                 ,
                    OFFM_TELNO          ,
                    MBTLNUM         ,
                    EMAIL_ADRES         ,
                    OFCPS_NM            ,
                    GROUP_ID            ,
                    ORGNZT_ID           ,
                    PSTINST_CODE          ,
                    EMPLYR_STTUS_CODE   ,
                    SBSCRB_DE           ,
                    CRTFC_DN_VALUE      ,
                    CHG_PWD_LAST_PNTTM        )
            VALUES (
                    #{uniqId}            ,
                    #{emplyrId}          ,
                    #{emplyrNm}          ,
                    #{password}          ,
                    #{passwordHint}      ,
                    #{passwordCnsr}      ,                   
                    #{emplNo}            ,
                    #{ihidnum}           ,
                    #{sexdstnCode}       ,
                    #{brth}              ,
                    #{areaNo}            ,
                    #{homemiddleTelno}   ,
                    #{homeendTelno}      ,
                    #{fxnum}             ,
                    #{homeadres}         ,
                    #{detailAdres}       ,
                    #{zip}               ,
                    #{offmTelno}         ,
                    #{moblphonNo}        ,
                    #{emailAdres}        ,
                    #{ofcpsNm}           ,
                    #{groupId}           ,
                    #{orgnztId}          ,
                    #{insttCode}         ,
                    #{emplyrSttusCode}   ,
                    sysdate()            ,
                    #{subDn}             ,
                    sysdate()            )
        
    </insert>
    
    <delete id="deleteUser_S">
        
            DELETE FROM COMTNEMPLYRINFO 
            WHERE ESNTL_ID=#{delId}
        
    </delete>
    
    <select id="selectUser_S" resultType="egovframework.com.uss.umt.service.UserManageVO">
        
            SELECT
                ESNTL_ID           uniqId            ,
                'USR03'           userTy,
            EMPLYR_ID         emplyrId          ,
            USER_NM         emplyrNm          ,
            PASSWORD          password          ,
            PASSWORD_HINT     passwordHint      ,
            PASSWORD_CNSR     passwordCnsr      ,
            EMPL_NO           emplNo            ,
            IHIDNUM           ihidnum           ,
            SEXDSTN_CODE      sexdstnCode       ,
            BRTHDY              brth              ,
            AREA_NO           areaNo            ,
            HOUSE_MIDDLE_TELNO  homemiddleTelno   ,
            HOUSE_END_TELNO     homeendTelno      ,
            FXNUM             fxnum             ,
            HOUSE_ADRES         homeadres         ,
            DETAIL_ADRES      detailAdres       ,
            ZIP               zip               ,
            OFFM_TELNO        offmTelno         ,
            MBTLNUM       moblphonNo        ,
            EMAIL_ADRES       emailAdres        ,
            OFCPS_NM          ofcpsNm           ,
            GROUP_ID          groupId           ,
            ORGNZT_ID         orgnztId          ,
            PSTINST_CODE        insttCode         ,
            EMPLYR_STTUS_CODE emplyrSttusCode   , 
            DATE_FORMAT(SBSCRB_DE,'%Y-%m-%d') AS sbscrbDe,
            CRTFC_DN_VALUE            subDn,
            LOCK_AT        				 lockAt
        FROM COMTNEMPLYRINFO
            WHERE ESNTL_ID=#{uniqId}
        
    </select>
    
    <update id="updateUser_S">
        
            UPDATE COMTNEMPLYRINFO 
            SET EMPLYR_ID           = #{emplyrId}        , 
                USER_NM           = #{emplyrNm}        ,
                PASSWORD_HINT       = #{passwordHint}    ,
                PASSWORD_CNSR       = #{passwordCnsr}    ,
                EMPL_NO             = #{emplNo}          ,
                IHIDNUM             = #{ihidnum}         ,
                SEXDSTN_CODE        = #{sexdstnCode}     ,
                BRTHDY                = #{brth}            ,
                AREA_NO             = #{areaNo}          ,
                HOUSE_MIDDLE_TELNO    = #{homemiddleTelno} ,
                HOUSE_END_TELNO       = #{homeendTelno}    ,
                FXNUM               = #{fxnum}           ,
                HOUSE_ADRES           = #{homeadres}       ,
                DETAIL_ADRES        = #{detailAdres}     ,
                ZIP                 = #{zip}             ,
                OFFM_TELNO          = #{offmTelno}       ,
                MBTLNUM         = #{moblphonNo}      ,
                EMAIL_ADRES         = #{emailAdres}      ,
                OFCPS_NM            = #{ofcpsNm}         ,
                GROUP_ID            = #{groupId}         ,
                ORGNZT_ID           = #{orgnztId}        ,
                PSTINST_CODE          = #{insttCode}       ,
                EMPLYR_STTUS_CODE   = #{emplyrSttusCode} ,
                CRTFC_DN_VALUE              = #{subDn}
            WHERE ESNTL_ID = #{uniqId}
        
    </update>
    
    <insert id="insertUserHistory_S">
        
            INSERT INTO COMTHEMPLYRINFOCHANGEDTLS 
                (   EMPLYR_ID           ,
                    EMPL_NO             ,
                    SEXDSTN_CODE        ,
                    BRTHDY                ,
                    AREA_NO             ,
                    HOUSE_MIDDLE_TELNO    ,
                    HOUSE_END_TELNO       ,
                    FXNUM               ,
                    HOUSE_ADRES           ,
                    DETAIL_ADRES        ,
                    ZIP                 ,
                    OFFM_TELNO          ,
                    MBTLNUM         ,
                    EMAIL_ADRES         ,
                    GROUP_ID            ,
                    ORGNZT_ID           ,
                    PSTINST_CODE          ,
                    EMPLYR_STTUS_CODE   ,
                    ESNTL_ID             ,
                    CHANGE_DE           )
            SELECT 
                    EMPLYR_ID           ,
                    EMPL_NO             ,
                    SEXDSTN_CODE        ,
                    BRTHDY                ,
                    AREA_NO             ,
                    HOUSE_MIDDLE_TELNO    ,
                    HOUSE_END_TELNO       ,
                    FXNUM               ,
                    HOUSE_ADRES           ,
                    DETAIL_ADRES        ,
                    ZIP                 ,
                    OFFM_TELNO          ,
                    MBTLNUM         ,
                    EMAIL_ADRES         ,
                    GROUP_ID            ,
                    ORGNZT_ID           ,
                    PSTINST_CODE          ,
                    EMPLYR_STTUS_CODE   ,
                    ESNTL_ID             ,
                    now()
            FROM COMTNEMPLYRINFO
            WHERE ESNTL_ID = #{uniqId}
        
    </insert>
    
    <select id="checkIdDplct_S" resultType="int">
        
            SELECT COUNT(1) usedCnt
                FROM(
                SELECT
                    EMPLYR_ID             userId
                FROM    COMTNEMPLYRINFO
                UNION ALL
                SELECT
                    ENTRPRS_MBER_ID        userId
                FROM    COMTNENTRPRSMBER
                UNION ALL
                SELECT
                    MBER_ID               userId
                FROM    COMTNGNRLMBER
                ) A
            WHERE userId = #{checkId}
        
    </select>
    
    <update id="updatePassword_S">
        
            UPDATE COMTNEMPLYRINFO 
            SET 
                   PASSWORD   =  #{password}
                 , CHG_PWD_LAST_PNTTM = sysdate()
            WHERE  ESNTL_ID  = #{uniqId}
        
    </update>
    
    <select id="selectPassword_S" resultType="egovframework.com.uss.umt.service.UserManageVO">
        
            SELECT
                    PASSWORD          password 
            FROM    COMTNEMPLYRINFO
            WHERE   ESNTL_ID=#{uniqId}
        
    </select>
    
    <update id="updateLockIncorrect">
            UPDATE COMTNEMPLYRINFO 
 			   SET  LOCK_AT = NULL 
			     ,  LOCK_CNT  = NULL 
			     ,  LOCK_LAST_PNTTM = NULL 
            WHERE  ESNTL_ID  = #{uniqId}
    </update>

</mapper>