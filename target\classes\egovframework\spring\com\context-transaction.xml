<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
						http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
						http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd">

	<bean id="txManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="egov.dataSource"/>
	</bean>
	  
	<tx:advice id="txAdvice" transaction-manager="txManager">
	   <tx:attributes>	 
	       <tx:method name="*" propagation="REQUIRED" rollback-for="Exception"/>
	   </tx:attributes>
	</tx:advice>
    
	<aop:config>
		<aop:pointcut id="requiredTx" expression="execution(* egovframework.com..*Impl.*(..)) or
												  execution(* org.egovframe.rte.fdl.excel.impl.*Impl.*(..))"/>
		<aop:advisor advice-ref="txAdvice" pointcut-ref="requiredTx" />
	</aop:config>

</beans>
