#Twitter connection#
ussIonTir.twitterMain.twitterMain=Twitter application registration
ussIonTir.twitterMain.version= Free version / OAuth 1.0 Authentication
ussIonTir.twitterMain.app=Log in to the Twitter site.
ussIonTir.twitterMain.buttonClick=Click the button and register the Twitter application service on the registration page.
ussIonTir.twitterMain.notice=Notes on registration
ussIonTir.twitterMain.returnUrl=(The URL returned after issuing the key)
ussIonTir.twitterMain.key=It stores the authorized consumer key and consumer secret.
ussIonTir.twitterMain.auth=Auth

ussIonTir.twitterPopup.twitterPopup=Request Twitter Certification
ussIonTir.twitterPopup.consumerKey=Please enter your Consumer key!
ussIonTir.twitterPopup.consumerSecret=Please enter your Consumer Secret!
ussIonTir.twitterPopup.twitterPopupKey=Twitter authentication request - Enter authentication key
ussIonTir.twitterPopup.saveKey=Storing Consumer key / Consumer secret key value
ussIonTir.twitterPopup.send=Send

ussIonTir.twitterTrnsmit.twitterTrnsmit=Twitter transmission (registration)
ussIonTir.twitterTrnsmit.validate.id=Please enter your Twitter ID!
ussIonTir.twitterTrnsmit.validate.password=Please enter your Twitter password (PW)!
ussIonTir.twitterTrnsmit.linklUser=Linked User
ussIonTir.twitterTrnsmit.validate.send=Send Twitter created?
ussIonTir.twitterTrnsmit.twitterText=Twitter Text
ussIonTir.twitterTrnsmit.send=Send

ussIonTir.twitterRecptn.twitterRecptn=Receive Twitter
ussIonTir.twitterRecptn.validate.number=Please enter a number!
ussIonTir.twitterRecptn.validate.onlyNumber=Only numbers can be entered
ussIonTir.twitterRecptn.twitterProfileImageURL=Profile image
ussIonTir.twitterRecptn.text=Text

ussIonTir.twitterTrnsmitResult.twitterTrnsmitResult=Twitter Submit (registration) Check result
ussIonTir.twitterTrnsmitResult.id=Registration ID
ussIonTir.twitterTrnsmitResult.screenName=Screen Name
ussIonTir.twitterTrnsmitResult.name=Nickname
ussIonTir.twitterTrnsmitResult.url=URL
ussIonTir.twitterTrnsmitResult.text=Text
ussIonTir.twitterTrnsmitResult.profileImage=Profile Image
ussIonTir.twitterTrnsmitResult.createdAt=Registration date
ussIonTir.twitterTrnsmitResult.isFavorited=favorite
ussIonTir.twitterTrnsmitResult.isRetweet=retweet
ussIonTir.twitterTrnsmitResult.isTruncated=runcated
ussIonTir.twitterTrnsmitResult.confirm=Confirm
ussIonTir.twitterTrnsmitResult.delete=DELETE
ussIonTir.twitterTrnsmitResult.delete.confirmMessage=Are you sure you want to delete the Tweet you created?
ussIonTir.twitterTrnsmitResult.delete.success=Delete Success
ussIonTir.twitterTrnsmitResult.delete.unsuccess=Delete Unsuccess
ussIonTir.twitterTrnsmitResult.reWrite=REWRITE
ussIonTir.twitterTrnsmitResult.viewTweet=VIEW TWEET