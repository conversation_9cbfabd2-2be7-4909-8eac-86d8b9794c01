#Server Resource Monitoring #

ultSysScm.mntrngServer.Title=Server Resource Monitoring

ultSysScm.mntrngServerList.Title=Server Resource Monitoring
ultSysScm.mntrngServerList.ServerName=Server H/W Name

ultSysScm.mntrngServerList.ServerId=Server H/W ID
ultSysScm.mntrngServerList.ServerIp=Server H/W IP
ultSysScm.mntrngServerList.ServerEmail=E-Mail

ultSysScm.mntrngServerList.btnLog=Log


#Server Resource Monitoring Log List#
ultSysScm.mntrngResrceServerList.Title=Server Resource Monitoring Log
ultSysScm.mntrngResrceServerList.scServerName=Server H/W Nm
ultSysScm.mntrngResrceServerList.scBetween=between
ultSysScm.mntrngResrceServerList.scFromDate=search start date
ultSysScm.mntrngResrceServerList.scToDate=search start date

ultSysScm.mntrngResrceServerList.listServerName=Server H/W Name
ultSysScm.mntrngResrceServerList.listServerIp=Server H/W IP
ultSysScm.mntrngResrceServerList.listCpuUse=CPU use
ultSysScm.mntrngResrceServerList.lisMemoryUse=Memory use
ultSysScm.mntrngResrceServerList.listServerStatus=Service status
ultSysScm.mntrngResrceServerList.listCreateDatetime=Create date

ultSysScm.mntrngResrceServerList.validate.datatFail=Date format is invalid!
ultSysScm.mntrngResrceServerList.validate.datatCheck=Start date can not be greater than end date!

#Server Resource Monitoring Log Detail#
ultSysScm.mntrngServerDetail.Title=Server Resource Monitoring Log
ultSysScm.mntrngServerDetail.logId=log ID
ultSysScm.mntrngServerDetail.serverName=server H/W name
ultSysScm.mntrngServerDetail.serverIp=server H/W IP
ultSysScm.mntrngServerDetail.cpuUse=Cpu Use
ultSysScm.mntrngServerDetail.memoryUse=Memory use
ultSysScm.mntrngServerDetail.serviceStatus=Service status
ultSysScm.mntrngServerDetail.logInfo=Log info
ultSysScm.mntrngServerDetail.crateDate=Create date
