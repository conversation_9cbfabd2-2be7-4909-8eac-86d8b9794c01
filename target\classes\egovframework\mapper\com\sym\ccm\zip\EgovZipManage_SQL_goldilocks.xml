<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ZipManageDAO">

	<select id="selectZipList" parameterType="egovframework.com.sym.ccm.zip.service.ZipVO" resultType="egovMap">
		
			SELECT  *
			  FROM  (
			SELECT ROWNUM RNUM, ALL_LIST.*
			  FROM  (
			/* 구현 Sql */
			SELECT  ZIP
			     ,  SN
			     ,  CTPRVN_NM
			     ,  SIGNGU_NM
			     ,  EMD_NM
			     ,  LI_BULD_NM
			     ,  LNBR_DONG_HO
			  FROM  COMTCZIP
			 WHERE	1 = 1
		
			<if test="searchCondition == 1">AND
				ZIP LIKE '%' || #{searchKeyword} || '%'
			</if>
			<if test="searchCondition == 2">AND
				CTPRVN_NM LIKE '%' || #{searchKeyword} || '%'
			</if>
			<if test="searchCondition == 3">AND
				SIGNGU_NM LIKE '%' || #{searchKeyword} || '%'
			</if>
			<if test="searchCondition == 4">AND
				EMD_NM LIKE '%' || #{searchKeyword} || '%'
			</if>
			<if test="searchCondition == 5">AND
				LI_BULD_NM LIKE '%' || #{searchKeyword} || '%'
			</if>
		<![CDATA[
			/* 구현 Sql */
				    ) ALL_LIST
				    )
			 WHERE  RNUM  > #{firstIndex}
			   AND  RNUM <= #{firstIndex} + #{recordCountPerPage}
		]]>
	</select>

	<select id="selectZipListTotCnt" parameterType="egovframework.com.sym.ccm.zip.service.ZipVO" resultType="int">
		
			SELECT  COUNT(*) totcnt
			  FROM  COMTCZIP
			 WHERE	1 = 1
		
			<if test="searchCondition == 1">AND
				ZIP LIKE '%' || #{searchKeyword} || '%'
			</if>
			<if test="searchCondition == 2">AND
				CTPRVN_NM LIKE '%'||#{searchKeyword}||'%'
			</if>
			<if test="searchCondition == 3">AND
				SIGNGU_NM LIKE '%'||#{searchKeyword}||'%'
			</if>
			<if test="searchCondition == 4">AND
				EMD_NM LIKE '%'||#{searchKeyword}||'%'
			</if>
			<if test="searchCondition == 5">AND
				LI_BULD_NM LIKE '%'||#{searchKeyword}||'%'
			</if>
	</select>

	<select id="selectZipDetail" parameterType="egovframework.com.sym.ccm.zip.service.Zip" resultType="egovframework.com.sym.ccm.zip.service.Zip">
		
			SELECT  ZIP           zip
			     ,  SN            sn
			     ,  CTPRVN_NM     ctprvnNm
			     ,  SIGNGU_NM     signguNm
			     ,  EMD_NM        emdNm
			     ,  LI_BULD_NM    liBuldNm
			     ,  LNBR_DONG_HO  lnbrDongHo
			  FROM  COMTCZIP
             WHERE  ZIP = #{zip}
               AND  SN  = #{sn}
		
	</select>

	<insert id="insertZip">
		<selectKey keyProperty="sn" resultType="int" order="BEFORE">
		SELECT CASE WHEN MAX(SN)+1 IS NULL THEN 1
		       ELSE MAX(SN)+1 END AS sn 
			  FROM  COMTCZIP
	    </selectKey>

		
			INSERT
			  INTO  COMTCZIP
			     (  ZIP
			     ,  SN
			     ,  CTPRVN_NM
			     ,  SIGNGU_NM
			     ,  EMD_NM
			     ,  LI_BULD_NM
			     ,  LNBR_DONG_HO
			     ,  FRST_REGIST_PNTTM
			     ,  FRST_REGISTER_ID
			     ,  LAST_UPDT_PNTTM
			     ,  LAST_UPDUSR_ID
			     )
			VALUES
			     (  #{zip}
			     ,  #{sn}
			     ,  #{ctprvnNm}
			     ,  #{signguNm}
			     ,  #{emdNm}
			     ,  #{liBuldNm}
			     ,  #{lnbrDongHo}
			     ,  SYSDATE
			     ,  #{frstRegisterId}
			     ,  SYSDATE
			     ,  #{frstRegisterId}
			     )
		
	</insert>

	<insert id="insertExcelZip">
		
			INSERT
			  INTO  COMTCZIP
			     (  ZIP
			     ,  SN
			     ,  CTPRVN_NM
			     ,  SIGNGU_NM
			     ,  EMD_NM
			     ,  LI_BULD_NM
			     ,  LNBR_DONG_HO
			     ,  FRST_REGIST_PNTTM
			     ,  FRST_REGISTER_ID
			     ,  LAST_UPDT_PNTTM
			     ,  LAST_UPDUSR_ID
			     )
			VALUES
			     (  #{zip}
			     ,  #{sn}
			     ,  #{ctprvnNm}
			     ,  #{signguNm}
			     ,  #{emdNm}
			     ,  #{liBuldNm}
			     ,  #{lnbrDongHo}
			     ,  SYSDATE
			     ,  #{frstRegisterId}
			     ,  SYSDATE
			     ,  #{frstRegisterId}
			     )
		
	</insert>

	<update id="updateZip">
		
            UPDATE  COMTCZIP
               SET  CTPRVN_NM         = #{ctprvnNm}
                 ,  SIGNGU_NM         = #{signguNm}
                 ,  EMD_NM            = #{emdNm}
                 ,  LI_BULD_NM        = #{liBuldNm}
                 ,  LNBR_DONG_HO      = #{lnbrDongHo}
                 ,  LAST_UPDT_PNTTM = sysdate
                 ,  LAST_UPDUSR_ID    = #{lastUpdusrId}
             WHERE  ZIP               = #{zip}
               AND  SN                = #{sn}
		
	</update>

	<delete id="deleteZip">
		
            DELETE
              FROM  COMTCZIP
             WHERE  ZIP = #{zip}
		
	</delete>

	<delete id="deleteAllZip">
		
            DELETE
              FROM  COMTCZIP
		
	</delete>

</mapper>