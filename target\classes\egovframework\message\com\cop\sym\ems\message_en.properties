comCopSymEms.regist.title=Outgoing Mail
comCopSymEms.regist.summary={0} Posted typing the possible contents of registers by clicking the button.
comCopSymEms.regist.receiver = Receiver
comCopSymEms.regist.title2 = Title
comCopSymEms.regist.content = Send Content
comCopSymEms.regist.atch = Attach files

comCopSymEms.inqire.sender = Sender
comCopSymEms.inqire.receiver = Receiver
comCopSymEms.inqire.title = Title
comCopSymEms.inqire.sendCn = Send Content
comCopSymEms.inqire.sendResult = Send Result
comCopSymEms.inqire.xml = XML Mail Inqire
comCopSymEms.inqire.atch = Attach files
comCopSymEms.inqire.summary=Print out {0} about a detail list.

comCopSymEms.searchCondition.title = Title
comCopSymEms.searchCondition.cn = Contents
comCopSymEms.searchCondition.sender = Sender

comCopSymEms.list.status = Status
comCopSymEms.list.receiver = Receiver
comCopSymEms.list.sender = Sender
comCopSymEms.list.title = Title
comCopSymEms.list.cn = Contents
comCopSymEms.list.regdate = Reg.Date
comCopSymEms.list.summary=Print out {0} about the list.