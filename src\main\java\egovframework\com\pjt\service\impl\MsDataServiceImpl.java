package egovframework.com.pjt.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.egovframe.rte.fdl.cmmn.EgovAbstractServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import egovframework.com.pjt.service.MsDataService;
import egovframework.com.pjt.service.MsDataVO;

@Service("msDataService")
public class MsDataServiceImpl extends EgovAbstractServiceImpl implements MsDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MsDataServiceImpl.class);

    @Resource(name = "msDataDAO")
    private MsDataDAO msDataDAO;

    @Override
    public List<MsDataVO> getDataList() throws Exception {
        return msDataDAO.selectDataList();
    }

    @Override
    public List<MsDataVO> getFrameByRoll(String roll) throws Exception {
        return msDataDAO.selectFrameByRoll(roll);
    }

    @Override
    public MsDataVO getFile(String roll, String frame) throws Exception {
        return msDataDAO.selectFile(roll, frame);
    }

    /**
     * 토지대장 기본정보 조회
     */
    @Override
    public List<MsDataVO> getLandBasicInfo(String rollNo, String frameNo) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rollNo", rollNo);
        paramMap.put("frameNo", frameNo);

        return msDataDAO.selectLandBasicInfo(paramMap);

    }

    /**
     * 토지대장 연혁 조회
     */
    @Override
    public List<MsDataVO> getLandHistory(String rollNo, String frameNo) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rollNo", rollNo);
        paramMap.put("frameNo", frameNo);

        return msDataDAO.selectLandHistory(paramMap);
    }

    /**
     * 토지대장 날짜/사고/주소 조회
     */
    @Override
    public List<MsDataVO> getLandDateInfo(String rollNo, String frameNo) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rollNo", rollNo);
        paramMap.put("frameNo", frameNo);

        return msDataDAO.selectLandDateInfo(paramMap);
    }

    /**
     * 토지대장 소유자 조회
     */
    @Override
    public List<MsDataVO> getLandOwnerInfo(String rollNo, String frameNo) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rollNo", rollNo);
        paramMap.put("frameNo", frameNo);

        return msDataDAO.selectLandOwnerInfo(paramMap);
    }

    /**
     * 토지대장 색인정보 조회
     */
    @Override
    public MsDataVO getLandIndexInfo(String rollNo, String frameNo) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rollNo", rollNo);
        paramMap.put("frameNo", frameNo);

        return msDataDAO.selectLandIndexInfo(paramMap);
    }

    /**
     * 토지대장 기본정보 저장
     */
    @Override
    public boolean saveLandBasicInfo(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception {
        try {
            // 기존 데이터 삭제
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("rollNo", rollNo);
            paramMap.put("frameNo", frameNo);
            msDataDAO.deleteLandBasicInfo(paramMap);

            // 새 데이터 삽입
            if (dataList != null && !dataList.isEmpty()) {
                msDataDAO.insertLandBasicInfo(dataList);
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("토지대장 기본정보 저장 중 오류 발생: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 토지대장 연혁 저장
     */
    @Override
    public boolean saveLandHistory(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception {
        try {
            // 기존 데이터 삭제
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("rollNo", rollNo);
            paramMap.put("frameNo", frameNo);
            msDataDAO.deleteLandHistory(paramMap);

            // 새 데이터 삽입
            if (dataList != null && !dataList.isEmpty()) {
                msDataDAO.insertLandHistory(dataList);
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("토지대장 연혁 저장 중 오류 발생: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 토지대장 날짜/사고/주소 저장
     */
    @Override
    public boolean saveLandDateInfo(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception {
        try {
            // 기존 데이터 삭제
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("rollNo", rollNo);
            paramMap.put("frameNo", frameNo);
            msDataDAO.deleteLandDateInfo(paramMap);

            // 새 데이터 삽입
            if (dataList != null && !dataList.isEmpty()) {
                msDataDAO.insertLandDateInfo(dataList);
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("토지대장 날짜/사고/주소 저장 중 오류 발생: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 토지대장 소유자 저장
     */
    @Override
    public boolean saveLandOwnerInfo(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception {
        try {
            // 기존 데이터 삭제
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("rollNo", rollNo);
            paramMap.put("frameNo", frameNo);
            msDataDAO.deleteLandOwnerInfo(paramMap);

            // 새 데이터 삽입
            if (dataList != null && !dataList.isEmpty()) {
                msDataDAO.insertLandOwnerInfo(dataList);
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("토지대장 소유자 저장 중 오류 발생: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 토지대장 색인정보 저장
     */
    @Override
    public boolean saveLandIndexInfo(String rollNo, String frameNo, MsDataVO data) throws Exception {
        try {
            // 기존 데이터 삭제
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("rollNo", rollNo);
            paramMap.put("frameNo", frameNo);
            msDataDAO.deleteLandIndexInfo(paramMap);

            // 새 데이터 삽입
            if (data != null) {
                msDataDAO.insertLandIndexInfo(data);
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("토지대장 색인정보 저장 중 오류 발생: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 지목 코드 리스트 조회
     */
    @Override
    public List<Map<String, Object>> getJimokCodeList() throws Exception {
        return msDataDAO.selectJimokCodeList();
    }

    /**
     * 연혁 코드 리스트 조회
     */
    @Override
    public List<Map<String, Object>> getHistoryCodeList() throws Exception {
        return msDataDAO.selectHistoryCodeList();
    }

    /**
     * 소유자 코드 리스트 조회
     */
    @Override
    public List<Map<String, Object>> getReasonCodeList() throws Exception {
        return msDataDAO.selectReasonCodeList();
    }

    /**
     * 소유자 연호 코드 리스트 조회
     */
    @Override
    public List<Map<String, Object>> getYearCodeList() throws Exception {
        return msDataDAO.selectYearCodeList();
    }
    
    /**
    * 소유자 주소 리스트 조회
    */
   @Override
   public List<Map<String, Object>> getOwnerAddrList() throws Exception {
       return msDataDAO.selectYearCodeList();
   }
    
    /**
    * 소유자 이름 리스트 조회
    */
   @Override
   public List<Map<String, Object>> getOwnerNameList() throws Exception {
       return msDataDAO.selectYearCodeList();
   }
    
    /**
     * 작업코드 별 특정 권, 페이지의 마지막 순번값 조회
     */
    @Override
    public int getMaxSrNo(String rollNo, String frameNo, String workcode) throws Exception {
    	return msDataDAO.selectMaxSrnoByTable(rollNo, frameNo, workcode);
    };
    
}

