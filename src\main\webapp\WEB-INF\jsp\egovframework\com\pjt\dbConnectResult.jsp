<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%
    // 이전 페이지에서 전달받은 workcode 파라미터 가져오기
    String workcode = request.getParameter("workcode");
    if (workcode == null || workcode.isEmpty()) {
        workcode = "0"; // 기본값은 0 (전체)
    }
    pageContext.setAttribute("workcode", workcode);
%>
<!DOCTYPE html>
<html lang="ko">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>프로젝트 관리 시스템</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- common css -->
    <link rel="stylesheet" href="<c:url value='/css/land/common2.css'/>">
    <!-- OpenSeadragon -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/openseadragon/4.1.0/openseadragon.min.js"></script>

    <style>
        /* 서제스트 팝업 스타일 */
        .suggest-popup {
            position: absolute;
            background-color: white;
            border: 1px solid #ccc;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            max-height: 300px;
            z-index: 1000;
            display: none;
            width: 250px;
            flex-direction: column;
        }

        .suggest-search {
            padding: 8px;
            border-bottom: 1px solid #eee;
            position: sticky;
            top: 0;
            background-color: white;
        }

        .suggest-search input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .suggest-items {
            overflow-y: auto;
            max-height: 250px;
        }

        .suggest-item {
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #f5f5f5;
        }

        .suggest-item:hover {
            background-color: #f0f0f0;
        }

        .suggest-item.active, .suggest-item:focus {
            background-color: #e0e0e0;
            outline: none;
            box-shadow: inset 0 0 0 2px #4e73df;
        }

        /* 순번 삽입/삭제 관련 스타일 */
        .th-action-menu {
            position: absolute;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            padding: 5px 0;
            min-width: 120px;
        }

        .th-action-menu button {
            display: block;
            width: 100%;
            padding: 8px 12px;
            text-align: left;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #333;
        }

        .th-action-menu button:hover {
            background-color: #f0f0f0;
        }

        .th-action-menu button.insert {
            color: #28a745;
        }

        .th-action-menu button.insert:hover {
            background-color: #e8f5e9;
        }

        .th-action-menu button.delete {
            color: #dc3545;
        }

        .th-action-menu button.delete:hover {
            background-color: #fbeaea;
        }

        /* 순번 헤더 스타일 */
        .header th {
            cursor: pointer;
            position: relative;
            user-select: none;
            background-color: #f8f9fc;
            transition: background-color 0.2s;
        }

        .header th:hover {
            background-color: #eaecf4;
        }

        /* 순번 헤더 클릭 효과 */
        .header th:active {
            background-color: #d4d7e3;
        }

        /* 순번 헤더 포커스 효과 제거 */
        .header th:focus {
            outline: none;
        }
    </style>
</head>
<body>
    <!-- 네비게이션 바 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="<c:url value='/pjt/projectList.do'/>">
                <i class="fas fa-home me-2"></i>문서 뷰어
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <c:if test="${not empty loginVO}">
                                ${loginVO.name}
                            </c:if>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>설정</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<c:url value="/uat/uia/actionLogout.do"/>">
                                <i class="fas fa-sign-out-alt me-2"></i>로그아웃
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
    </div>
    </nav>

    <main class="main-content">
        <section class="viewer-section">
            <div class="image-container">
                <div id="viewer"></div>
                <div id="magnifier" class="magnifier"></div>
                <div class="loading-overlay" id="loading" style="display: none;">
                    <div class="loading-spinner"></div>
                </div>
      </div>
            <div class="data-table-container">
                <div id="template-container">
                    <div class="initial-message">
                        <p>데이터를 조회하려면 우측 목록에서 권-페이지를 선택하세요.</p>
                    </div>
                </div>
                <!-- 서제스트 팝업 -->
                <div id="suggestPopup" class="suggest-popup">
                    <div class="suggest-search">
                        <input type="text" id="suggestFilter" placeholder="검색어 입력..." autocomplete="off">
                    </div>
                    <div id="suggestItems" class="suggest-items">
                        <!-- 서제스트 항목들이 여기에 동적으로 추가됨 -->
                    </div>
                </div>
            </div>
    </section>

        <aside class="sidebar">
            <div class="sidebar-header">
                <button id="btnVolume" class="active">권</button>
                <button id="btnPage" disabled>페이지</button>
            </div>
            <div class="sidebar-content">
                <div id="kwonList" class="list-group">
                    <!-- 권 목록이 여기에 동적으로 추가됨 -->
                </div>
                <div id="pageList" class="list-group" style="display: none;">
                    <!-- 페이지 목록이 여기에 동적으로 추가됨 -->
                </div>
            </div>
        </aside>
  </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 페이지 로드 시 포커스 제거 - 영역별로 제한
        document.addEventListener('DOMContentLoaded', function() {
            // 초기 포커스 제거는 유지
            document.activeElement.blur();

            // 테두리 제거를 위한 클래스 추가 (변경 없음)
            document.querySelectorAll('#kwonList .list-group-item, #pageList .list-group-item').forEach(item => {
                item.classList.add('no-outline');
            });

            // 특정 영역에만 클릭 이벤트 핸들러 적용
            document.querySelector('.sidebar').addEventListener('click', function(e) {
                // contenteditable 요소가 아닌 경우에만 포커스 제거
                if (!e.target.closest('[contenteditable="true"]')) {
                    document.activeElement.blur();
                }
            });

            document.querySelector('.image-container').addEventListener('click', function() {
                document.activeElement.blur();
            });

            // 데이터 처리 영역에서는 contenteditable 요소의 포커스 유지
            document.querySelector('#template-container').addEventListener('click', function(e) {
                // contenteditable 요소가 아닌 경우에만 포커스 제거
                if (!e.target.closest('[contenteditable="true"]')) {
                    document.activeElement.blur();
                }
            });
        });

        // 사이드바 토글 기능
        document.getElementById('btnVolume').addEventListener('click', function() {
            this.classList.add('active');
            document.getElementById('btnPage').classList.remove('active');
            document.getElementById('kwonList').style.display = 'block';
            document.getElementById('pageList').style.display = 'none';
            
            const activeKwon = document.querySelector('#kwonList .list-group-item.active');
            if (activeKwon) {
                activeKwon.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }
        });

        document.getElementById('btnPage').addEventListener('click', function() {
            this.classList.add('active');
            document.getElementById('btnVolume').classList.remove('active');
            document.getElementById('pageList').style.display = 'block';
            document.getElementById('kwonList').style.display = 'none';
            
            const activePage = document.querySelector('#pageList .list-group-item.active');
            if (activePage) {
                activePage.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }
        });

     // 권 목록 동적 생성
        function createKwonList() {
         const url = "<c:url value='/msdata/kwonList'/>";
    	 fetch(url)
                .then(response => response.json())
                .then(data => {
                    const kwonList = document.getElementById('kwonList');
                    kwonList.innerHTML = '';
                    data.forEach(item => {
                        const kwon = item.rollNo; // VO 필드명과 매핑
                        const element = document.createElement('a');
                        element.className = 'list-group-item';
                        element.textContent = kwon;
                        element.setAttribute('data-kwon', kwon);
                        // 테두리 제거는 CSS에서 통합적으로 처리함
                        kwonList.appendChild(element);
                    });

                    // 권 목록에 대한 더블클릭 이벤트를 별도로 설정
                    addKwonListDblClickEvent();
                });
        }

        // 페이지 목록 동적 생성
        function createPageList(roll) {
        	const baseurl = "<c:url value='/msdata/pageList'/>";  // JSP 태그는 여기까지
        	const fullUrl = baseurl + "?roll=" + encodeURIComponent(roll);

        	fetch(fullUrl)
            .then(response => response.json())
            .then(pages => {
                const pageList = document.getElementById('pageList');
                pageList.innerHTML = '';
                pages.forEach(page => {
                    const item = document.createElement('a');
                    item.className = 'list-group-item';
                    item.textContent = page.frameNo;
                    item.setAttribute('data-frame', page.frameNo);
                    // 테두리 제거는 CSS에서 통합적으로 처리함
                    pageList.appendChild(item);
                });

                // 페이지 목록에 대한 더블클릭 이벤트를 별도로 설정
                addPageListDblClickEvent();

                // 자동으로 페이지 탭 열기
                document.getElementById('btnPage').click();

                // 페이지 목록 스크롤바 맨 위로 이동
                const pageListContainer = document.querySelector('.sidebar-content');
                if (pageListContainer) {
                    pageListContainer.scrollTop = 0;
                }
            });
        }

        // 권 목록에 대한 더블클릭 이벤트 처리 함수
        function addKwonListDblClickEvent() {
            const kwonItems = document.querySelectorAll('#kwonList .list-group-item');
            kwonItems.forEach(item => {
                // 기존 이벤트 리스너 제거 (중복 방지)
                const clone = item.cloneNode(true);
                item.parentNode.replaceChild(clone, item);

                // 더블클릭 이벤트 처리
                clone.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.blur();
                    document.activeElement.blur(); // 현재 포커스된 요소의 포커스 제거

                    const rollNo = this.getAttribute('data-kwon');
                    // 페이지 탭 버튼 활성화
                    document.getElementById('btnPage').disabled = false;

                    // 클릭한 항목 활성화
                    document.querySelectorAll('#kwonList .list-group-item').forEach(el => {
                        el.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                 	// 권표기
                	const btnKwon = document.getElementById('btnVolume');
					if (btnKwon) {
						btnKwon.textContent = rollNo + ' 권';
					}

                    // 페이지 목록 로드
                    createPageList(rollNo);

                    // 페이지 목록 스크롤바 맨 위로 이동
                    setTimeout(() => {
                        const pageListContainer = document.querySelector('.sidebar-content');
                        if (pageListContainer) {
                            pageListContainer.scrollTop = 0;
                        }
                    }, 100); // 페이지 목록이 로드된 후 스크롤 이동을 위해 약간의 지연 추가
                });

                // 단일 클릭 이벤트 재정의
                clone.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.blur();
                    document.activeElement.blur(); // 현재 포커스된 요소의 포커스 제거

                    document.querySelectorAll('#kwonList .list-group-item').forEach(el => {
                        el.classList.remove('active');
                    });
                    this.classList.add('active');
                };
            });
        }

        // 페이지 목록에 대한 더블클릭 이벤트 처리 함수
        function addPageListDblClickEvent() {
            const pageItems = document.querySelectorAll('#pageList .list-group-item');
            pageItems.forEach(item => {
                // 기존 이벤트 리스너 제거 (중복 방지)
                const clone = item.cloneNode(true);
                item.parentNode.replaceChild(clone, item);

                // 새 이벤트 리스너 추가
                clone.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.blur();
                    document.activeElement.blur(); // 현재 포커스된 요소의 포커스 제거

                    const frameNo = this.getAttribute('data-frame');
                    // 현재 선택된 권 번호 가져오기
                    const activeKwon = document.querySelector('#kwonList .list-group-item.active');
                    if (activeKwon) {
                        const rollNo = activeKwon.getAttribute('data-kwon');

                        // 클릭한 항목 활성화
                        document.querySelectorAll('#pageList .list-group-item').forEach(el => {
                            el.classList.remove('active');
                        });
                        this.classList.add('active');

                     	// 페이지표기
                    	const btnPage = document.getElementById('btnPage');
						if (btnPage) {
						    btnPage.textContent = frameNo + ' 페이지';
						}
                        
                        // 로딩 표시
                        loading.style.display = 'flex';

                        // 이전 페이지에서 전달받은 workcode 사용
                        const workcode = '${workcode}';

                        try {
                            // 이미지 로드
                            loadImageByRollAndFrame(rollNo, frameNo);

                            // 템플릿 로드
                            loadTemplate(rollNo, frameNo, workcode);
                        } catch (error) {
                            console.error('Error in page double-click handler:', error);
                            alert(`오류가 발생했습니다: ${error.message}`);
                            loading.style.display = 'none';
                        }
                    } else {
                        alert('먼저 권을 선택해주세요.');
                    }
                });

                // 단일 클릭 이벤트 추가
                clone.onclick = function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.blur();
                    document.activeElement.blur(); // 현재 포커스된 요소의 포커스 제거

                    document.querySelectorAll('#pageList .list-group-item').forEach(el => {
                        el.classList.remove('active');
                    });
                    this.classList.add('active');
                };
            });
        }

        // 전역 변수로 코드 리스트 데이터 저장
        const codeListData = {
            jimokCodeList: [],
            historyCodeList: [],
            ownerCodeList: [],
            yearCodeList: []
        };

        // workcode에 따라 필요한 코드 리스트 조회
        function loadCodeListByWorkcode(workcode) {
            return new Promise((resolve, reject) => {
                let codeUrls = [];

                switch(workcode) {
                    case '1': // 지목
                        codeUrls.push({
                            url: '<c:url value="/msdata/code/jimok.do"/>',
                            type: 'jimokCodeList'
                        });
                        break;
                    case '2': // 연혁
                        codeUrls.push({
                            url: '<c:url value="/msdata/code/history.do"/>',
                            type: 'historyCodeList'
                        });
                        break;
                    case '3': // 소유자 코드, 연호코드 (두 가지 모두 필요)
                        codeUrls.push({
                            url: '<c:url value="/msdata/code/owner.do"/>',
                            type: 'ownerCodeList'
                        });
                        codeUrls.push({
                            url: '<c:url value="/msdata/code/year.do"/>',
                            type: 'yearCodeList'
                        });
                        break;
                }

                if (codeUrls.length === 0) {
                    // 코드 리스트가 필요하지 않은 경우
                    resolve(codeListData);
                    return;
                }

                // 모든 코드 리스트 조회 요청을 병렬로 처리
                const fetchPromises = codeUrls.map(item => {
                    console.log(`코드 리스트 조회 URL: ${item.url}`);

                    return fetch(item.url)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(response => {
                            if (!response.success) {
                                throw new Error(response.message || '코드 리스트 조회 실패');
                            }

                            // 코드 리스트 데이터 저장
                            const data = response.data;
                            console.log(`${item.type} 코드 리스트 조회 결과:`, data);
                            codeListData[item.type] = data;

                            return data;
                        });
                });

                // 모든 코드 리스트 조회가 완료되면 처리
                Promise.all(fetchPromises)
                    .then(() => {
                        resolve(codeListData);
                    })
                    .catch(error => {
                        console.error('코드 리스트 조회 중 오류 발생:', error);
                        reject(error);
                    });
            });
        }

        // 템플릿 로드 함수 - workcode에 따라 다른 템플릿 로드
        function loadTemplate(rollNo, frameNo, workcode) {
            const container = document.getElementById('template-container');
            container.innerHTML = '<div class="alert alert-info">템플릿을 로드하는 중...</div>';

            // 레이아웃 관련 요소 참조
            const viewerSection = document.querySelector('.viewer-section');
            const imageContainer = document.querySelector('.image-container');
            const dataTableContainer = document.querySelector('.data-table-container');

            // workcode에 따라 다른 템플릿 URL 생성
            let templateUrl = '';
            let cssPath = '<c:url value="/css/land/common.css"/>';

            switch(workcode) {
                case '1': // 지목
                    templateUrl = '<c:url value="/msdata/land/landBasicInfo.do"/>';
                    // 위-아래 레이아웃 유지
                    resetToVerticalLayout();
                    break;
                case '2': // 연혁
                    templateUrl = '<c:url value="/msdata/land/landHistory.do"/>';
                    // 위-아래 레이아웃 유지
                    resetToVerticalLayout();
                    break;
                case '3': // 날짜
                    templateUrl = '<c:url value="/msdata/land/landDateInfo.do"/>';
                    // 위-아래 레이아웃 유지
                    resetToVerticalLayout();
                    break;
                case '4': // 이름
                    templateUrl = '<c:url value="/msdata/land/landOwnerInfo.do"/>';
                    // 위-아래 레이아웃 유지
                    resetToVerticalLayout();
                    break;
                case '5': // 적요
                    templateUrl = '<c:url value="/msdata/land/landIndexInfo.do"/>';
                    // 좌-우 레이아웃으로 변경
                    changeToHorizontalLayout();
                    break;
            }
			console.log(workcode+" "+templateUrl);

            // CSS 파일은 이미 헤더에 로드되어 있으므로 추가 로드하지 않음
            // 필요한 경우 특정 스타일만 동적으로 추가
            console.log('현재 템플릿에 맞는 CSS 스타일 적용: ' + workcode);

            // 먼저 코드 리스트를 로드한 후 템플릿과 데이터 로드
            loadCodeListByWorkcode(workcode)
                .then(() => {
                    // 코드 리스트 로드 후 데이터 로드 함수 호출
                    loadDataByWorkcode(rollNo, frameNo, workcode);
                })
                .catch(error => {
                    console.error('코드 리스트 로드 중 오류 발생:', error);
                    // 오류가 발생해도 데이터 로드는 진행
                    loadDataByWorkcode(rollNo, frameNo, workcode);
                });
        }

        // 위-아래 레이아웃으로 초기화 (기본 레이아웃)
        function resetToVerticalLayout() {
            const viewerSection = document.querySelector('.viewer-section');
            const imageContainer = document.querySelector('.image-container');
            const dataTableContainer = document.querySelector('.data-table-container');

            // 기존 스타일 초기화
            viewerSection.style.flexDirection = 'column';
            imageContainer.style.flex = '5';
            imageContainer.style.width = '100%';
            dataTableContainer.style.flex = '5';
            dataTableContainer.style.width = '100%';
        }

        // 좌-우 레이아웃으로 변경
        function changeToHorizontalLayout() {
            const viewerSection = document.querySelector('.viewer-section');
            const imageContainer = document.querySelector('.image-container');
            const dataTableContainer = document.querySelector('.data-table-container');

            // 좌-우 레이아웃으로 변경
            viewerSection.style.flexDirection = 'row';
            imageContainer.style.flex = '1';
            imageContainer.style.width = '50%';
            dataTableContainer.style.flex = '1';
            dataTableContainer.style.width = '50%';
        }

        // HTML 템플릿에서 body 컨텐츠만 추출하는 함수
        function extractBodyContent(html) {
            // 임시 div 요소 생성
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            // form#landForm 요소 찾기 (주요 컨텐츠 포함)
            const formElement = tempDiv.querySelector('form#landForm');

            if (formElement) {
                // 폼 요소에서 hidden input 제거 (이미 원본 페이지에 있음)
                const hiddenInputs = formElement.querySelectorAll('input[type="hidden"]');
                hiddenInputs.forEach(input => input.remove());

                // ground 요소만 반환 (주요 데이터 테이블 포함)
                const groundElement = formElement.querySelector('.ground');
                if (groundElement) {
                    return groundElement.outerHTML;
                }

                return formElement.innerHTML;
            }

            // form 요소를 찾지 못한 경우 전체 HTML 반환
            return html;
        }


        // workcode에 따라 다른 데이터 로드
        function loadDataByWorkcode(rollNo, frameNo, workcode) {

            // 1. 먼저 템플릿을 로드 (landTemplate.do 사용)
            const templateUrl = '<c:url value="/msdata/land/template.do"/>' +
                              '?workcode=' + encodeURIComponent(workcode) +
                              '&roll_no=' + encodeURIComponent(rollNo) +
                              '&frame_no=' + encodeURIComponent(frameNo);

            const container = document.getElementById('template-container');
            container.innerHTML = '<div class="alert alert-info">템플릿을 로드하는 중...</div>';

            // 템플릿 로드
            fetch(templateUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.text(); // HTML 템플릿을 텍스트로 받음
                })
                .then(html => {
                    // 템플릿에서 본문 부분만 추출
                    const bodyContent = extractBodyContent(html);

                    // 템플릿 컨테이너에 로드
                    container.innerHTML = bodyContent;
                    console.log('Template loaded successfully');

                    // 2. 템플릿 로드 후 데이터 로드 (getData.do 사용)
                    loadDataForTemplate(rollNo, frameNo, workcode);
                })
                .catch(error => {
                    console.error('Error loading template:', error);
                    container.innerHTML = `<div class="alert alert-danger">템플릿 로드 중 오류가 발생했습니다: ${error.message}</div>`;
                });
        }

        // 템플릿에 맞는 데이터 로드 함수
        function loadDataForTemplate(rollNo, frameNo, workcode) {
            // workcode에 따른 데이터 타입 결정
            let dataType;
            switch(workcode) {
                case '1': dataType = 'basic'; break;
                case '2': dataType = 'history'; break;
                case '3': dataType = 'date'; break;
                case '4': dataType = 'owner'; break;
                case '5': dataType = 'index'; break;
                default: dataType = 'all'; break;
            }

            // getData.do URL 생성
            const dataUrl = '<c:url value="/msdata/land/getData.do"/>' +
                          '?roll_no=' + encodeURIComponent(rollNo) +
                          '&frame_no=' + encodeURIComponent(frameNo) +
                          '&type=' + encodeURIComponent(dataType);

            console.log("Loading data from: "+dataUrl);

            // 데이터 가져오기
            fetch(dataUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(response => {
                    console.log('Data loaded:', response);

                    // 응답 구조 처리
                    if (!response.success) {
                        throw new Error(response.message || '데이터 로드 실패');
                    }

                    const data = response.data;

                    // workcode에 따라 다른 렌더링 함수 호출
                    switch(workcode) {
                        case '1': // 지목
                            renderBasicInfo(data.basicInfo || []);
                            // 체크박스 및 포커스 이동 이벤트 리스너 등록
                            setupCheckboxEventListeners();
                            break;
                        case '2': // 연혁
                            renderHistory(data.history || []);
                            break;
                        case '3': // 날짜
                            renderDateInfo(data.dateInfo || []);
                            break;
                        case '4': // 이름
                            renderOwnerInfo(data.ownerInfo || []);
                            break;
                        case '5': // 적요
                        	renderIndexInfo(data.indexInfo || []);
                            break;
                    }

                    // 모든 workcode에 대해 포커스 이동 이벤트 리스너 설정
                    // 체크박스 이벤트는 workcode=1인 경우에만 설정
                    if (workcode !== '1') {
                        setupFocusNavigation();
                        setupSuggestEventListeners(); // 서제스트 이벤트 리스너 설정
                    }
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                    // 오류 발생 시 조용히 무시 (템플릿은 그대로 표시)
                });
        }

        // 기본정보 렌더링 함수
        function renderBasicInfo(data) {
            if (!data || !Array.isArray(data) || data.length === 0) return;

            // 지적 단위(jijukUnit)와 등급 단위(grideUnit) 처리를 위한 코드 리스트 분류
            let jijukUnitCodeList = [];
            let grideUnitCodeList = [];

            // 코드 리스트가 있는 경우 분류
            if (codeListData.jimokCodeList && codeListData.jimokCodeList.length > 0) {

                // code2 값이 문자열 또는 숫자일 수 있으므로 두 가지 경우 모두 처리
                jijukUnitCodeList = codeListData.jimokCodeList.filter(item =>
                    String(item.code2) === '0002');
                grideUnitCodeList = codeListData.jimokCodeList.filter(item =>
                    String(item.code2) === '0003');
            }

            document.querySelectorAll('td[data-type][data-index]').forEach(td => {
                const type = td.getAttribute('data-type');     // 예: 'jimok', 'jijuk'
                const index = td.getAttribute('data-index');   // 예: '1', '2'
                const matchedItem = data.find(item => item.srNo === index);

                if (!matchedItem || matchedItem[type] === undefined) return;

                // 지목 처리
                if (type === 'jimok') {
                    const jimokDiv = td.querySelector('div');
                    if (jimokDiv) {
                        // 가져온 데이터를 그대로 표시
                        jimokDiv.textContent = matchedItem[type];
                    }

                    // 지목 체크박스 처리
                    if (matchedItem.jimokLine !== undefined) {
                        const checkbox = td.querySelector('input.jimokLine');
                        if (checkbox) {
                            const isChecked = matchedItem.jimokLine === '1';
                            checkbox.checked = isChecked;
                            checkbox.value = matchedItem.jimokLine; // value 값 업데이트

                            // 체크박스 상태에 따라 td에 slash 클래스 추가/제거
                            if (isChecked) {
                                // 오른쪽 데이터 영역인지 왜쪽 데이터 영역인지 확인
                                const isRightData = td.closest('.right-data') !== null;
                                if (isRightData) {
                                    td.classList.add('slash');
                                } else {
                                    td.classList.add('slash2');
                                }
                            } else {
                                td.classList.remove('slash');
                                td.classList.remove('slash2');
                            }
                        }
                    }
                }

                // 지적 처리
                else if (type === 'jijuk') {
                    const jijukDiv = td.querySelector('div');
                    if (jijukDiv) {
                        jijukDiv.textContent = matchedItem[type];
                    }

                    // 지적 단위 처리
                    const jijukUnitDiv = td.querySelector('.jijukUnit');
                    if (jijukUnitDiv && matchedItem.jijukUnit) {
                        const unitCode = matchedItem.jijukUnit;
                        jijukUnitDiv.textContent = unitCode; // 기본값 설정

                        // code2='0002'(지적 단위) 코드리스트에서 이름 찾아서 표시
                        if (jijukUnitCodeList.length > 0) {
                            // unitCode가 문자열 또는 숫자일 수 있으므로 두 가지 경우 모두 처리
                            const unitInfo = jijukUnitCodeList.find(item =>
                                item.code3 === unitCode ||
                                item.code3 === Number(unitCode) ||
                                Number(item.code3) === Number(unitCode));
                            if (unitInfo && unitInfo.codeName) {
                                jijukUnitDiv.textContent = unitInfo.codeName;
                            }
                        }
                    }
                }

                // 과세가격1 처리
                else if (type === 'grade1') {
                    const grade1Div = td.querySelector('div');
                    if (grade1Div) {
                        grade1Div.textContent = matchedItem[type];
                    }

                    // 과세가격 단위 처리
                    const gradeUnitDiv = td.querySelector('.gradeUnit');
                    if (gradeUnitDiv && matchedItem.grideUnit) {
                        const unitCode = matchedItem.grideUnit;
                        gradeUnitDiv.textContent = unitCode; // 기본값 설정

                        // code2='0003'(등급 단위) 코드리스트에서 이름 찾아서 표시
                        if (grideUnitCodeList.length > 0) {
                            // unitCode가 문자열 또는 숫자일 수 있으므로 두 가지 경우 모두 처리
                            const unitInfo = grideUnitCodeList.find(item =>
                                item.code3 === unitCode ||
                                item.code3 === Number(unitCode) ||
                                Number(item.code3) === Number(unitCode));
                            if (unitInfo && unitInfo.codeName) {
                                gradeUnitDiv.textContent = unitInfo.codeName;
                            }
                        }
                    }

                    // 등급 체크박스 처리
                    if (matchedItem.gradeLine !== undefined) {
                        const checkbox = td.querySelector('input.gradeLine');
                        if (checkbox) {
                            const isChecked = matchedItem.gradeLine === '1';
                            checkbox.checked = isChecked;
                            checkbox.value = matchedItem.gradeLine; // value 값 업데이트

                            // 체크박스 상태에 따라 td에 slash 클래스 추가/제거
                            if (isChecked) {
                                // 오른쪽 데이터 영역인지 왜쪽 데이터 영역인지 확인
                                const isRightData = td.closest('.right-data') !== null;
                                if (isRightData) {
                                    td.classList.add('slash');
                                } else {
                                    td.classList.add('slash2');
                                }
                            } else {
                                td.classList.remove('slash');
                                td.classList.remove('slash2');
                            }
                        }
                    }
                }

                // 과세가격2 처리
                else if (type === 'grade2') {
                    const grade2Div = td.querySelector('div');
                    if (grade2Div) {
                        grade2Div.textContent = matchedItem[type];
                    }
                }
            });
        }

        // 연혁 렌더링 함수
        function renderHistory(data) {
        	if (!data || !Array.isArray(data) || data.length === 0) return;

            // const fontClass = history.length > 30 ? 'f-small' :
            //                  history.length > 20 ? 'f-medium' : 'f-large';

            document.querySelectorAll('td[data-type][data-index]').forEach (td => {
                const type = td.getAttribute('data-type');
                const index = td.getAttribute('data-index');
                const matchedItem = data.find(item => item.srNo === index);

                if (!matchedItem) {
                    console.warn(`srNo ${index}에 해당하는 항목 없음`);
                    return;
                }
                else {
                	const histDiv = td.querySelector('div');
                    if (histDiv) {
                        // 가져온 데이터를 그대로 표시
                        histDiv.textContent = matchedItem.history;
                    }
                }
            })
        }

        // 날짜 정보 렌더링 함수
        function renderDateInfo(data) {
            if (!data || !Array.isArray(data) || data.length === 0) return;

            document.querySelectorAll('td[data-type][data-index]').forEach(td => {
                const type = td.getAttribute('data-type');
                const index = td.getAttribute('data-index');
                const matchedItem = data.find(item => item.srNo === index);

                if (!matchedItem || matchedItem[type] === undefined) return;

                // 날짜 처리
                if (type === 'hisDate') {
                    const dateDiv = td.querySelector('#date_'+index);
                    if (dateDiv) {
                        // 가져온 데이터를 그대로 표시
                        dateDiv.textContent = matchedItem[type];
                    }
                    // 연호코드 처리
                    const yearDiv = td.querySelector('#year_'+index);
                    if (yearDiv && matchedItem.yearCode) {
                        const yearCode = matchedItem.yearCode;
                        yearDiv.textContent = yearCode; // 기본값 설정

                        // 연호코드리스트에서 연호 찾아서 표시
                        if (codeListData.yearCodeList.length > 0) {
                            const yearInfo = codeListData.yearCodeList.find(item => item.yearCode === yearCode);
                            if (yearInfo && yearInfo.yearName) {
                                yearDiv.textContent = yearInfo.yearName;
                            }
                        }
                    }
                }

                //사고처리
                else if (type === 'reason') {
                    const reasonDiv = td.querySelector('div');
                    if (reasonDiv) {
                        // 가져온 데이터를 그대로 표시
                        reasonDiv.textContent = matchedItem[type];
                    }
                }

                //주소처리
                else if (type === 'address') {
                    const addrDiv = td.querySelector('div');
                    if (addrDiv) {
                        // 가져온 데이터를 그대로 표시
                        addrDiv.textContent = matchedItem[type];
                    }
                }
            });
        }

        // 소유자 정보 렌더링 함수
        function renderOwnerInfo(data) {
            if (!data || !Array.isArray(data) || data.length === 0) return;

            document.querySelectorAll('td[data-type][data-index]').forEach (td => {
                const type = td.getAttribute('data-type');
                const index = td.getAttribute('data-index');
                const matchedItem = data.find(item => item.srNo === index);

                if (!matchedItem) {
                    console.warn(`srNo ${index}에 해당하는 항목 없음`);
                    return;
                }
                else {
                	const nameDiv = td.querySelector('div');
                    if (nameDiv) {
                        // 가져온 데이터를 그대로 표시
                        nameDiv.textContent = matchedItem.name;
                    }
                }
            });
        }

        // 색인 정보 렌더링 함수
        function renderIndexInfo(data) {
            if (!data || data.length === 0) return;

            const keys = ['jimeung', 'jibun', 'grade', 'jukyo'];

            keys.forEach(function(key) {
                const div = document.querySelector('div.' + key);

                if (!div || data[key] === undefined) return;

                div.textContent = data[key];
            });
        }

        // 체크박스 및 포커스 이동 이벤트 리스너 설정 함수
        function setupCheckboxEventListeners() {
            // 체크박스 클릭 이벤트 처리
            document.querySelectorAll('input.jimokLine, input.gradeLine').forEach(checkbox => {
                // 기존 이벤트 리스너 제거 (중복 방지)
                const clone = checkbox.cloneNode(true);
                checkbox.parentNode.replaceChild(clone, checkbox);

                // 체크박스 클릭 이벤트
                clone.addEventListener('change', function() {
                    const td = this.closest('td');
                    const isChecked = this.checked;

                    // value 값 업데이트
                    this.value = isChecked ? '1' : '0';

                    // 오른쪽 데이터 영역인지 왜쪽 데이터 영역인지 확인
                    const isRightData = td.closest('.right-data') !== null;

                    // 체크박스 상태에 따라 td에 slash 클래스 추가/제거
                    if (isChecked) {
                        if (isRightData) {
                            td.classList.add('slash');
                        } else {
                            td.classList.add('slash2');
                        }
                    } else {
                        td.classList.remove('slash');
                        td.classList.remove('slash2');
                    }
                });
            });

            // td 영역에 Ctrl 키 이벤트 처리
            document.querySelectorAll('td[data-type="jimok"], td[data-type="grade1"]').forEach(td => {
                // 기존 이벤트 리스너 제거 (중복 방지)
                td.removeEventListener('keydown', handleCtrlKeyDown);
                td.removeEventListener('keyup', handleCtrlKeyUp);

                // 키다운 이벤트 추가
                td.addEventListener('keydown', handleCtrlKeyDown);
                // 키업 이벤트 추가
                td.addEventListener('keyup', handleCtrlKeyUp);
            });

            // 포커스 이동을 위한 이벤트 리스너 설정
            setupFocusNavigation();
        }

        // 포커스 이동을 위한 이벤트 리스너 설정 함수
        function setupFocusNavigation() {
            // 모든 편집 가능한 div 요소에 이벤트 리스너 추가
            document.querySelectorAll('div[contenteditable="true"]').forEach(div => {
                // 기존 이벤트 리스너 제거 (중복 방지)
                div.removeEventListener('keydown', handleEnterKeyNavigation);

                // 새 이벤트 리스너 추가
                div.addEventListener('keydown', handleEnterKeyNavigation);
                div.addEventListener('keydown', handleInsertKeyDown);
            });
        }

        // Enter 키를 통한 포커스 이동 처리 함수
        function handleEnterKeyNavigation(event) {
            // Enter 키 감지 (keyCode 13)
            if (event.keyCode === 13 && !event.shiftKey) {
                // 기본 동작 방지 (줄바꿈 방지)
                event.preventDefault();

                // 현재 포커스가 있는 div 요소
                const currentDiv = event.target;
                // 현재 div가 있는 td 요소
                const currentTd = currentDiv.closest('td');

                if (!currentTd) return;

                // 현재 div가 jijukUnit 또는 gradeUnit인지 확인
                const isJijukUnit = currentDiv.classList.contains('jijukUnit');
                const isGradeUnit = currentDiv.classList.contains('gradeUnit');

                // jijukUnit 또는 gradeUnit인 경우 포커스 이동 안함 (줄바꿈만 방지)
                if (isJijukUnit || isGradeUnit) {
                    return;
                }

                // 현재 workcode 가져오기
                const workcode = '${workcode}';

                // 다음 포커스를 찾기 위한 정보 추출
                const dataType = currentTd.getAttribute('data-type');
                const dataIndex = currentTd.getAttribute('data-index');

                // 다음 포커스를 찾기 위한 로직
                let nextElement = null;

                // workcode에 따라 다른 포커스 이동 로직 적용
                switch(workcode) {
                    case '1': // 지목
                        // 1. jimok -> jijuk
                        if (dataType === 'jimok') {
                            nextElement = document.getElementById('jijuk_'+dataIndex);
                        }
                        // 2. jijuk -> grade1 (jijukUnit 건너뛰기)
                        else if (dataType === 'jijuk') {
                            nextElement = document.getElementById('grade1_'+dataIndex);
                        }
                        // 3. grade1 -> grade2 (gradeUnit 건너뛰기)
                        else if (dataType === 'grade1') {
                            nextElement = document.getElementById('grade2_'+dataIndex);
                        }
                        // 4. grade2 -> 다음 순번 jimok 또는 처음으로 순환
                        else if (dataType === 'grade2') {
                            // 다음 순번 계산
                            const nextIndex = parseInt(dataIndex) + 1;
                            // 다음 순번이 존재하는지 확인
                            nextElement = document.getElementById('jimok_'+nextIndex);
                            // 마지막 순번이면 처음으로 돌아가기
                            if (!nextElement && dataIndex === '13') {
                                nextElement = document.getElementById('jimok_1');
                            }
                        }
                        break;

                    case '2': // 연혁
                        // history td 밑 div의 포커스를 data-index 순번에 따라 1부터 13까지 이동
                        if (dataType === 'history') {
                            // 다음 순번 계산
                            const nextIndex = parseInt(dataIndex) + 1;
                            // 다음 순번이 존재하는지 확인
                            nextElement = document.getElementById('hist_'+nextIndex);
                            // 마지막 순번이면 처음으로 돌아가기
                            if (!nextElement && dataIndex === '13') {
                                nextElement = document.getElementById('hist_1');
                            }
                        }
                        break;

                    case '3': // 날짜
                        const currentId = currentDiv.id;
                        
                        // 포커스 이동 순서: year_ -> date_ -> reason -> addr
                        if (dataType === 'hisDate' && currentId.startsWith('year_')) {
                                nextElement = document.getElementById('date_'+dataIndex);
                            }
                        else if (dataType === 'hisDate' && currentId.startsWith('date_')) {
                            // date_에서 reason으로 이동
                            nextElement = document.getElementById('reason_'+dataIndex);
                        }
                        else if (dataType === 'reason') {
                            // reason에서 addr로 이동
                            nextElement = document.getElementById('addr_'+dataIndex);
                        }
                        else if (dataType === 'address') {
                            // 다음 순번 계산
                            const nextIndex = parseInt(dataIndex) + 1;
                            // 다음 순번이 존재하는지 확인
                            nextElement = document.getElementById('year_'+nextIndex);
                            // 마지막 순번이면 처음으로 돌아가기
                            if (!nextElement && dataIndex === '24') {
                                nextElement = document.getElementById('year_1');
                            }
                        }
                        break;

                    case '4': // 이름
                        // name td 밑 div의 포커스를 data-index 순번에 따라 1부터 24까지 이동
                        if (dataType === 'name') {
                            // 다음 순번 계산
                            const nextIndex = parseInt(dataIndex) + 1;
                            // 다음 순번이 존재하는지 확인
                            nextElement = document.getElementById('name_'+nextIndex);
                            // 마지막 순번이면 처음으로 돌아가기
                            if (!nextElement && dataIndex === '24') {
                                nextElement = document.getElementById('name_1');
                            }
                        }
                        break;

                    case '5': // 적요
                        // td 밑 div의 포커스를 td의 data-type이 jimeung, jibun, grade, jukyo의 순서로 이동
                        if (dataType === 'jimeung') {
                            // jimeung -> jibun
                            const jibunTd = document.querySelector('td[data-type="jibun"]');
                            if (jibunTd) {
                                nextElement = jibunTd.querySelector('div');
                            }
                        }
                        else if (dataType === 'jibun') {
                            // jibun -> grade
                            const gradeTd = document.querySelector('td[data-type="grade"]');
                            if (gradeTd) {
                                nextElement = gradeTd.querySelector('div');
                            }
                        }
                        else if (dataType === 'grade') {
                            // grade -> jukyo
                            const jukyoTd = document.querySelector('td[data-type="jukyo"]');
                            if (jukyoTd) {
                                nextElement = jukyoTd.querySelector('div');
                            }
                        }
                        else if (dataType === 'jukyo') {
                            // jukyo -> jimeung (순환)
                            const jimeungTd = document.querySelector('td[data-type="jimeung"]');
                            if (jimeungTd) {
                                nextElement = jimeungTd.querySelector('div');
                            }
                        }
                        break;
                }

                // 다음 요소가 있으면 포커스 이동
                if (nextElement) {
                    nextElement.focus();

                    // 텍스트 선택 (커서를 끝으로 이동)
                    const range = document.createRange();
                    const sel = window.getSelection();

                    // 텍스트 노드가 있는 경우
                    if (nextElement.firstChild && nextElement.firstChild.nodeType === 3) {
                        range.setStart(nextElement.firstChild, nextElement.firstChild.length);
                        range.collapse(true);
                    } else {
                        // 텍스트 노드가 없는 경우
                        range.setStart(nextElement, 0);
                        range.collapse(true);
                    }

                    sel.removeAllRanges();
                    sel.addRange(range);
                }
            }
        }

        // Ctrl 키다운 이벤트 처리 함수
        function handleCtrlKeyDown(event) {
            // Ctrl 키 감지 (keyCode 17)
            if (event.ctrlKey || event.keyCode === 17) {
                const td = event.currentTarget;
                const checkbox = td.querySelector('input[type="checkbox"]');

                if (checkbox) {
                    // 체크박스 상태 변경
                    checkbox.checked = !checkbox.checked;
                    checkbox.value = checkbox.checked ? '1' : '0';

                    // 오른쪽 데이터 영역인지 왜쪽 데이터 영역인지 확인
                    const isRightData = td.closest('.right-data') !== null;

                    // 체크박스 상태에 따라 td에 slash 클래스 추가/제거
                    if (checkbox.checked) {
                        if (isRightData) {
                            td.classList.add('slash');
                        } else {
                            td.classList.add('slash2');
                        }
                    } else {
                        td.classList.remove('slash');
                        td.classList.remove('slash2');
                    }
                }
            }
        }

        // Ctrl 키업 이벤트 처리 함수
        function handleCtrlKeyUp(event) {
            // 키업 이벤트 처리가 필요하면 여기에 추가
        }

        // insert 키다운 이벤트 처리 함수
        function handleInsertKeyDown(event) {
            // insert 키 감지 (keyCode 45)
            if (event.keyCode === 45) {
                event.preventDefault();

                // 현재 포커스가 있는 div 요소
                const currentDiv = event.target;
                // 현재 div가 있는 td 요소
                const currentTd = currentDiv.closest('td');
                const dataType = currentTd.getAttribute('data-type');

                if (!currentTd) return;

                // 현재 div가 jijukUnit 또는 gradeUnit인지 확인
                const isJijukUnit = currentDiv.classList.contains('jijukUnit');
                const isGradeUnit = currentDiv.classList.contains('gradeUnit');

                // jijukUnit 또는 gradeUnit인 경우 동작 안함 
                if (isJijukUnit || isGradeUnit) {
                    return;
                }

                // 현재 workcode 가져오기
                const workcode = '${workcode}';

                currentDiv.textContent = currentDiv.textContent + '[]';

                const selection = window.getSelection();
                const range = document.createRange();
                const len = currentDiv.lastChild.length;

                range.selectNodeContents(currentDiv);
                range.setStart(currentDiv.lastChild, len - 1); // "["과 "]" 사이
                range.collapse(true); 

                selection.removeAllRanges();
                selection.addRange(range);

            }
        }

        // 서제스트 팝업 관련 변수
        let activeSuggestPopup = null;
        let activeSuggestIndex = -1;
        let suggestItems = [];
        let currentEditableDiv = null;

        let boundFilterKeyDown = null;
        let boundFilterInput = null;

        // 서제스트 팝업 모듈 관리
        const SuggestModule = {

            // init: function() {
		    //     this.boundHandleFilterKeyDown = this.handleFilterKeyDown.bind(this);
		    //     this.boundHandleFilterInput = this.handleFilterInput.bind(this);
		    // },

            // 팝업 열기
            openPopup: function(target, items, filterFn, selectCallback) {
                // 현재 편집 중인 div 요소 저장
                currentEditableDiv = target;

                // 팝업 요소 가져오기
                const popup = document.getElementById('suggestPopup');
                const suggestItemsContainer = document.getElementById('suggestItems');
                const suggestFilter = document.getElementById('suggestFilter');

                // 팝업 위치 설정
                const rect = target.getBoundingClientRect();
                popup.style.left = (rect.left + rect.width + 5) + 'px';
                popup.style.top = (rect.top + 5) + 'px';

                // 아이템 컨테이너 초기화
                suggestItemsContainer.innerHTML = '';

                // 필터 입력 필드 초기화 및 포커스
                suggestFilter.value = '';

                // 모든 아이템 저장
                this.allItems = items;
                this.selectCallback = selectCallback;
                this.currentItems = [];

                // 초기 아이템 표시 (필터링 없이)
                this.updateSuggestItems(items);

                // 이벤트 설정
                if (!boundFilterKeyDown) boundFilterKeyDown = this.handleFilterKeyDown.bind(this);
                if (!boundFilterInput) boundFilterInput = this.handleFilterInput.bind(this);

                suggestFilter.removeEventListener('keydown', boundFilterKeyDown);
                suggestFilter.removeEventListener('input', boundFilterInput);

                suggestFilter.addEventListener('keydown', boundFilterKeyDown);
                suggestFilter.addEventListener('input', boundFilterInput);

                // 팝업 표시
                popup.style.display = 'flex';
                activeSuggestPopup = popup;
                activeSuggestIndex = -1;

                // 필터 입력 필드에 포커스
                setTimeout(() => suggestFilter.focus(), 50);
            },

            // 필터 입력 처리 (input 이벤트)
            handleFilterInput: function(event) {
                // 필터링 처리
                const filterText = event.target.value.trim().toLowerCase();
                const filteredItems = SuggestModule.allItems.filter(item => {
                    const itemName = item.codeName || item.code_name || item.CODE_NAME || item.history || item.yearName;
                    return itemName.toLowerCase().includes(filterText);
                });

                SuggestModule.updateSuggestItems(filteredItems);
            },

            // 필터 키보드 이벤트 처리 (keydown 이벤트)
            handleFilterKeyDown: function(event) {
                console.log('keydown:', event.key);
                const container = document.getElementById('suggestItems');
                const items = container.querySelectorAll('.suggest-item');

                if (items.length === 0 || (items.length === 1 && items[0].textContent === '검색 결과가 없습니다')) {
                    return;
                }

                if (event.key === 'ArrowDown') {
                    event.preventDefault();
                    this.moveSelection(1);
                } else if (event.key === 'ArrowUp') {
                    event.preventDefault();
                    this.moveSelection(-1);
                } else if (event.key === 'Enter') {
                    event.preventDefault();
                    if (activeSuggestIndex >= 0 && activeSuggestIndex < this.currentItems.length) {
                        const item = this.currentItems[activeSuggestIndex];
                        this.selectCallback(item, currentEditableDiv);
                        if (currentEditableDiv && typeof currentEditableDiv.focus === 'function') {
                            currentEditableDiv.focus();

                            // 커서를 끝으로 이동
                            const selection = window.getSelection();
                            const range = document.createRange();
                            range.selectNodeContents(currentEditableDiv);
                            range.collapse(false); // false = 끝으로

                            selection.removeAllRanges();
                            selection.addRange(range);
                        }
                        this.closePopup();
                    }
                } else if (event.key === 'Escape') {
                    event.preventDefault();
                    this.closePopup();
                }
            },

            // 선택된 항목 이동
            moveSelection: function(direction) {
                console.log('moveSelection called with direction:', direction, 'index before:', activeSuggestIndex);
                const container = document.getElementById('suggestItems');
                const items = container.querySelectorAll('.suggest-item');

                if (items.length === 0) return;

                // 기존 active 해제
                if (activeSuggestIndex >= 0) {
                    items[activeSuggestIndex].classList.remove('active');
                }

                // 인덱스 계산
                activeSuggestIndex += direction;
                if (activeSuggestIndex < 0) activeSuggestIndex = items.length - 1;
                if (activeSuggestIndex >= items.length) activeSuggestIndex = 0;

                // 새 항목 활성화
                const itemEl = items[activeSuggestIndex];
                itemEl.classList.add('active');
                itemEl.scrollIntoView({ block: 'nearest' });
            },

            // 서제스트 아이템 업데이트
            updateSuggestItems: function(items) {
                const container = document.getElementById('suggestItems');
                container.innerHTML = '';

                // 최대 10개만 표시
                const displayItems = items.slice(0, 10);
                this.currentItems = displayItems;

                activeSuggestIndex = -1; // 인덱스 초기화

                if (displayItems.length === 0) {
                    const noResults = document.createElement('div');
                    noResults.className = 'suggest-item';
                    noResults.textContent = '검색 결과가 없습니다';
                    noResults.style.fontStyle = 'italic';
                    noResults.style.color = '#999';
                    container.appendChild(noResults);
                    return;
                }

                displayItems.forEach((item, index) => {
                    const div = document.createElement('div');
                    div.className = 'suggest-item';
                    div.textContent = item.codeName || item.code_name || item.CODE_NAME || item.history || item.yearName || '';
                    div.dataset.index = index;

                    div.addEventListener('click', () => {
                        this.selectCallback(item, currentEditableDiv);
                        this.closePopup();
                    });

                    container.appendChild(div);
                });

                activeSuggestIndex = -1;
            },

            // 팝업 닫기
            closePopup: function() {
                if (activeSuggestPopup) {
                    activeSuggestPopup.style.display = 'none';
                    activeSuggestPopup = null;
                    activeSuggestIndex = -1;
                    suggestItems = [];
                }
            }
        };

        // 편집 가능한 div에 백틱 키 이벤트 추가
        function setupSuggestEventListeners() {
            document.querySelectorAll('div[contenteditable="true"]').forEach(div => {
                // 기존 이벤트 리스너 제거 (중복 방지)
                div.removeEventListener('keydown', handleSuggestKeyDown);

                // 새 이벤트 리스너 추가
                div.addEventListener('keydown', handleSuggestKeyDown);
            });

            // 팝업 외부 클릭 시 팝업 닫기
            document.addEventListener('click', function(e) {
                if (!e.target.closest('#suggestPopup') && !e.target.closest('div[contenteditable="true"]')) {
                    SuggestModule.closePopup();
                }
            });
        }

        // 백틱 키 및 팝업 네비게이션 처리
        function handleSuggestKeyDown(event) {
            // 현재 td 요소 가져오기
            const currentTd = event.target.closest('td');
            if (!currentTd) return;

            // 현재 workcode 가져오기
            const workcode = '${workcode}';

            // tr 클래스 가져오기
            const trClass = currentTd.closest('tr').className;

            // 현재 div의 ID 가져오기
            const currentDivId = event.target.id;

            // 현재 td의 data-type 가져오기
            const dataType = currentTd.getAttribute('data-type');

            // 백틱 키 감지 (keyCode 192)
            if (event.keyCode === 192) {
                event.preventDefault();

                // 필터링 함수는 이제 서제스트 모듈 내부에서 처리됨
                const filterFn = null;

                // workcode에 따라 다른 서제스트 리스트 적용
                switch(workcode) {
                    case '1': // 지목
                        // 해당 영역에 따라 다른 코드 리스트 사용
                        if (trClass.includes('jimok')) {
                            // 지목 코드 리스트 - code2='0001'
                            const jimokCodes = codeListData.jimokCodeList.filter(item =>
                                String(item.code2) === '0001');

                            // 선택 콜백 - 지목 div에 codeName 설정
                            const selectCallback = (item, target) => {
                                const codeName = item.codeName || item.code_name || item.CODE_NAME;
                                // 기존 텍스트에 새 텍스트 추가
                                target.textContent = target.textContent + codeName;
                            };

                            SuggestModule.openPopup(event.target, jimokCodes, filterFn, selectCallback);

                        } else if (trClass.includes('jijuk')) {
                            // 지적 코드 리스트 - code2='0002'
                            const jijukCodes = codeListData.jimokCodeList.filter(item =>
                                String(item.code2) === '0002');

                            // 선택 콜백 - jijukUnit div에 codeName 설정
                            const selectCallback = (item, target) => {
                                const codeName = item.codeName || item.code_name || item.CODE_NAME;
                                // jijukUnit div 찾기
                                const jijukUnitDiv = currentTd.querySelector('.jijukUnit');
                                if (jijukUnitDiv) {
                                    // 기존 텍스트 덮어쓰기
                                    jijukUnitDiv.textContent = codeName;
                                }
                            };

                            SuggestModule.openPopup(event.target, jijukCodes, null, selectCallback);

                        } else if (trClass.includes('grade1')) {
                            // 등급 코드 리스트 - code2='0003'
                            const gradeCodes = codeListData.jimokCodeList.filter(item =>
                                String(item.code2) === '0003');

                            // 선택 콜백 - gradeUnit div에 codeName 설정
                            const selectCallback = (item, target) => {
                                const codeName = item.codeName || item.code_name || item.CODE_NAME;
                                // gradeUnit div 찾기
                                const gradeUnitDiv = currentTd.querySelector('.gradeUnit');
                                if (gradeUnitDiv) {
                                    // 기존 텍스트 덮어쓰기
                                    gradeUnitDiv.textContent = codeName;
                                }
                            };

                            SuggestModule.openPopup(event.target, gradeCodes, null, selectCallback);
                        }
                        break;

                    case '2': // 연혁
                        // history 밑 div에서 동작 시, historyCodeList의 리스트를 사용해 서제스트 기능
                        if (trClass.includes('hist')) {
                            // 연혁 코드 리스트 사용
                            const historyCodes = codeListData.historyCodeList || [];

                            // 선택 콜백 - 연혁 div에 history 설정
                            const selectCallback = (item, target) => {
                                const historyText = item.history || item.HISTORY;
                                // 기존 텍스트에 새 텍스트 추가
                                target.textContent = target.textContent + historyText;
                            };

                            SuggestModule.openPopup(event.target, historyCodes, filterFn, selectCallback);
                        }
                        break;

                    case '3': // 날짜
                        // date td 밑 year_로 시작하는 div에서 yearCodeList를 사용해 작동
                        if (trClass.includes('date') && currentDivId.startsWith('year_')) {
                            // 연호 코드 리스트 사용
                            const yearCodes = codeListData.yearCodeList || [];

                            // 선택 콜백 - 연호 div에 yearCode 추가
                            const selectCallback = (item, target) => {
                                const yearText = item.yearName || item.YEAR_NAME;
                                // 기존 텍스트 덮어쓰기
                                target.textContent = yearText;
                            };

                            SuggestModule.openPopup(event.target, yearCodes, null, selectCallback);
                        }
                        // reason td 밑 div에서 ownerCodeList를 사용해 작동
                        if (currentTd.getAttribute('data-type') === 'reason') {
                            // 소유자 코드 리스트 사용
                            const ownerCodes = codeListData.ownerCodeList || [];

                            // 선택 콜백 - reason div에 ownerCode 설정
                            const selectCallback = (item, target) => {
                                const ownerText = item.codeName || item.CODE_NAME;
                             // 기존 텍스트에 새 텍스트 추가
                                target.textContent = target.textContent + ownerText;
                            };

                            SuggestModule.openPopup(event.target, ownerCodes, filterFn, selectCallback);
                        }
                        break;
                }
            }

            // 팝업이 열려 있을 때 키보드 네비게이션
            if (activeSuggestPopup) {
                // 위쪽 화살표
                if (event.keyCode === 38) {
                    event.preventDefault();
                    SuggestModule.navigateItems('up');
                }
                // 아래쪽 화살표
                else if (event.keyCode === 40) {
                    event.preventDefault();
                    SuggestModule.navigateItems('down');
                }
                // Enter 키
                else if (event.keyCode === 13) {
                    event.preventDefault();
                    if (activeSuggestIndex >= 0) {
                        // 현재 선택된 항목 선택
                        SuggestModule.selectItem(activeSuggestIndex);
                    }
                }
                // Escape 키
                else if (event.keyCode === 27) {
                    event.preventDefault();
                    SuggestModule.closePopup();
                }
            }
        }

        // 템플릿 로드 후 서제스트 이벤트 리스너 설정
        function setupTemplateEventListeners() {
            // 체크박스 및 포커스 이동 이벤트 리스너 설정
            setupCheckboxEventListeners();

            // 서제스트 팝업 이벤트 리스너 설정
            setupSuggestEventListeners();
        }

        // 다음 페이지로 이동
        function moveToNextPage() {
            // MsDataVO에서 현재 rollNo와 frameNo 가져오기
            const rollElement = document.querySelector('#kwonList .list-group-item.active');
			const frameElement = document.querySelector('#pageList .list-group-item.active');

			const rollNo = rollElement ? rollElement.getAttribute('data-kwon') : '';
			const frameNo = frameElement ? frameElement.getAttribute('data-frame') : '';

            const currentFrameNo = frameNo;

            if (!rollNo || !currentFrameNo) {
                alert('현재 페이지 정보를 찾을 수 없습니다.');
                return;
            }

            // 다음 페이지 번호 계산
            const nextFrameNo = parseInt(currentFrameNo) + 1;

            // 다음 페이지 항목 찾기
            const nextPageItem = document.querySelector('#pageList .list-group-item[data-frame="' + nextFrameNo + '"]');
            if (!nextPageItem) {
                alert('마지막 페이지입니다.');
                return;
            }

            // 로딩 표시
            loading.style.display = 'flex';

            // ProjectVO에서 workcode 가져오기
            const workcode = '${workcode}';

            try {
                // 다음 페이지 항목 활성화
                document.querySelectorAll('#pageList .list-group-item').forEach(el => {
                    el.classList.remove('active');
                });
                nextPageItem.classList.add('active');
                
                // 스크롤 이동
                nextPageItem.scrollIntoView({
                    behavior: 'smooth',   // 부드럽게 이동
                    block: 'center',     // 중앙 정렬
                    inline: 'nearest'
                });

                // 현재 활성화된 권 항목 업데이트
                document.querySelectorAll('#kwonList .list-group-item').forEach(el => {
                    if (el.getAttribute('data-kwon') === rollNo) {
                        el.classList.add('active');
                    } else {
                        el.classList.remove('active');
                    }
                });

                // 이미지 로드
                loadImageByRollAndFrame(rollNo, nextFrameNo);

                // 템플릿 로드
                loadTemplate(rollNo, nextFrameNo, workcode);
            } catch (error) {
                console.error('Error in moveToNextPage:', error);
                alert('다음 페이지로 이동 중 오류가 발생했습니다: ' + error.message);
                loading.style.display = 'none';
            }
        }
        
     	// 저장할 데이터추출
        async function extractData(rollNo, frameNo, workcode) {
            const url = '<c:url value="/msdata/maxNo"/>' +
                '?rollNo=' + encodeURIComponent(rollNo) +
                '&frameNo=' + encodeURIComponent(frameNo) +
                '&workcode=' + encodeURIComponent(workcode);
            
            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error('응답 오류: ' + response.status);

                const maxSrNoText = await response.text();
                const maxSrNo = parseInt(maxSrNoText, 10);
                if (isNaN(maxSrNo)) return [];

                const dataList = extractDataByWorkcode(rollNo, frameNo, maxSrNo, workcode);
                console.log("추출된 데이터: ", dataList);
                return dataList;
            } catch (error) {
                console.error("extractData() 오류:", error);
                return [];
            }
        }
        
        // 지목 데이터 추출 함수
        function extractLandBasicInfoData(rollNo, frameNo, maxSrNo) {
            const dataList = [];
            
            let maxNo = 0;
            let maxSrNoEdit = 0;
            	
            for (let i = 1; i <= 13; i++) {
            	let hasContent = false;
            	
            	//지목
            	const jimokTd_ = document.querySelector('td[data-type="jimok"][data-index="' + i + '"]');
            	const jimokDiv_ = jimokTd_.querySelector('div#jimok_' + i);
            	const jimokCheckbox_ = jimokTd_.querySelector('input.jimokLine');
            	//지적
            	const jijukTd = document.querySelector('td[data-type="jijuk"][data-index="' + i + '"]');
            	const jijukDiv_ = jijukTd.querySelector('div#jijuk_' + i);
                const jijukUnitDiv_ = jijukTd.querySelector('.jijukUnit');
                //등급1
                const grade1Td_ = document.querySelector('td[data-type="grade1"][data-index="' + i + '"]');
                const grade1Div_ = grade1Td_.querySelector('div#grade1_' + i);
                const gradeUnitDiv_ = grade1Td_.querySelector('.gradeUnit');
                const gradeCheckbox_ = grade1Td_.querySelector('input.gradeLine');
                //등급2
                const grade2Td_ = document.querySelector('td[data-type="grade2"][data-index="' + i + '"]');
                const grade2Div_ = grade2Td_.querySelector('div#grade2_' + i);
                
                if (jimokDiv_.textContent.trim() !== '') hasContent = true;
                if (jimokCheckbox_.checked) hasContent = true;
                if (jijukDiv_.textContent.trim() !== '') hasContent = true;
                if (jijukUnitDiv_.textContent.trim() !== '') hasContent = true;
                if (grade1Div_.textContent.trim() !== '') hasContent = true;
                if (gradeUnitDiv_.textContent.trim() !== '') hasContent = true;
                if (gradeCheckbox_.checked) hasContent = true;
                if (grade2Div_.textContent.trim() !== '') hasContent = true;
                
                if (hasContent) maxSrNoEdit++;
            }
            
            if (maxSrNo == 0 || maxSrNo < maxSrNoEdit) maxNo = maxSrNoEdit; 
            else maxNo = maxSrNo;
            
            for (let srNo = 1; srNo <= maxNo; srNo++) {
            	
                // 각 항목의 데이터 추출
                const dataRow = {
                    rollNo: rollNo,
                    frameNo: frameNo,
                    srNo: srNo
                };

                // 지목 데이터 추출
                const jimokTd = document.querySelector('td[data-type="jimok"][data-index="' + srNo + '"]');
                if (jimokTd) {
                    // 지목 값
                    const jimokDiv = jimokTd.querySelector('div#jimok_' + srNo);
                    if (jimokDiv) {
                        dataRow.jimok = jimokDiv.textContent.trim();
                    }

                    // 지목 라인 체크박스 값
                    const jimokCheckbox = jimokTd.querySelector('input.jimokLine');
                    if (jimokCheckbox) {
                    	dataRow.jimokLine = jimokCheckbox.checked ? '1' : '0';
                    }
                }

                // 지적 데이터 추출
                const jijukTd = document.querySelector('td[data-type="jijuk"][data-index="' + srNo + '"]');
                if (jijukTd) {
                    // 지적 값
                    const jijukDiv = jijukTd.querySelector('div#jijuk_' + srNo);
                    if (jijukDiv) {
                        dataRow.jijuk = jijukDiv.textContent.trim();
                    }

                    // 지적 단위 값
                    const jijukUnitDiv = jijukTd.querySelector('.jijukUnit');
                    if (jijukUnitDiv) {
                        const unitText = jijukUnitDiv.textContent.trim();
                        // 코드리스트에서 지적 단위 코드 찾기 (code2='0002')
                        if (codeListData.jimokCodeList && codeListData.jimokCodeList.length > 0) {
                            // code2 값이 '0002'인 코드 필터링
                            const jijukUnitCodeList = codeListData.jimokCodeList.filter(function(item) {
                                return String(item.code2) === '0002';
                            });

                            // 코드명으로 코드 찾기
                            const unitInfo = jijukUnitCodeList.find(function(item) {
                                const itemCodeName = item.codeName || item.code_name || item.CODE_NAME;
                                return itemCodeName === unitText;
                            });

                            if (unitInfo) {
                                // code3 값을 jijukUnit에 저장
                                dataRow.jijukUnit = unitInfo.code3 || unitInfo.CODE3;
                            } else {
                                // 매칭되는 코드가 없으면 원래 텍스트 사용
                                dataRow.jijukUnit = unitText;
                            }
                        } else {
                            dataRow.jijukUnit = unitText;
                        }
                    }
                }

                // 과세가격1 데이터 추출
                const grade1Td = document.querySelector('td[data-type="grade1"][data-index="' + srNo + '"]');
                if (grade1Td) {
                    // 과세가격1 값
                    const grade1Div = grade1Td.querySelector('div#grade1_' + srNo);
                    if (grade1Div) {
                        dataRow.grade1 = grade1Div.textContent.trim();
                    }

                    // 과세가격 단위 값
                    const gradeUnitDiv = grade1Td.querySelector('.gradeUnit');
                    if (gradeUnitDiv) {
                        const unitText = gradeUnitDiv.textContent.trim();
                        // 코드리스트에서 과세가격 단위 코드 찾기 (code2='0003')
                        if (codeListData.jimokCodeList && codeListData.jimokCodeList.length > 0) {
                            // code2 값이 '0003'인 코드 필터링
                            const gradeUnitCodeList = codeListData.jimokCodeList.filter(function(item) {
                                return String(item.code2) === '0003';
                            });

                            // 코드명으로 코드 찾기
                            const unitInfo = gradeUnitCodeList.find(function(item) {
                                const itemCodeName = item.codeName || item.code_name || item.CODE_NAME;
                                return itemCodeName === unitText;
                            });

                            if (unitInfo) {
                                // code3 값을 gradeUnit에 저장
                                dataRow.gradeUnit = unitInfo.code3 || unitInfo.CODE3;
                            } else {
                                // 매칭되는 코드가 없으면 원래 텍스트 사용
                                dataRow.gradeUnit = unitText;
                            }
                        } else {
                            dataRow.gradeUnit = unitText;
                        }
                    }

                    // 과세가격 라인 체크박스 값
                    const gradeCheckbox = grade1Td.querySelector('input.gradeLine');
                    if (gradeCheckbox) {
                        dataRow.gradeLine = gradeCheckbox.checked ? '1' : '0';
                    }
                }

                // 과세가격2 데이터 추출
                const grade2Td = document.querySelector('td[data-type="grade2"][data-index="' + srNo + '"]');
                if (grade2Td) {
                    // 과세가격2 값
                    const grade2Div = grade2Td.querySelector('div#grade2_' + srNo);
                    if (grade2Div) {
                        dataRow.grade2 = grade2Div.textContent.trim();
                    }
                }
                console.log('추출된 지목 데이터 확인:', srNo, dataRow);
                dataList.push(dataRow);
            };

            return dataList;
        }

        // 연혁 데이터 추출 함수
        function extractLandHistoryData(rollNo, frameNo, maxSrNo) {
            const dataList = [];

            for (let srNo = 1; srNo <= maxSrNo; srNo++) {
                // 각 항목의 데이터 추출
                const dataRow = {
                    rollNo: rollNo,
                    frameNo: frameNo,
                    srNo: srNo
                };

                // 연혁 데이터 추출
                const historyTd = document.querySelector('td[data-type="history"][data-index="' + srNo + '"]');
                if (historyTd) {
                    // 연혁 값
                    const historyDiv = historyTd.querySelector('div#hist_' + srNo);
                    if (historyDiv) {
                        dataRow.history = historyDiv.textContent.trim();
                    }
                }
                console.log('추출된 연혁 데이터 확인:', srNo, dataRow);
                dataList.push(dataRow);
            };

            return dataList;
        }

        // 날짜 데이터 추출 함수
        function extractLandDateInfoData(rollNo, frameNo, maxSrNo) {
            const dataList = [];

            for (let srNo = 1; srNo <= maxSrNo; srNo++) {
                // 각 항목의 데이터 추출
                const dataRow = {
                    rollNo: rollNo,
                    frameNo: frameNo,
                    srNo: srNo
                };

                // 날짜 데이터 추출
                const dateTd = document.querySelector('td[data-type="hisDate"][data-index="' + srNo + '"]');
                if (dateTd) {
                    // 날짜 값
                    const dateDiv = dateTd.querySelector('div#date_' + srNo);
                    if (dateDiv) {
                        dataRow.hisDate = dateDiv.textContent.trim();
                    }

                    // 연호 값
                    const yearDiv = dateTd.querySelector('div#year_' + srNo);
                    if (yearDiv) {
                        const yearText = yearDiv.textContent.trim();

                        // 코드리스트에서 연호 코드 찾기
                        if (codeListData.yearCodeList && codeListData.yearCodeList.length > 0) {
                            // 연호명으로 코드 찾기
                            const yearInfo = codeListData.yearCodeList.find(function(item) {
                                const itemYearName = item.yearName || item.YEAR_NAME;
                                return itemYearName === yearText;
                            });

                            if (yearInfo) {
                                // yearCode 값을 저장
                                dataRow.yearCode = yearInfo.yearCode || yearInfo.YEAR_CODE;
                            } else {
                                // 매칭되는 코드가 없으면 원래 텍스트 사용
                                dataRow.yearCode = yearText;
                            }
                        } else {
                            dataRow.yearCode = yearText;
                        }
                    }
                }

                // 사고 데이터 추출
                const reasonTd = document.querySelector('td[data-type="reason"][data-index="' + srNo + '"]');
                if (reasonTd) {
                    const reasonDiv = reasonTd.querySelector('div');
                    if (reasonDiv) {
                        dataRow.reason = reasonDiv.textContent.trim();
                    }
                }

                // 주소 데이터 추출
                const addressTd = document.querySelector('td[data-type="address"][data-index="' + srNo + '"]');
                if (addressTd) {
                    const addressDiv = addressTd.querySelector('div');
                    if (addressDiv) {
                        dataRow.address = addressDiv.textContent.trim();
                    }
                }
                console.log('추출된 날짜 데이터 확인:', srNo, dataRow);
                dataList.push(dataRow);
            };
	
            return dataList;
        }

        // 소유자 데이터 추출 함수
        function extractLandOwnerInfoData(rollNo, frameNo, maxSrNo) {
            const dataList = [];

            for (let srNo = 1; srNo <= maxSrNo; srNo++) {
                // 각 항목의 데이터 추출
                const dataRow = {
                    rollNo: rollNo,
                    frameNo: frameNo,
                    srNo: srNo
                };

                // 소유자 데이터 추출
                const nameTd = document.querySelector('td[data-type="name"][data-index="' + srNo + '"]');
                if (nameTd) {
                    // 소유자 값
                    const nameDiv = nameTd.querySelector('div#name_' + srNo);
                    if (nameDiv) {
                        dataRow.name = nameDiv.textContent.trim();
                    }
                }
                console.log('추출된 소유자 데이터 확인:', srNo, dataRow);

                dataList.push(dataRow);
            };

            return dataList;
        }

        // 색인 데이터 추출 함수
        function extractLandIndexInfoData(rollNo, frameNo) {
            const dataList = [];

            // 색인 데이터는 단일 레코드로 처리
            const dataRow = {
                rollNo: rollNo,
                frameNo: frameNo,
                srNo: '0' // 색인 데이터는 단일 레코드이므로 srNo를 0으로 고정
            };

            // 지명 데이터 추출
            const jimeungTd = document.querySelector('td[data-type="jimeung"]');
            if (jimeungTd) {
                const jimeungDiv = jimeungTd.querySelector('div.jimeung');
                if (jimeungDiv) {
                    dataRow.jimeung = jimeungDiv.textContent.trim();
                }
            }

            // 지번 데이터 추출
            const jibunTd = document.querySelector('td[data-type="jibun"]');
            if (jibunTd) {
                const jibunDiv = jibunTd.querySelector('div.jibun');
                if (jibunDiv) {
                    dataRow.jibun = jibunDiv.textContent.trim();
                }
            }

            // 등급 데이터 추출
            const gradeTd = document.querySelector('td[data-type="grade"]');
            if (gradeTd) {
                const gradeDiv = gradeTd.querySelector('div.grade');
                if (gradeDiv) {
                    dataRow.grade = gradeDiv.textContent.trim();
                }
            }

            // 적요 데이터 추출
            const jukyoTd = document.querySelector('td[data-type="jukyo"]');
            if (jukyoTd) {
                const jukyoDiv = jukyoTd.querySelector('div.jukyo');
                if (jukyoDiv) {
                    dataRow.jukyo = jukyoDiv.textContent.trim();
                }
            }
            console.log('추출된 색인 데이터 확인:', dataRow);
            
            dataList.push(dataRow);

            return dataList;
        }
        
     // 추출 함수 공통 인터페이스
        function extractDataByWorkcode(rollNo, frameNo, maxSrNo, workcode) {
            switch (workcode) {
                case '1':
                    return extractLandBasicInfoData(rollNo, frameNo, maxSrNo);
                case '2':
                    return extractLandHistoryData(rollNo, frameNo, maxSrNo);
                case '3':
                    return extractLandDateInfoData(rollNo, frameNo, maxSrNo);
                case '4':
                    return extractLandOwnerInfoData(rollNo, frameNo, maxSrNo);
                case '5':
                    return extractLandIndexInfoData(rollNo, frameNo);
            }
        }

        // 저장 및 다음 페이지 이동
        async function saveAndMoveToNextPage() {
            loading.style.display = 'flex';

            const rollElement = document.querySelector('#kwonList .list-group-item.active');
            const frameElement = document.querySelector('#pageList .list-group-item.active');
            const rollNo = rollElement ? rollElement.getAttribute('data-kwon') : '';
            const frameNo = frameElement ? frameElement.getAttribute('data-frame') : '';
            const workcode = '${workcode}';

            const saveUrlMap = {
                '1': '<c:url value="/msdata/land/saveLandBasicInfo.do"/>',
                '2': '<c:url value="/msdata/land/saveLandHistory.do"/>',
                '3': '<c:url value="/msdata/land/saveLandDateInfo.do"/>',
                '4': '<c:url value="/msdata/land/saveLandOwnerInfo.do"/>',
                '5': '<c:url value="/msdata/land/saveLandIndexInfo.do"/>'
            };

            try {
                const extractedData = await extractData(rollNo, frameNo, workcode);

                if (extractedData.length === 0) {
                    console.log('저장할 데이터가 없습니다. 다음 페이지로 이동합니다.');
                    moveToNextPage();
                    return;
                }

                const jsonData = { rollNo, frameNo, dataList: extractedData };
                const saveUrl = saveUrlMap[workcode];

                const response = await fetch(saveUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(jsonData)
                });

                if (!response.ok) throw new Error('서버 응답 오류: ' + response.status);

                const data = await response.json();
                if (data.success) {
                    console.log('데이터 저장 성공:', data.message);
                } else {
                    console.error('데이터 저장 실패:', data.message);
                }
            } catch (error) {
                console.error('저장 중 오류 발생:', error);
                alert('오류: ' + error.message);
            } finally {
                moveToNextPage();
            }
        }

        // '*' 키 이벤트 처리
        function setupSaveKeyEvent() {
            // 기존 이벤트 리스너 제거 (중복 방지)
            window.removeEventListener('keydown', handleSaveKeyDown);

            // 새 이벤트 리스너 추가
            window.addEventListener('keydown', handleSaveKeyDown);
        }

        // '*' 키 이벤트 처리 함수
        function handleSaveKeyDown(event) {
            // '*' 키 감지 (일반 '*' 키는 Shift+8, 키패드의 '*'는 106)
            if ((event.keyCode === 56 && event.shiftKey) || event.keyCode === 106) {
                event.preventDefault();
                const workcode = '${workcode}'; // JSP에서 전달받은 workcode 사용
                console.log('* 키 감지: keyCode=' + event.keyCode + ', workcode=' + workcode);

                // 모든 workcode에 대해 다음 페이지로 이동
                saveAndMoveToNextPage();
            }
        }

        // 페이지 정보는 MsDataVO와 ProjectVO에서 가져오므로 별도의 업데이트 함수가 필요 없습니다.

        // 템플릿 로드 후 이벤트 리스너 설정 함수 수정
        function setupTemplateEventListeners() {
            // 체크박스 및 포커스 이동 이벤트 리스너 설정
            setupCheckboxEventListeners();

            // 서제스트 팝업 이벤트 리스너 설정
            setupSuggestEventListeners();

            // 저장 키 이벤트 설정
            setupSaveKeyEvent();
        }

        // loadTemplate 함수 수정 - 템플릿 로드 후 이벤트 리스너 설정
        const originalLoadTemplate = loadTemplate;
        loadTemplate = function(rollNo, frameNo, workcode) {
            // 페이지 정보 로깅 (MsDataVO와 ProjectVO에서 가져오는 정보로 대체)
            console.log('템플릿 로드: 권 ' + rollNo + ', 페이지 ' + frameNo + ', 작업코드 ' + workcode);

            // Promise를 반환하도록 수정
            return new Promise((resolve, reject) => {
                try {
                    // 기존 loadTemplate 함수 호출
                    originalLoadTemplate(rollNo, frameNo, workcode);

                    // 데이터 로드가 완료되는 시간을 고려하여 약간의 지연 후 이벤트 리스너 설정
                    setTimeout(() => {
                        setupTemplateEventListeners();
                        resolve();
                    }, 500);
                } catch (error) {
                    console.error('Template loading error:', error);
                    reject(error);
                }
            });
        };

        // 호출 시점 (초기화) - 권 목록만 초기화
        createKwonList();
        // 페이지 목록은 권 선택 후 더블클릭 이벤트에서 로드됨

        // OpenSeadragon 초기화
        const viewer = OpenSeadragon({
            id: "viewer",
            prefixUrl: "https://cdnjs.cloudflare.com/ajax/libs/openseadragon/4.1.0/images/",
            showNavigationControl: false,
            maxZoomPixelRatio: 10,
            minZoomImageRatio: 0.8,
            zoomPerClick: 1.4,
            constrainDuringPan: true,
            visibilityRatio: 1.0,
            loadTilesWithAjax: false,
            gestureSettingsMouse: {
                clickToZoom: false,
                dblClickToZoom: false,
                // 스크롤 줌은 필요에 따라 설정 (예: true이면 활성화)
                scrollToZoom: true
            },
            // 초기에는 이미지를 로드하지 않음 - 페이지 더블클릭 시 로드됨
            showNavigator: false
        });

        // 로딩 상태 처리 - 초기에는 표시하지 않고 이미지 로드 시작시에만 표시
        const loading = document.getElementById('loading');
        // 초기에는 로딩 스피너 숨김
        loading.style.display = 'none';

        viewer.addHandler('open', function() {
            loading.style.display = 'none';
            const containerWidth = viewer.container.clientWidth;
            const containerHeight = viewer.container.clientHeight;
            const imageWidth = viewer.world.getItemAt(0).source.width;
            const imageHeight = viewer.world.getItemAt(0).source.height;

            // const scaleX = containerWidth / imageWidth;
            // const scaleY = containerHeight / imageHeight;
            // const scale = Math.min(scaleX, scaleY);

            // 가로세로 비율 계산
            const containerRatio = containerWidth / containerHeight;
            const imageRatio = imageWidth / imageHeight;

            let scale;

            // 조건: 이미지가 더 넓은 형태일 때 가로맞춤, 더 높은 형태일 땐 세로맞춤
            if (imageRatio >= containerRatio) {
                // 가로맞춤 (이미지가 container보다 더 가로로 넓음)
                scale = containerWidth / imageWidth;
            } else {
                // 세로맞춤 (이미지가 container보다 더 세로로 김)
                scale = containerHeight / imageHeight;
            }
            viewer.viewport.zoomTo(scale);

            const centerX = (containerWidth - imageWidth * scale) / 2;
            const centerY = (containerHeight - imageHeight * scale) / 2;
            viewer.viewport.panTo(new OpenSeadragon.Point(centerX, centerY));

            viewer.viewport.goHome(true);
            viewer.forceRedraw();

            // 이미지 로드 완료 - 추가 처리가 필요하면 여기에 추가
        });

        viewer.addHandler('open-failed', function() {
            loading.style.display = 'none';
            alert('이미지 로드에 실패했습니다.');
        });

        // 이미지 로드 함수 - 파일명 직접 지정
        function loadImage(fileName) {
            loading.style.display = 'flex';
            // 이전 페이지에서 전달받은 workcode 사용
            const workcode = '${workcode}';

            // 이미지 URL 생성 - workcode와 파일명 정보 포함
            const imageUrl = '<c:url value="/image/crop"/>' +
                     '?fileName=' + encodeURIComponent(fileName) +
                     '&workcode=' + encodeURIComponent(workcode);

            // 이미지 로드 시도
            fetch(imageUrl, { method: 'HEAD' })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    // 이미지 로드 성공 시 OpenSeadragon에서 열기
                    viewer.open({
                        type: 'image',
                        url: imageUrl
                    });
                })
                .catch(error => {
                    console.error('Error loading image:', error);
                    alert(`이미지 로드 중 오류가 발생했습니다: ${error.message}`);
                    loading.style.display = 'none';
                });
        }

        // 이미지 로드 함수 - 권번호와 페이지번호로 로드
        function loadImageByRollAndFrame(rollNo, frameNo) {
            loading.style.display = 'flex';
            // 이전 페이지에서 전달받은 workcode 사용
            const workcode = '${workcode}';

            // 먼저 파일명 정보 가져오기
            const fileInfoUrl = '<c:url value="/msdata/fileName"/>' +
                     '?roll=' + encodeURIComponent(rollNo) +
                     '&frame=' + encodeURIComponent(frameNo);

            // 파일 정보 조회 후 이미지 로드
            fetch(fileInfoUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(fileInfo => {

                    // 이미지 URL 생성 - workcode와 파일명 정보 포함
                    const imageUrl = '<c:url value="/image/crop"/>' +
                             '?rollNo=' + encodeURIComponent(rollNo) +
                             '&frameNo=' + encodeURIComponent(frameNo) +
                             '&workcode=' + encodeURIComponent(workcode);

                    // 이미지 로드 시도
                    return fetch(imageUrl, { method: 'HEAD' })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            // 이미지 로드 성공 시 OpenSeadragon에서 열기
                            viewer.open({
                                type: 'image',
                                url: imageUrl
                            });
                        });
                })
                .catch(error => {
                    console.error('Error loading image:', error);
                    alert(`이미지 로드 중 오류가 발생했습니다: ${error.message}`);
                    loading.style.display = 'none';
                });
        }

        // 창 크기 변경 시 이미지 크기 조절
        window.addEventListener('resize', function() {
            if (viewer.world.getItemCount() > 0) {
                const containerWidth = viewer.container.clientWidth;
                const containerHeight = viewer.container.clientHeight;
                const imageWidth = viewer.world.getItemAt(0).source.width;
                const imageHeight = viewer.world.getItemAt(0).source.height;

                const scaleX = containerWidth / imageWidth;
                const scaleY = containerHeight / imageHeight;
                const scale = Math.min(scaleX, scaleY);

                viewer.viewport.zoomTo(scale);

                const centerX = (containerWidth - imageWidth * scale) / 2;
                const centerY = (containerHeight - imageHeight * scale) / 2;
                viewer.viewport.panTo(new OpenSeadragon.Point(centerX, centerY));
            }
        });

        // <th> 요소 클릭 시 삽입/삭제 기능 구현
        function setupThActionMenu() {
            // 액션 메뉴 생성
            const actionMenu = document.createElement('div');
            actionMenu.className = 'th-action-menu';
            actionMenu.style.display = 'none';
            actionMenu.innerHTML = `
                <button class="insert"><i class="fas fa-plus-circle me-2"></i>삽입</button>
                <button class="delete"><i class="fas fa-minus-circle me-2"></i>삭제</button>
            `;
            document.body.appendChild(actionMenu);

            // 현재 선택된 th 요소
            let currentTh = null;

            // th 요소 클릭 이벤트 처리
            function setupThClickEvents() {
                const workcode = '${workcode}';
                let maxIdx = 0;
                
                if(workcode === '1' || workcode === '2') {
                    maxIdx = 13;
                }
                else if(workcode === '3' || workcode === '4') {
                    maxIdx = 24;
                }
                else {
                    return;
                }

                // 모든 th 요소에 클릭 이벤트 추가
                document.querySelectorAll('th').forEach(th => {
                    // 기존 이벤트 리스너 제거 (중복 방지)
                    th.removeEventListener('click', handleThClick);

                    // 새 이벤트 리스너 추가
                    th.addEventListener('click', handleThClick);
                });

                // 문서 클릭 시 액션 메뉴 닫기
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('th') && !e.target.closest('.th-action-menu')) {
                        actionMenu.style.display = 'none';
                    }
                });

                // 삽입 버튼 클릭 이벤트
                actionMenu.querySelector('.insert').addEventListener('click', function() {
                    if (currentTh) {
                        const index = getThIndex(currentTh);
                        if (index !== null) {
                            shiftColumnData(workcode, index, maxIdx, 1);
                            actionMenu.style.display = 'none';
                        }
                    }
                });

                // 삭제 버튼 클릭 이벤트
                actionMenu.querySelector('.delete').addEventListener('click', function() {
                    if (currentTh) {
                        const index = getThIndex(currentTh);
                        if (index !== null) {
                            shiftColumnData(workcode, index, maxIdx, -1);
                            actionMenu.style.display = 'none';
                        }
                    }
                });
            }

            // th 요소 클릭 이벤트 핸들러
            function handleThClick(e) {
                e.preventDefault();
                e.stopPropagation();

                // 현재 th 요소 저장
                currentTh = e.currentTarget;

                // 액션 메뉴 위치 설정
                const rect = currentTh.getBoundingClientRect();
                actionMenu.style.left = rect.left + 'px';
                actionMenu.style.top = (rect.bottom + 5) + 'px';

                // 액션 메뉴 표시
                actionMenu.style.display = 'block';
            }

            // th 요소의 인덱스 가져오기
            function getThIndex(th) {
                // data-index 속성이 있는 경우
                if (th.hasAttribute('data-index')) {
                    return parseInt(th.getAttribute('data-index'));
                }

                // 텍스트 내용이 숫자인 경우
                const text = th.textContent.trim();
                if (/^\d+$/.test(text)) {
                    return parseInt(text);
                }

                return null;
            }

            function shiftColumnData(workcode, startIndex, endIndex, step) {
                const leftContainer = document.querySelector('.left-data');
                const rightContainer = document.querySelector('.right-data');
                if (!leftContainer || !rightContainer) return;

                // 행 종류 정의
                let rowTypes = [];
                if (workcode === '1') rowTypes = ['jimok', 'jijuk', 'grade1', 'grade2'];
                else if (workcode === '2') rowTypes = ['hist'];
                else if (workcode === '3') rowTypes = ['date', 'reason', 'addr'];
                else if (workcode === '4') rowTypes = ['name'];
                else return;

                // 교차 영향을 주는 인덱스 정의
                let boundaryLeft = 7;
                let boundaryRight = 6;
                if (parseInt(endIndex) === 24) {
                    boundaryLeft = 12;
                    boundaryRight = 11;
                }

                let indices = [];
                if (step > 0) {
                    for (let i = endIndex; i >= startIndex; i--) indices.push(i); // 삽입
                } else {
                    for (let i = startIndex; i <= endIndex; i++) indices.push(i); // 삭제
                }

                // 행별 반복 처리
                rowTypes.forEach(rowClass => {
                    const leftRow = leftContainer.querySelector('tr.' + rowClass);
                    const rightRow = rightContainer.querySelector('tr.' + rowClass);
                    // if (!leftRow || !rightRow) return;

                    indices.forEach(index => {
                        let sourceTd = null;
                        let targetTd = null;

                        if (step > 0) { // 삽입
                            if (index === boundaryRight) {
                                sourceTd = rightRow.querySelector('td[data-index="' + index + '"]');
                                targetTd = leftRow.querySelector('td[data-index="' + (index + 1) + '"]');
                            } else {
                                const containerRow = (index >= boundaryLeft) ? leftRow : rightRow;
                                sourceTd = containerRow.querySelector('td[data-index="' + index + '"]');
                                if (index === endIndex) {
                                	targetTd = containerRow.querySelector('td[data-index="' + index + '"]');
                                } else {
                                	targetTd = containerRow.querySelector('td[data-index="' + (index + 1) + '"]');
                                }
                            }
                        } else if (step < 0) { // 삭제
                        	if (index === boundaryRight) {
                                sourceTd = leftRow.querySelector('td[data-index="' + (index + 1) + '"]');
                                targetTd = rightRow.querySelector('td[data-index="' + index + '"]');
                            } else {
                                const containerRow = (index > boundaryRight) ? leftRow : rightRow;
                                if (index === endIndex) {
                                	sourceTd = containerRow.querySelector('td[data-index="' + index + '"]');
                                } else {
                                	sourceTd = containerRow.querySelector('td[data-index="' + (index + 1) + '"]');
                                }
                                targetTd = containerRow.querySelector('td[data-index="' + index + '"]');
                            }
                        }
                        if (!sourceTd || !targetTd) return;
                        
                        if (workcode === '1') {
                            // ✅ input 복사
                            const sourceInput = sourceTd.querySelectorAll('input');
                            const targetInput = targetTd.querySelectorAll('input');
                            
                           	sourceInput.forEach((srcInp, idx) => {
                           		const tgtInp = targetInput[idx];
                           		tgtInp.value = srcInp.checked ? '1' : '0';
                           		tgtInp.checked = srcInp.checked;
                           		
                           		sourceTd.classList.remove('slash');
                                targetTd.classList.remove('slash');
                                sourceTd.classList.remove('slash2');
                                targetTd.classList.remove('slash2');
                                
                                const isRightData = (sourceTd.closest('.right-data') !== null) || (targetTd.closest('.right-data') !== null);
                                if (srcInp.checked){
                                    
                                    if (isRightData) {
                                    	sourceTd.classList.add('slash');
                                    } else {
                                    	sourceTd.classList.add('slash2');
                                    }
                                }
                                if (tgtInp.checked){
                                	if (isRightData) {
                                		targetTd.classList.add('slash');
                                    } else {
                                    	targetTd.classList.add('slash2');
                                    }
                                }
                           	});
                           	
                           	if (step < 0) {
                                if(index === endIndex) {
                                	sourceInput.forEach((srcInp, idx) => {
                                		srcInp.value = '0';
                                    	srcInp.checked = false;
                                		
                                    	sourceTd.classList.remove('slash');
                                    	sourceTd.classList.remove('slash2');
                                    });
                                }
                            }
                            
                            if(step > 0) {
                                if(index === startIndex) {
                                	sourceInput.forEach((srcInp, idx) => {
                                		srcInp.value = '0';
                                		srcInp.checked = false;
                                		
                                		sourceTd.classList.remove('slash');
                                		sourceTd.classList.remove('slash2');
                                	});
                                }
                            }
                        }

                        // ✅ div 복사
                        const sourceDivs = sourceTd.querySelectorAll('div');
                        const targetDivs = targetTd.querySelectorAll('div');
                        
                        if (step < 0) {
                        	if (index === endIndex ) {
                        		sourceDivs.forEach((srcDiv, idx) => {
                                    if (srcDiv) {
                                        srcDiv.textContent = '';
                                    }
                        		});
                        	}
                        }
                        
                        sourceDivs.forEach((srcDiv, idx) => {
                            const tgtDiv = targetDivs[idx];

                            if (tgtDiv) {
                                tgtDiv.textContent = srcDiv.textContent;
                            }
                        });
                        
                        if (step > 0) {
                        	if (index === startIndex) {
                        		sourceDivs.forEach((srcDiv, idx) => {
                                    if (srcDiv) {
                                    	srcDiv.textContent = '';
                                    }
                        		});
                        	}
                        }
                    });
                });
            }

         	// 이벤트 리스너 재설정
            setupFocusNavigation();
            setupSuggestEventListeners();
            
            // 초기 설정
            setupThClickEvents();
        }

        // 템플릿 로드 후 이벤트 리스너 설정 함수 수정
        const originalSetupTemplateEventListeners = setupTemplateEventListeners;
        setupTemplateEventListeners = function() {
            // 기존 이벤트 리스너 설정
            originalSetupTemplateEventListeners();

            // <th> 요소 클릭 이벤트 설정
            setupThActionMenu();
        };
    </script>
</body>
</html>
