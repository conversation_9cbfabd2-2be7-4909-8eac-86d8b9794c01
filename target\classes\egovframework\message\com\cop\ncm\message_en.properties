#\uba85\ud568\ubaa9\ub85d \uc870\ud68c
#EgovNcrdList.jsp
comCopNcm.ncrdList.title=Public Business Card List
comCopNcm.ncrdList.submit=Send
comCopNcm.ncrdList.searchWordInput=Enter Search Words
comCopNcm.ncrdList.nrcdSearch=Business card inquiry
comCopNcm.ncrdList.multiList=Number, name, company name, department name, registration date, use registration list.

#EgovNcrdInqirePopup.jsp
comCopNcm.ncrdInqirePopup.title=Contact information

#EgovMyNcrdList.jsp
comCopNcm.myNcrdList.title=My business card list

#EgovNcrdRegist.jsp
comCopNcm.ncrdRegist.title=Business Card Registration
comCopNcm.ncrdRegist.idNo=Identification number

#EgovNcrdUpdt.jsp
comCopNcm.ncrdUpdt.title=Business Card Update

#EgovNcrdRegist.xml
comCopNcm.ncrdRegist.validate.areaNo=Area code
comCopNcm.ncrdRegist.validate.middleTelNo=4 digit station number
comCopNcm.ncrdRegist.validate.endTelNo=4 digits at end of phone
comCopNcm.ncrdRegist.validate.middleMbtlNum=4 digits in front of your phone
comCopNcm.ncrdRegist.validate.endMbtlNum=4 digits at the end of your phone number
comCopNcm.ncrdRegist.validate.detailAdres=Detailed Address

#370\uba85\ud568\uad00\ub9ac , 371.\ub0b4\uba85\ud568\ubaa9\ub85d \uae40\uc608\uc601J#
cop.extrlUser = External User
cop.intrlUser = Internal User
cop.private = private
cop.public = public

cop.adbkNm = Address Name
cop.othbcScope = Disclose Range
cop.company = compnay
cop.part = part
cop.man = man
cop.adbkUser = member
cop.bbsNm = BBS Name
cop.bbsIntrcn = BBS Introduction
cop.bbsTyCode = BBS Type
cop.bbsAttrbCode = BBS Attribute
cop.replyPosblAt = Reply Possible Alternative
cop.fileAtchPosblAt = File Attach Possible Alternative
cop.posblAtchFileNumber = Possible Attach File Number
cop.tmplatId = Template Information
cop.guestList.subject = This article registered by Guest List
cop.nttSj = Notice Subject
cop.nttCn = Notice Contents
cop.ntceBgnde = Notice Start Date
cop.ntceEndde = Notice End Date
cop.ntcrNm = Noticer Name
cop.password = PassWord
cop.atchFile = Attach Files
cop.guestList = Guest List
cop.guestListCn = Guest List Contents
cop.noticeTerm = Notice term
cop.atchFileList = Attached File List
cop.cmmntyNm = Community Name
cop.cmmntyIntrcn = Community Introduction
cop.cmmntyMngr = Community Manager
cop.clbOprtr = Club Operator
cop.clbIntrcn = Club Introduction
cop.clbNm = Club Name
cop.tmplatNm = Template Name
cop.tmplatSeCode = Template Se Code
cop.tmplatCours = Template Cours
cop.useAt = Use Alternative
cop.ncrdNm = NameCard user name
cop.cmpnyNm = Company name
cop.deptNm = Department name
cop.ofcpsNm = OFCPS name
cop.clsfNm = Class Name
cop.emailAdres = E-mail
cop.telNo =  Tel No.
cop.mbtlNum = Mobile
cop.adres = Address
cop.extrlUserAt = External User alternative
cop.publicAt = Public open alternative
cop.remark = Remark
cop.trgetNm = Company/Club Information
cop.preview = preview

cop.withdraw.msg=confirm withdrawal memebership?
cop.reregist.msg=confirm re-registration?
cop.registmanager.msg=confirm registration of manager?
cop.use.msg=confirm use?
cop.unuse.msg=confirm stop using?
cop.delete.confirm.msg=If you choose to disable the re-use change is impossible.
cop.ing.msg=Approval is being requested.
cop.request.msg=Signup is normally requested.
cop.password.msg=Please enter your password.
cop.password.not.same.msg=Password do not match.

cop.comment.wrterNm = Writer Name
cop.comment.commentCn = Comment
cop.comment.commentPassword = Password

cop.satisfaction.wrterNm = Writer Name
cop.satisfaction.stsfdgCn = Satisfaction
cop.satisfaction.stsfdg = Satisfaction Degree
cop.satisfaction.stsfdgPassword = Password

cop.scrap.scrapNm = Scrap Name