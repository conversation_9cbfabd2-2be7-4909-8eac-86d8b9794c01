#Login Screen Image Management#
ussIonLsi.loginScrinImageList.loginScrinImageList=Login Screen Image List
ussIonLsi.loginScrinImageList.imageNm=Image Name
ussIonLsi.loginScrinImageList.image=Image
ussIonLsi.loginScrinImageList.imageDc=Image Description
ussIonLsi.loginScrinImageList.reflctAtt=Applicability
ussIonLsi.loginScrinImageList.noImage=No Login image selected.
ussIonLsi.loginScrinImageList.failInquire=No results were inquired.
ussIonLsi.loginScrinImageList.deleteImage=Will you delete it?

ussIonLsi.loginScrinImageRegist.loginScrinImageRegist=Login Screen Image Regist
ussIonLsi.loginScrinImageRegist.mainImageNm=Image Name
ussIonLsi.loginScrinImageRegist.mainImage=Image
ussIonLsi.loginScrinImageRegist.mainImageId=Image ID
ussIonLsi.loginScrinImageRegist.mainImageDc=Image Description
ussIonLsi.loginScrinImageRegist.mainImageReflctAt=Applicability
ussIonLsi.loginScrinImageRegist.mainImageregDate=Registration date
ussIonLsi.loginScrinImageRegist.saveImage=Do you want to save it?
ussIonLsi.loginScrinImageRegist.ImageReq=Image is required.
ussIonLsi.loginScrinImageRegist.deleteImage=Will you delete it?

ussIonLsi.loginScrinImageUpdt.loginScrinImageUpdt=Login Screen Image Update
ussIonLsi.loginScrinImageUpdt.mainImageNm=Image Name
ussIonLsi.loginScrinImageUpdt.mainImage=Image
ussIonLsi.loginScrinImageUpdt.mainImageId=Image ID
ussIonLsi.loginScrinImageUpdt.mainImageDc=Image Description
ussIonLsi.loginScrinImageUpdt.mainImageReflctAt=Applicability
ussIonLsi.loginScrinImageUpdt.mainImageregDate=Registration date
ussIonLsi.loginScrinImageUpdt.saveImage=Do you want to save it?
ussIonLsi.loginScrinImageUpdt.deleteImage=Will you delete it?

ussIonLsi.loginScrinImageView.loginScrinImageView=Login Screen image reflected
ussIonLsi.loginScrinImageView.loginScrinImageViewDc=This page is where 750. images registered in Login Screen Image Management are reflected.