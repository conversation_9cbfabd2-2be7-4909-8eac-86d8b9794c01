#   Statistics Bulletin Board
comStsBst.bbsStats.title = Statistics Bulletin Board
comStsBst.bbsStats.fromDate = Start Date
comStsBst.bbsStats.toDate = End Date
comStsBst.bbsStats.periodKind = Type of Period
comStsBst.bbsStats.select = Select
comStsBst.bbsStats.byYear = Yearly
comStsBst.bbsStats.byMonth = Monthly
comStsBst.bbsStats.byDay = Daily

comStsBst.bbsStats.statKind = Type of Statistics
comStsBst.bbsStats.selectPlease = Please Select
comStsBst.bbsStats.statKind1 = By Type 
comStsBst.bbsStats.statKind2 = By Template
comStsBst.bbsStats.statKind3 = By Property

comStsBst.bbsStats.tab1 = Number os Posts
comStsBst.bbsStats.tab2 = Total Read Count
comStsBst.bbsStats.tab3 = Average of Read Count
comStsBst.bbsStats.tab4 = Top/Minimum View 
comStsBst.bbsStats.tab5 = Top Publisher

comStsBst.bbsStats.statResult = Statistical Results

comStsBst.bbsStats.tab1.subTitle1 = Graph (Unit, Number)
comStsBst.bbsStats.tab1.subTitle2 = Text (Unit, Number)
comStsBst.bbsStats.tab2.subTitle1 = Graph (Unit, Times)
comStsBst.bbsStats.tab2.subTitle2 = Text (Unit, Times)
comStsBst.bbsStats.tab3.subTitle1 = Graph (Unit, Times)
comStsBst.bbsStats.tab3.subTitle2 = Text (Unit, Times)
comStsBst.bbsStats.tab4.subTitle1 = Top View
comStsBst.bbsStats.tab4.subTitle2 = Minimum View
comStsBst.bbsStats.tab4.createdDate = Created Date
comStsBst.bbsStats.tab4.number = Number
comStsBst.bbsStats.tab4.title = Title
comStsBst.bbsStats.tab4.readCount = Read Count
comStsBst.bbsStats.tab5.subTitle1 = Top Publisher Info
comStsBst.bbsStats.tab5.writerID = Publisher ID
comStsBst.bbsStats.tab5.count = Number of Publications
comStsBst.bbsStats.tab5.statDate = Aggregation Date

comStsBst.validate.fromDateCheck = Enter period start date.
comStsBst.validate.toDateCheck = Enter period end date.
comStsBst.validate.periodKindCheck = Select period classification.
comStsBst.validate.statKindCheck = Select statistic classification.
comStsBst.validate.tabCheck = Search for top/minimum view, the top publisher information applies to daily searches only the top posters.