# Statistics Connects
comStsCst.conectStats.title = Statistics Connects
comStsCst.conectStats.fromDate = Start Date
comStsCst.conectStats.toDate = End Date
comStsCst.conectStats.periodKind = Type of Period
comStsCst.conectStats.select = Select
comStsCst.conectStats.byYear = Yearly
comStsCst.conectStats.byMonth = Monthly
comStsCst.conectStats.byDay = Daily

comStsCst.conectStats.statKind = Type of Statistics
comStsCst.conectStats.selectPlease = Please Select
comStsCst.conectStats.statKind1 = By Service
comStsCst.conectStats.statKind2 = By Personal

comStsCst.conectStats.statResult = Statistical Results
comStsCst.conectStats.results.col1 = Date
comStsCst.conectStats.results.col2 = Method
comStsCst.conectStats.results.col3 = Create
comStsCst.conectStats.results.col4 = Modify
comStsCst.conectStats.results.col5 = Inquery
comStsCst.conectStats.results.col6 = Delete
comStsCst.conectStats.results.col7 = Print
comStsCst.conectStats.results.col8 = Error

comStsCst.conectStats.results.unit = Times

comStsCst.conectStats.subTitle1 = Graph (Unit, Times)
comStsCst.conectStats.subTitle2 = Text (Unit, Times)

comStsCst.validate.fromDateCheck = Enter period start date.
comStsCst.validate.toDateCheck = Enter period end date.
comStsCst.validate.periodKindCheck = Select period classification.
comStsCst.validate.statKindCheck = Select statistic classification.
