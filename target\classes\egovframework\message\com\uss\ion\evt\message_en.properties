# Event Application Management / Event Reception Management / Event Reception Approval Management comUssIonEvt

# EgovEventManage.xml
comUssIonEvt.eventManage.validate.eventSe = Event Category
comUssIonEvt.eventManage.validate.eventNm = Event Name
comUssIonEvt.eventManage.validate.eventPurps = Event Purpose
comUssIonEvt.eventManage.validate.eventBeginDe = Date the event started
comUssIonEvt.eventManage.validate.eventEndDe = End date of the event
comUssIonEvt.eventManage.validate.eventAuspcInsttNm = Name of host organization
comUssIonEvt.eventManage.validate.eventMngtInsttNm = Name of event organization
comUssIonEvt.eventManage.validate.eventPlace = Venue
comUssIonEvt.eventManage.validate.eventCn = Event content
comUssIonEvt.eventManage.validate.ctOccrrncAt = Cost incurred
comUssIonEvt.eventManage.validate.partcptCt = Participation Cost
comUssIonEvt.eventManage.validate.psncpa =capacity
comUssIonEvt.eventManage.validate.rceptBeginDe = Received start date
comUssIonEvt.eventManage.validate.rceptEndDe = End date of receipt

# EgovEventAtdrn.xml
comUssIonEvt.eventAtdrn.validate.eventId = Event ID
comUssIonEvt.eventAtdrn.validate.sanctnerId = Responder ID

# EgovEventReqstManageList.jsp
comUssIonEvt.eventReqstManageList.title = Event Management List

# EgovEventReqstRegist.jsp
comUssIonEvt.eventReqstRegist.title = Registration

# EgovEventReqstDetail.jsp
comUssIonEvt.eventReqstDetail.title = Event Details

# EgovEventReqstUpdt.jsp
comUssIonEvt.eventReqstUpdt.title = Edit Events
comUssIonEvt.eventReqstUpdt.eventManageUpdt = Edit Event Management

# EgovEventReqstAtdrnList.jsp
comUssIonEvt.eventReqstAtdrnList.title = Event attendee list
comUssIonEvt.eventReqstAtdrnList.eventTemp1 = attendee name
comUssIonEvt.eventReqstAtdrnList.eventTemp2 = belongs
comUssIonEvt.eventReqstAtdrnList.eventTemp4 = Category
comUssIonEvt.eventReqstAtdrnList.eventTemp4.A = Apply
comUssIonEvt.eventReqstAtdrnList.eventTemp4.C = Agree
comUssIonEvt.eventReqstAtdrnList.eventTemp4.R = Disagree

#EgovEventRceptConfm jsp
comUssIonEvt.eventRceptConfm.title = Approval List of Events
comUssIonEvt.eventRceptConfm.validate.checkedEvent = Approval There is no target to process. Approval You can select an object to process and process it.
comUssIonEvt.eventRceptConfm.eventParticipateApply.agree = Apply for Event - Approve
comUssIonEvt.eventRceptConfm.eventParticipateApply.disagree = Application for registration - Penalty
comUssIonEvt.eventRceptConfm.eventRcptMngList = List of approvals received

# EgovEventRceptManageList.jsp
comUssIonEvt.eventRceptManageList.title = Order Management List
comUssIonEvt.eventRceptManageList.proccessCategory = Exercise Classification
comUssIonEvt.eventRceptManageList.searchConfmAt = Selection Category
comUssIonEvt.eventRceptManageList.searchConfmAt.non = Before submission
comUssIonEvt.eventRceptManageList.searchConfmAt.a = Application for Mediation
comUssIonEvt.eventRceptManageList.searchConfmAt.c = Approval
comUssIonEvt.eventRceptManageList.searchConfmAt.r = Please refuse
comUssIonEvt.eventRceptManageList.applyDetail = Apply (learn more)
comUssIonEvt.eventRceptManageList.confmAt = Apply

# EgovEventRceptDetail.jsp
comUssIonEvt.eventRceptDetail.title = Receive eligibility details inquiry
comUssIonEvt.eventRceptDetail.validate.confirmCancle = Are you sure you want to cancel this application?
comUssIonEvt.eventRceptDetail.eventManageVO.eventTemp7 = belongs
comUssIonEvt.eventRceptDetail.confirmCancle = Cancel application

# EgovEventRceptRegist.jsp
comUssIonEvt.eventRceptRegist.title = Application for Event Application
comUssIonEvt.eventRceptRegist.infrmlSanctn = Author

#Common
comUssIonEvt.common.validate.searchYearValue = You can not view only months in the entire year. Please select the year
comUssIonEvt.common.searchSe = Event Category
comUssIonEvt.common.selectedAll = All
comUssIonEvt.common.eventYearMonth = Date of the event
comUssIonEvt.common.searchYear = Year of the event
comUssIonEvt.common.searchMonth = Event month
comUssIonEvt.common.year = year
comUssIonEvt.common.month = month
comUssIonEvt.common.eventNm = Event Name
comUssIonEvt.common.eventPlace = Event Place
comUssIonEvt.common.eventTemp3 = Event Category
comUssIonEvt.common.eventBeginDe.eventEndDe = Date of the event
comUssIonEvt.common.eventTemp1 = Period
comUssIonEvt.common.prcpPpl = Participation /capacity
comUssIonEvt.common.rceptBeginDe.rceptEndDe = Date of the event
comUssIonEvt.common.days = day
comUssIonEvt.common.validate.vEventBeginDefastervEventEndDe = The event start date is later than the event end date. Please check the promotion period.
comUssIonEvt.common.validate.vRceptBeginDeFastervRceptEndDe = The start date of the event is later than the end date of the event. Please check the application period.
comUssIonEvt.common.validate.vRceptEndDeFastervEventBeginDe = Entries must be received before the start date of the event. Please confirm the period / period of the event.
comUssIonEvt.common.validate.urlCheckvRefrnUrl = The format of the reference URL is different from the URL format. Please check.
comUssIonEvt.common.validate.isNaNvPartcptCt = You can only enter numbers for entry fee.
comUssIonEvt.common.validate.vPartcptCtZero = You must enter more than 0 yen if participation fee is charged. Do not know.
comUssIonEvt.common.validate.isNaNvPsncpa = Only numbers can be entered in thecapacity.
comUssIonEvt.common.validate.vPsncpaZero = You must enter zero or more. Please do not.
comUssIonEvt.common.eventPurps = Event Purpose
comUssIonEvt.common.eventDe = Duration of the event
comUssIonEvt.common.eventBeginDe = Date the event started
comUssIonEvt.common.eventEndDe = End date of the event
comUssIonEvt.common.eventAuspcInsttNm = Organize Events
comUssIonEvt.common.eventMngtInsttNm = Organize Events
comUssIonEvt.common.refrnUrl = Reference URL
comUssIonEvt.common.eventCn = Event content
comUssIonEvt.common.ctOccrrncAt = Participation Cost
comUssIonEvt.common.free = Free
comUssIonEvt.common.fee = Paid
comUssIonEvt.common.feePrice = Amount paid
comUssIonEvt.common.feeUnit = 10,000 Won
comUssIonEvt.common.psncpa =capacity
comUssIonEvt.common.rceptBeginDe = Start date of the event
comUssIonEvt.common.rceptEndDe = End date of the event
comUssIonEvt.common.init = Initialize
comUssIonEvt.common.submit = Send
comUssIonEvt.common.toNewWindow = In a new window
comUssIonEvt.common.titleIconImage = Image of title icon
comUssIonEvt.common.confrm = Agree
comUssIonEvt.common.return = Disagree
comUssIonEvt.common.applcntNm = Applicant
comUssIonEvt.common.sanctnDt = Approval date