#Message Management#
#1\ucc28\uc644\uc131
#EgovNoteManage.jsp
comUssIonNtm.NoteManage.title = Note Management
comUssIonNtm.NoteMange.subject  = Title
comUssIonNtm.NoteMange.receiver = Receiver
comUssIonNtm.NoteMange.receiverList = Receiver List
comUssIonNtm.NoteMange.reception = TO
comUssIonNtm.NoteMange.reference = CC
comUssIonNtm.NoteMange.delete = Delete
comUssIonNtm.NoteMange.content = Contents
comUssIonNtm.NoteMange.send = Send
comUssIonNtm.NoteMange.summary = You can take a note service.
comUssIonNtm.NoteMange.alert.noList = The delete list has no one!
comUssIonNtm.NoteMange.success = Note sent complete.

#EgovNoteEmpList.jsp
comUssIonNtm.NoteEmpList.title = Note Management(Sending) - Select
comUssIonNtm.NoteEmpList.name = Name
comUssIonNtm.NoteEmpList.id = Id
comUssIonNtm.NoteEmpList.tel = Tel
comUssIonNtm.NoteEmpList.number = No
comUssIonNtm.NoteEmpList.address = Address
comUssIonNtm.NoteEmpList.all = Select All