<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Wed May 11 15:49:38 KST 2016-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="FileManageDAO">

	<resultMap id="fileList" type="egovframework.com.cmm.service.FileVO">
		<result property="atchFileId" column="ATCH_FILE_ID"/>
		<result property="fileCn" column="FILE_CN"/>
		<result property="fileExtsn" column="FILE_EXTSN"/>
		<result property="fileMg" column="FILE_SIZE"/>
		<result property="fileSn" column="FILE_SN"/>
		<result property="fileStreCours" column="FILE_STRE_COURS"/>
		<result property="orignlFileNm" column="ORIGNL_FILE_NM"/>
		<result property="streFileNm" column="STRE_FILE_NM"/>
		<result property="creatDt" column="CREAT_DT"/>			
	</resultMap>

	<resultMap id="fileDetail" type="egovframework.com.cmm.service.FileVO">
		<result property="atchFileId" column="ATCH_FILE_ID"/>
		<result property="fileCn" column="FILE_CN"/>
		<result property="fileExtsn" column="FILE_EXTSN"/>
		<result property="fileMg" column="FILE_SIZE"/>
		<result property="fileSn" column="FILE_SN"/>
		<result property="fileStreCours" column="FILE_STRE_COURS"/>
		<result property="orignlFileNm" column="ORIGNL_FILE_NM"/>
		<result property="streFileNm" column="STRE_FILE_NM"/>
	</resultMap>	


 	<select id="selectFileList" parameterType="FileVO" resultMap="fileList">
 		
			SELECT 
				a.ATCH_FILE_ID, b.FILE_CN, b.FILE_SN, b.FILE_STRE_COURS, b.STRE_FILE_NM,
				b.FILE_EXTSN, b.ORIGNL_FILE_NM, b.FILE_SIZE, a.CREAT_DT
			FROM
				COMTNFILE a, COMTNFILEDETAIL b
			WHERE
				a.ATCH_FILE_ID = #{atchFileId}
			AND 
				a.ATCH_FILE_ID = b.ATCH_FILE_ID
			AND 
				a.USE_AT = 'Y'				
			ORDER BY b.FILE_SN	
 		
 	</select>
 	
	<insert id="insertFileMaster" parameterType="FileVO">
		
			INSERT INTO COMTNFILE
			(ATCH_FILE_ID, CREAT_DT, USE_AT)
			VALUES
			( #{atchFileId}, SYSDATE(), 'Y')			
		
	</insert>
	
	<insert id="insertFileDetail" parameterType="FileVO">
		
			INSERT INTO COMTNFILEDETAIL
			( ATCH_FILE_ID, FILE_SN, FILE_STRE_COURS, STRE_FILE_NM, 
			  ORIGNL_FILE_NM, FILE_EXTSN, FILE_SIZE, FILE_CN )
			VALUES
			( #{atchFileId}, #{fileSn}, #{fileStreCours}, #{streFileNm}, 
			  #{orignlFileNm}, #{fileExtsn}, #{fileMg}, #{fileCn} )			
		
	</insert>	
	
	<delete id="deleteFileDetail" parameterType="FileVO">
		
			DELETE FROM COMTNFILEDETAIL
			WHERE
				ATCH_FILE_ID = #{atchFileId}
			AND	
				FILE_SN = #{fileSn}			
			
	</delete>
 	
	<select id="getMaxFileSN" parameterType="FileVO" resultType="java.lang.Integer">
		
			SELECT IFNULL(MAX(FILE_SN),0)+1 AS FILE_SN
			FROM COMTNFILEDETAIL
			WHERE ATCH_FILE_ID =  #{atchFileId}		
		
	</select>

 	<select id="selectFileInf" parameterType="FileVO" resultMap="fileDetail">
 		
			SELECT 
				ATCH_FILE_ID, FILE_CN, FILE_SN, FILE_STRE_COURS, STRE_FILE_NM,
				FILE_EXTSN, ORIGNL_FILE_NM, FILE_SIZE
			FROM
				COMTNFILEDETAIL
			WHERE
				ATCH_FILE_ID = #{atchFileId}
			AND 
				FILE_SN = #{fileSn}	
 		
 	</select>

	<update id="deleteCOMTNFILE" parameterType="FileVO">
		
			UPDATE COMTNFILE
			SET USE_AT = 'N'
			WHERE ATCH_FILE_ID = #{atchFileId}
		
	</update>

 	<select id="selectFileListByFileNm" parameterType="FileVO" resultMap="fileList">
 		
			SELECT 
				a.ATCH_FILE_ID, b.FILE_CN, b.FILE_SN, b.FILE_STRE_COURS, b.STRE_FILE_NM,
				b.FILE_EXTSN, b.ORIGNL_FILE_NM, b.FILE_SIZE, a.CREAT_DT
			FROM
				COMTNFILE a, COMTNFILEDETAIL b
			WHERE
				a.ATCH_FILE_ID = b.ATCH_FILE_ID
			AND 
				a.USE_AT = 'Y'
 		
			<if test="searchCondition == 'streFileNm'">AND
					b.STRE_FILE_NM LIKE CONCAT ('%', #{searchKeyword},'%') 		
			</if>
			<if test="searchCondition == 'orignlFileNm'">AND
					b.ORIGNL_FILE_NM LIKE CONCAT ('%', #{searchKeyword},'%') 		
			</if>
					
			ORDER BY a.ATCH_FILE_ID, b.FILE_SN	
			LIMIT #{recordCountPerPage} OFFSET #{firstIndex}
				 		
 	</select>

 	<select id="selectFileListCntByFileNm" parameterType="FileVO" resultType="java.lang.Integer">
 		
			SELECT 
				COUNT(a.ATCH_FILE_ID)
			FROM
				COMTNFILE a, COMTNFILEDETAIL b
			WHERE
				a.ATCH_FILE_ID = b.ATCH_FILE_ID
			AND 
				a.USE_AT = 'Y'				
 		
			<if test="searchCondition == 'streFileNm'">AND
					b.STRE_FILE_NM LIKE CONCAT ('%', #{searchKeyword},'%') 		
			</if>
			<if test="searchCondition == 'orignlFileNm'">AND
					b.ORIGNL_FILE_NM LIKE CONCAT ('%', #{searchKeyword},'%') 		
			</if>
 	</select>
 	
  	<select id="selectImageFileList" parameterType="FileVO" resultMap="fileList">
 		
			SELECT 
				a.ATCH_FILE_ID, b.FILE_CN, b.FILE_SN, b.FILE_STRE_COURS, b.STRE_FILE_NM,
				b.FILE_EXTSN, b.ORIGNL_FILE_NM, b.FILE_SIZE, a.CREAT_DT
			FROM
				COMTNFILE a, COMTNFILEDETAIL b
			WHERE
				a.ATCH_FILE_ID = #{atchFileId}
			AND 
				a.ATCH_FILE_ID = b.ATCH_FILE_ID
			AND
				UPPER(b.FILE_EXTSN) IN ('GIF','JPG','BMP','PNG')
			AND 
				a.USE_AT = 'Y'				
			ORDER BY b.FILE_SN	
 		
 	</select>	
 	
</mapper>