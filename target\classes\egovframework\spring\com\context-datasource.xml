<?xml version="1.0" encoding="UTF-8"?>
<beans 
	xmlns="http://www.springframework.org/schema/beans" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:util="http://www.springframework.org/schema/util" 
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd 
	 						http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-4.0.xsd">

	<bean id="egov.propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:/egovframework/egovProps/globals.properties</value>
                <!-- <value>file:/product/jeus/egovProps/globals.properties</value> -->
            </list>
        </property>
    </bean>
	
	<!-- DataSource -->
	<alias name="dataSource" alias="egov.dataSource" />
	
	<!-- MySQL -->
	<beans profile="mysql">  
	<bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.mysql.DriverClassName}"/>
		<property name="url" value="${Globals.mysql.Url}" />
		<property name="username" value="${Globals.mysql.UserName}"/>
		<!-- 암호화(Crypto) 관련 서비스 https://www.egovframe.go.kr/wiki/doku.php?id=egovframework:rte2:fdl:crypto_simplify_v3_8 참조 -->
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
	</bean>
	</beans>
		
	<!-- oracle -->
	<beans profile="oracle">
	<!-- <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.oracle.DriverClassName}"/>
		<property name="url" value="${Globals.oracle.Url}" />
		<property name="username" value="${Globals.oracle.UserName}"/>
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
	</bean>
	</beans> -->
		
		<bean id="masterDataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
			<property name="driverClassName" value="${Globals.oracle.DriverClassName}"/>
			<property name="url" value="${Globals.oracle.Url}" />
			<property name="username" value="${Globals.oracle.UserName}"/>
			<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
		</bean>

		<!-- 동적 라우팅용 데이터소스 -->
		<bean id="dynamicRoutingDataSource" class="egovframework.com.pjt.db.DynamicRoutingDataSource">
			<property name="defaultTargetDataSource" ref="masterDataSource"/>
			<property name="targetDataSources">
				<map key-type="java.lang.String">
					<entry key="master" value-ref="masterDataSource"/>
				</map>
			</property>
		</bean>

		<!-- LazyConnectionDataSourceProxy 를 통해 Spring 트랜잭션과 호환 -->
		<bean id="dataSource" class="org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy">
			<constructor-arg ref="dynamicRoutingDataSource"/>
		</bean>
    </beans>

	<!-- altibase -->
	<beans profile="altibase">
	<bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.altibase.DriverClassName}"/>
		<property name="url" value="${Globals.altibase.Url}" />
		<property name="username" value="${Globals.altibase.UserName}"/>
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
		
		<property name="validationQuery" value="select 1"/>
        <property name="testWhileIdle" value="true"/>
	</bean>
	</beans>

	<!-- tibero -->
	<beans profile="tibero">
	<bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.tibero.DriverClassName}"/>
		<property name="url" value="${Globals.tibero.Url}" />
		<property name="username" value="${Globals.tibero.UserName}"/>
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
	</bean>
	</beans>

    <!-- cubrid -->
    <beans profile="cubrid">
    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${Globals.cubrid.DriverClassName}"/>
        <property name="url" value="${Globals.cubrid.Url}" />
        <property name="username" value="${Globals.cubrid.UserName}"/>
        <property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
        
        <property name="validationQuery" value="select 1"/>
        <property name="testOnBorrow" value="false"/>
    </bean>
    </beans>
    <!--
    	CUBRID 가이드 참조
    	https://www.cubrid.com/tutorial/3794188
    	* DBCP를 통해 최초 connection 시 해당 connection이 유효한지 체크하는 isValid를 호출
     -->

	<!-- MariaDB -->
	<beans profile="maria">
	<bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.maria.DriverClassName}"/>
		<property name="url" value="${Globals.maria.Url}" />
		<property name="username" value="${Globals.maria.UserName}"/>
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
	</bean>
	</beans>

	<!-- PostresSQL -->
	<beans profile="postgres">  
	<bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.postgres.DriverClassName}"/>
		<property name="url" value="${Globals.postgres.Url}" />
		<property name="username" value="${Globals.postgres.UserName}"/>
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
	</bean>
	</beans>
	
	<!-- GOLDILOCKS -->
	<beans profile="goldilocks">  
	<bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${Globals.goldilocks.DriverClassName}"/>
		<property name="url" value="${Globals.goldilocks.Url}" />
		<property name="username" value="${Globals.goldilocks.UserName}"/>
		<property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
	</bean>
	</beans>

    <!-- DB Pool이 생성이 되더라고 특정 시간 호출되지 않으면 DBMS 설정에 따라 연결을 끊어질 때
		이 경우 DBCP를 사용하셨다면.. 다음과 같은 설정을 추가하시면 연결을 유지시켜 줍니다. -->
	<!--
	<property name="validationQuery" value="select 1 from dual" />
	<property name="testWhileIdle" value="true" />
	<property name="timeBetweenEvictionRunsMillis" value="60000" /> -->  <!-- 1분 -->

	<!-- DBCP가 아닌 WAS의 DataSource를 사용하시는 경우도 WAS별로 동일한 설정을 하실 수 있습니다.
		(WAS별 구체적인 설정은 WAS document 확인) -->

</beans>
