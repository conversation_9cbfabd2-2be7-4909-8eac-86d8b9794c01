#RSS Tag Management#
ussIonRss.rssTagManageList.rssTagManageList=RSS Tag Management List
ussIonRss.rssTagManageList.trgetSvcNm=Target Service Name
ussIonRss.rssTagManageList.trgetSvcTable=Target Service Table
ussIonRss.rssTagManageList.hderTitle=Header TITLE
ussIonRss.rssTagManageList.hderLink=Header LINK
ussIonRss.rssTagManageList.hderDescription=Header DESCRIPTION
ussIonRss.rssTagManageList.hderTag=Header TAG
ussIonRss.rssTagManageList.hderEtc=Header ETC
ussIonRss.rssTagManageList.bdtLink=Body LINK
ussIonRss.rssTagManageList.bdtDescription=Body DESCRIPTION
ussIonRss.rssTagManageList.bdtTag=Body TAG
ussIonRss.rssTagManageList.bdtEtc=Body ETC
ussIonRss.rssTagManageList.frstRegisterNm=Registrant
ussIonRss.rssTagManageList.frstRegisterPnttm=Registration date
ussIonRss.rssTagManageList.validate.searchCondition=Please select a search condition!
ussIonRss.rssTagManageList.validate.searchKeyword=Please enter your search term!
ussIonRss.rssTagManageList.validate.deleteAlert=Are you sure you want to delete the selected information?
ussIonRss.rssTagManageList.validate.selectDelete=Please select a list to delete!

ussIonRss.rssTagManageDetail.rssTagManageDetail=RSS Tag Management Detail
ussIonRss.rssTagManageDetail.trgetSvcNm=Target Service Name
ussIonRss.rssTagManageDetail.trgetSvcTable=Target Service Table
ussIonRss.rssTagManageDetail.trgetSvcListCo=Number of target service list
ussIonRss.rssTagManageDetail.hderTitle=Header TITLE
ussIonRss.rssTagManageDetail.hderLink=Header LINK
ussIonRss.rssTagManageDetail.hderDescription=Header DESCRIPTION
ussIonRss.rssTagManageDetail.hderTag=Header TAG
ussIonRss.rssTagManageDetail.hderEtc=Header ETC
ussIonRss.rssTagManageDetail.bdtTitle=Body TITLE
ussIonRss.rssTagManageDetail.bdtLink=Body LINK
ussIonRss.rssTagManageDetail.bdtDescription=Body DESCRIPTION
ussIonRss.rssTagManageDetail.bdtTag=Body TAG
ussIonRss.rssTagManageDetail.bdtEtc=Body ETC
ussIonRss.rssTagManageDetail.validate.deleteAlert=Are you sure you want to delete?

ussIonRss.rssTagManageRegist.rssTagManageRegist=RSS Tag Management Regist
ussIonRss.rssTagManageRegist.trgetSvcNm=Target Service Name
ussIonRss.rssTagManageRegist.trgetSvcTable=Target Service Table
ussIonRss.rssTagManageRegist.trgetSvcListCo=Number of target service list
ussIonRss.rssTagManageRegist.hderTitle=Header TITLE
ussIonRss.rssTagManageRegist.hderLink=Header LINK
ussIonRss.rssTagManageRegist.hderDescription=Header DESCRIPTION
ussIonRss.rssTagManageRegist.hderTag=Header TAG
ussIonRss.rssTagManageRegist.hderEtc=Header ETC
ussIonRss.rssTagManageRegist.tableColumn=Column
ussIonRss.rssTagManageRegist.bdtTitle=Body TITLE
ussIonRss.rssTagManageRegist.bdtLink=Body LINK
ussIonRss.rssTagManageRegist.bdtDescription=Body DESCRIPTION
ussIonRss.rssTagManageRegist.bdtTag=Body TAG
ussIonRss.rssTagManageRegist.bdtEtc=Body ETC
ussIonRss.rssTagManageRegist.validate.Duplicate=Column name duplicated!

ussIonRss.rssTagManageUpdt.rssTagManageUpdt=RSS Tag Management Update
ussIonRss.rssTagManageUpdt.trgetSvcNm=Target Service Name
ussIonRss.rssTagManageUpdt.trgetSvcTable=Target Service Table
ussIonRss.rssTagManageUpdt.trgetSvcListCo=Number of target service list
ussIonRss.rssTagManageUpdt.hderTitle=Header TITLE
ussIonRss.rssTagManageUpdt.hderLink=Header LINK
ussIonRss.rssTagManageUpdt.hderDescription=Header DESCRIPTION
ussIonRss.rssTagManageUpdt.hderTag=Header TAG
ussIonRss.rssTagManageUpdt.hderEtc=Header ETC
ussIonRss.rssTagManageUpdt.tableColumn=Column
ussIonRss.rssTagManageUpdt.bdtTitle=Body TITLE
ussIonRss.rssTagManageUpdt.bdtLink=Body LINK
ussIonRss.rssTagManageUpdt.bdtDescription=Body DESCRIPTION
ussIonRss.rssTagManageUpdt.bdtTag=Body TAG
ussIonRss.rssTagManageUpdt.bdtEtc=Body ETC
ussIonRss.rssTagManageUpdt.validate.Duplicate=Column name duplicated!