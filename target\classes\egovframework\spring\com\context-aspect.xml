<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
						http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.0.xsd">

	<aop:config>
		<aop:pointcut id="egov.serviceMethod" expression="execution(* egovframework.com..impl.*Impl.*(..))" />

		<aop:aspect ref="egov.exceptionTransfer">
			<aop:after-throwing throwing="exception" pointcut-ref="egov.serviceMethod" method="transfer" />
		</aop:aspect>
	</aop:config>
	
	<bean id="egov.exceptionTransfer" class="org.egovframe.rte.fdl.cmmn.aspect.ExceptionTransfer">
		<property name="exceptionHandlerService">
			<list>
				<ref bean="defaultExceptionHandleManager" />
				<ref bean="otherExceptionHandleManager" />
			</list>
		</property>
	</bean>

	<bean id="defaultExceptionHandleManager" class="org.egovframe.rte.fdl.cmmn.exception.manager.DefaultExceptionHandleManager">
		<property name="reqExpMatcher">
			<ref bean="egov.antPathMater"/>
		</property>
		<property name="patterns">
			<list>
				<value>**service.impl.*</value>
			</list>
		</property>
		<property name="handlers">
			<list>
				<ref bean="egovHandler" />
			</list>
		</property>
	</bean>

	<bean id="otherExceptionHandleManager" class="org.egovframe.rte.fdl.cmmn.exception.manager.DefaultExceptionHandleManager">
		<property name="reqExpMatcher">
			<ref bean="egov.antPathMater"/>
		</property>
		<property name="patterns">
			<list>
				<value>**service.impl.*</value>
			</list>
		</property>
		<property name="handlers">
			<list>
				<ref bean="otherHandler" />
			</list>
		</property>
	</bean>
	
	<bean id="egovHandler" class="egovframework.com.cmm.EgovComExcepHndlr" />
	<bean id="otherHandler" class="egovframework.com.cmm.EgovComOthersExcepHndlr" />	
	
</beans>