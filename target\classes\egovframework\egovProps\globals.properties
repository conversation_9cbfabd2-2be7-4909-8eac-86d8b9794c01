#-----------------------------------------------------------------------
#
#   globals.properties : \uc2dc\uc2a4\ud15c
#
#-----------------------------------------------------------------------
#   1.  key = value \uad6c\uc870\uc785\ub2c8\ub2e4.
#   2.  key\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \ud3ec\ud568\ubd88\uac00, value\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \uac00\ub2a5
#   3.  key\uac12\uc73c\ub85c \ud55c\uae00\uc744 \uc0ac\uc6a9\ubd88\uac00,   value\uac12\uc740 \ud55c\uae00\uc0ac\uc6a9\uc774 \uac00\ub2a5
#   4.  \uc904\uc744 \ubc14\uafc0 \ud544\uc694\uac00 \uc788\uc73c\uba74 '\'\ub97c \ub77c\uc778\uc758 \ub05d\uc5d0 \ucd94\uac00(\ub9cc\uc57d  '\'\ubb38\uc790\ub97c \uc0ac\uc6a9\ud574\uc57c \ud558\ub294 \uacbd\uc6b0\ub294 '\\'\ub97c \uc0ac\uc6a9)
#   5.  Windows\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '\\' or '/'  ('\' \uc0ac\uc6a9\ud558\uba74 \uc548\ub428)
#   6.  Unix\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '/'
#   7.  \uc8fc\uc11d\ubb38 \ucc98\ub9ac\ub294  #\uc0ac\uc6a9
#   8.  value\uac12 \ub4a4\uc5d0 \uc2a4\ud398\uc774\uc2a4\uac00 \uc874\uc7ac\ud558\ub294 \uacbd\uc6b0 \uc11c\ube14\ub9bf\uc5d0\uc11c \ucc38\uc870\ud560\ub54c\ub294 \uc5d0\ub7ec\ubc1c\uc0dd\ud560 \uc218 \uc788\uc73c\ubbc0\ub85c trim()\ud558\uac70\ub098 \ub9c8\uc9c0\ub9c9 \uacf5\ubc31\uc5c6\uc774 properties \uac12\uc744 \uc124\uc815\ud560\uac83
#-----------------------------------------------------------------------

# \uc6b4\uc601\uc11c\ubc84 \ud0c0\uc785(WINDOWS, UNIX)
Globals.OsType = WINDOWS

# DB\uc11c\ubc84 \ud0c0\uc785(mysql, oracle, altibase, tibero, cubrid, maria, postgres, goldilocks) - datasource \ubc0f sqlMap \ud30c\uc77c \uc9c0\uc815\uc5d0 \uc0ac\uc6a9\ub428
Globals.DbType = oracle

# \uad8c\ud55c \uc778\uc99d\ubc29\uc2dd(dummy, session, security) - \uc0ac\uc6a9\uc790\uc758 \ub85c\uadf8\uc778\uc2dc \uc778\uc99d \ubc29\uc2dd\uc744 \uacb0\uc815\ud568
# dummy : \ub354\ubbf8 \ubc29\uc2dd\uc73c\ub85c \uc0ac\uc6a9\uc790 \uad8c\ud55c\uc744 \uc778\uc99d\ud568
# session : \uc138\uc158 \ubc29\uc2dd\uc73c\ub85c \uc0ac\uc6a9\uc790 \uad8c\ud55c\uc744 \uc778\uc99d\ud568
# security : spring security \ubc29\uc2dd\uc73c\ub85c \uc0ac\uc6a9\uc790 \uad8c\ud55c\uc744 \uc778\uc99d\ud568
Globals.Auth = dummy

# MainPage Setting
Globals.MainPage  = /pjt/projectList.do

# \uc704\uc800\ub4dc \uc0ac\uc6a9\uc2dc \ub370\uc774\ud130\ubca0\uc774\uc2a4 \uad00\ub828 \uc124\uc815\uc744 \ubd88\ub7ec\uc634

# KISA \uac80\uc99d (2019\ub144 11\uc6d4) - \uc911\uc694\uc815\ubcf4 \uc554\ud638\ud654
# Globals.mysql.Password \ub294 com01 \uc744 \uc554\ud638\ud654\ud55c \uac83\uc73c\ub85c https://www.egovframe.go.kr/wiki/doku.php?id=egovframework:rte2:fdl:crypto_simplify_v3_8 \ucc38\uc870\ud560 \uac83
# Globals.mysql.Password = com01 \ucc98\ub7fc \ud3c9\ubb38\uc744 \uc0ac\uc6a9\ud558\ub824\uba74 context-crypto.xml \uc5d0\uc11c initial="false" crypto="false" \ub85c \uc124\uc815\ud558\uace0, 
# context-datasource.xml \uc5d0\uc11c <property name="password" value="${Globals.mysql.Password}"/> \ub85c \ubc14\uafb8\uc5b4 \uc8fc\uc5b4\uc57c \ud568
#mysql
Globals.mysql.DriverClassName=net.sf.log4jdbc.DriverSpy
Globals.mysql.Url=****************************************
Globals.mysql.UserName = com
Globals.mysql.Password = xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#oracle
Globals.oracle.DriverClassName=oracle.jdbc.driver.OracleDriver
Globals.oracle.Url=****************************************
Globals.oracle.UserName = com
Globals.oracle.Password = xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#altibase
Globals.altibase.DriverClassName=Altibase.jdbc.driver.AltibaseDriver
Globals.altibase.Url=***************************************************
Globals.altibase.UserName = com
Globals.altibase.Password = xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#tibero
Globals.tibero.DriverClassName=com.tmax.tibero.jdbc.TbDriver
Globals.tibero.Url=***************************************
Globals.tibero.UserName = com
Globals.tibero.Password = xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#cubrid
Globals.cubrid.DriverClassName=cubrid.jdbc.driver.CUBRIDDriver
Globals.cubrid.Url=************************************************
Globals.cubrid.UserName = com
Globals.cubrid.Password = xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#MariaDB
Globals.maria.DriverClassName=org.mariadb.jdbc.Driver
Globals.maria.Url=*********************************
Globals.maria.UserName = com
Globals.maria.Password = xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#postgreSQL
Globals.postgres.DriverClassName=org.postgresql.Driver
Globals.postgres.Url=************************************
Globals.postgres.UserName=com
Globals.postgres.Password=xz4fmrSdr1vGGl6UtwPLwA%3D%3D

#GOLDILOCKS
Globals.goldilocks.DriverClassName=sunje.goldilocks.jdbc.GoldilocksDriver
Globals.goldilocks.Url=********************************************
Globals.goldilocks.UserName=com
Globals.goldilocks.Password= xz4fmrSdr1vGGl6UtwPLwA%3D%3D

# \ub85c\uadf8\uc778 \uc778\uc99d \uc81c\ud55c(login authentication limit)
#Globals.LoginPolicy=SECURITY
# (\uc0ac\uc6a9 \uc5ec\ubd80 \uc124\uc815\uac12 : true, false)
Globals.login.Lock = true
# -\uc778\uc99d \uc81c\uc2dc\ub3c4 \ud69f\uc218
Globals.login.LockCount = 5

#\ud1b5\ud569\uba54\uc778\uba54\ub274
#Globals.MainPage = /sym/mnu/mpm/EgovMainMenuHome.do

# G4C \uc5f0\uacb0\uc6a9 IP (localhost)
#Globals.LocalIp = *********

# \ud658\uacbd\ud30c\uc77c \uc704\uce58
Globals.ShellFilePath     = globals.properties
Globals.ServerConfPath    = conf/server.properties
Globals.ClientConfPath    = conf/client.properties
Globals.FileFormatPath    = conf/format.properties
Globals.GPKIConfPath      = conf/gpki.properties
Globals.ConfPath          = conf
Globals.MailRequestPath   = mail/request/
Globals.MailRResponsePath = mail/response/
Globals.SMEConfigPath     = conf/SMEConfig.properties
Globals.SmsDbConfPath     = conf/smsdb.properties

# \ud658\uacbd\ud30c\uc77c \uc704\uce58 (jeus)
#Globals.ShellFilePath     = globals.properties
#Globals.ServerConfPath    = conf/server.properties
#Globals.ClientConfPath    = conf/client.properties
#Globals.FileFormatPath    = conf/format.properties
#Globals.GPKIConfPath      = conf/gpki.properties
#Globals.ConfPath          = conf
#Globals.fileStorePath     =
#Globals.MailRequestPath   = mail/request/
#Globals.MailRResponsePath = mail/response/
#Globals.SMEConfigPath     = conf/SMEConfig.properties
#Globals.SynchrnServerPath = tmp/upload/

# WINDOWS\uc6a9 \uc258\ud30c\uc77c \uc815\ubcf4
SHELL.WINDOWS.getHostName      = prg/sh_001.bat
SHELL.WINDOWS.getDrctryByOwner = prg/getDrctryByOwner.bat
SHELL.WINDOWS.getDrctryOwner   = prg/getDrctryOwner.bat
SHELL.WINDOWS.moveDrctry       = prg/moveDrctry.bat
SHELL.WINDOWS.compileSchema    = prg/compileSchema.bat
SHELL.WINDOWS.getDiskAttrb     = prg/getDiskAttrb.bat
SHELL.WINDOWS.getDiskCpcty     = prg/getDiskCpcty.bat
SHELL.WINDOWS.getDiskExst      = prg/getDiskExst.bat

# UNIX\uc6a9 \uc258\ud30c\uc77c \uc815\ubcf4 - \uac1c\ubc1cwas jeus\uc5d0\uc11c \uc0ac\uc6a9\ud560\ub54c \uae30\uc900
SHELL.UNIX.getHostName      = prg/sh_001.sh
SHELL.UNIX.getDrctryByOwner = prg/getDrctryByOwner.sh
SHELL.UNIX.getDrctryOwner   = prg/getDrctryOwner.sh
SHELL.UNIX.getDrctryAccess  = prg/getDrctryAccess.sh
SHELL.UNIX.getMountLc       = prg/getMountLc.sh
SHELL.UNIX.getDiskName      = prg/getNetWorkInfo.sh
SHELL.UNIX.moveDrctry       = prg/moveDrctry.sh
SHELL.UNIX.compileSchema    = prg/compileSchema.sh
SHELL.UNIX.getDiskAttrb     = prg/getDiskAttrb.sh
SHELL.UNIX.getDiskCpcty     = prg/getDiskCpcty.sh
SHELL.UNIX.getDiskExst      = prg/getDiskExst.sh
SHELL.UNIX.getProcInfo      = prg/getProcInfo.sh

# LINUX\uc6a9 \uc258\ud30c\uc77c \uc815\ubcf4

# \uc5f0\uacc4
# \uae30\uad00\ucf54\ub4dc\uc218\uc2e0\uc6a9(\uacbd\ub85c \uc124\uc815\uc740 \ubc18\ub4dc\uc2dc \uc808\ub300\uacbd\ub85c\ub97c \uc0ac\uc6a9\ud574\uc57c\ud568.)
CNTC.INSTTCODE.DIR.rcv       = D:/tmp/test/rcv/
CNTC.INSTTCODE.DIR.rcvold    = D:/tmp/test/rcvold/
CNTC.INSTTCODE.DIR.bin       = D:/tmp/test/bin/

CNTC.INSTTCODE.CMD.edircv    = gcc_edircv.bat
CNTC.INSTTCODE.CMD.edircvmsg = gcc_edircvmsg.bat

CNTC.INSTTCODE.INFO.userid   = USERID
CNTC.INSTTCODE.INFO.userpw   = ******

# \uae30\uad00\ucf54\ub4dc, \ubc95\uc815\ub3d9\ucf54\ub4dc \uc218\uc2e0\uc6a9 \uacf5\uacf5\ub370\uc774\ud130\ud3ec\ud138 \uc778\uc99d\ud0a4(\uacf5\uacf5\ub370\uc774\ud130\ud3ec\ud138\uc5d0\uc11c \ubc1c\uae09 \ubc1b\uc740 \ud6c4 \uae30\uc785)
Globals.data.serviceKey = 

# \ud30c\uc77c \uc5c5\ub85c\ub4dc \uacbd\ub85c(\uacbd\ub85c \uc124\uc815\uc740 \ubc18\ub4dc\uc2dc \uc808\ub300\uacbd\ub85c\ub97c \uc0ac\uc6a9\ud574\uc57c\ud568, \uacbd\ub85c \ub4a4\uc5d0 /\ub97c \ubd99\uc5ec \uc8fc\uc5b4\uc57c\ud568.)
Globals.fileStorePath =  C:/egovframework/upload/
# \uc5c5\ub85c\ub4dc URL \uacbd\ub85c \uc554\ud638\ud654 KEY
#Globals.filePath.Encryption.key = 10\uc790\ub9ac\uc774\uc0c1,\uc54c\ud30c\ubcb3 \ubc0f \uc22b\uc790 \ubc0f \ud2b9\uc218\uae30\ud638 \ud63c\uc6a9\ud558\uc5ec \ud0a4\ub97c \uc9c0\uc815\ud55c\ub2e4. 
# \ud30c\uc77c \ud655\uc7a5\uc790 \ud654\uc774\ud2b8\ub9ac\uc2a4\ud2b8(\ud5c8\uc6a9\ubaa9\ub85d) : \ud30c\uc77c \ud655\uc7a5\uc790\ub97c (.)\uacfc \ud568\uaed8 \uc5f0\uc774\uc5b4\uc11c \uc0ac\uc6a9\ud558\uba70 (.)\ub85c \uc2dc\uc791\ud55c\ub2e4.
Globals.fileUpload.Extensions.Images = .gif.jpg.jpeg.png
Globals.fileUpload.Extensions = .gif.jpg.jpeg.png.xls.xlsx
Globals.fileUpload.maxSize = 1048576
Globals.fileDownload.Extensions = .gif.jpg.jpeg.png

# \ud30c\uc77c \ub3d9\uae30\ud654 \ucef4\ud3ec\ub10c\ud2b8\uc5d0\uc11c \uc0ac\uc6a9\ud560 \ud30c\uc77c \uc5c5\ub85c\ub4dc \uacbd\ub85c(\uacbd\ub85c \uc124\uc815\uc740 \ubc18\ub4dc\uc2dc \uc808\ub300\uacbd\ub85c\ub97c \uc0ac\uc6a9\ud574\uc57c\ud568, \uacbd\ub85c \ub4a4\uc5d0 /\ub97c \ubd99\uc5ec \uc8fc\uc5b4\uc57c\ud568.)
Globals.SynchrnServerPath = C:/egovframework/upload/Synch/

# Social(Facebook, Oauth \ucef4\ud3ec\ub10c\ud2b8 \uc0ac\uc6a9\uc2dc \ud65c\uc6a9, \ud398\uc774\uc2a4\ubd81,\ud2b8\uc704\ud130\uc5d0\uc11c \ubc1c\uae09 \ubc1b\uc740 \ud6c4 \uae30\uc785))
facebook.appId         = 
facebook.appSecret     = 
twitter.consumerKey    = 
twitter.consumerSecret = 

#LDAP\uc870\uc9c1\ub3c4\uad00\ub9ac \ucef4\ud3ec\ub10c\ud2b8 \uc0ac\uc6a9\uc2dc LDAP\uc11c\ubc84\uc124\uc815\uc815\ubcf4 \ud544\uc694
ldap.url = ldap://localhost:10389
ldap.rootDn = c=kr
ldap.username = uid=admin,ou=system
ldap.password = ******

#\uc57d\ub3c4\uad00\ub9ac Kakao \uac1c\ubc1c\uc790 appkey \ubc1c\uae09 \ubc1b\uc544 \ub4f1\ub85d\ud574\uc11c \uae30\uc785(\uce74\uce74\uc624\uac1c\ubc1c Dev \uc571\uad00\ub9ac \ub0b4\uc5d0\uc11c \ub3c4\uba54\uc778 \ub4f1\ub85d \ud544\uc694)
roughMap.appkey = 

# \uc694\uc18c\uae30\uc220 - \ud658\uc728\uacc4\uc0b0 : \ud55c\uad6d\uc218\ucd9c\uc785\uc740\ud589 \ud604\uc7ac\ud658\uc728 API authKey(https://www.koreaexim.go.kr/ir/HPHKIR020M01?apino=2&viewtype=C&searchselect=&searchword= \ubc1c\uae09 \ubc1b\uc740 \ud6c4 \uae30\uc785)
ehgtCalc.authKey = 

# \ube44\ubc00\ubc88\ud638 \uc720\ud6a8\uae30\uac04 \uc124\uc815
Globals.ExpirePwdDay = 180

# \ubc30\uce58\uc258\ud30c\uc77c WhiteList \uc124\uc815(","\uae30\ud638\ub85c \ub2e4\uc911\uc9c0\uc815 \uac00\ub2a5) - \uad00\ub828\uba54\ub274 : 1120.\ubc30\uce58\uc791\uc5c5\uad00\ub9ac / 1130.\ubc30\uce58\uacb0\uacfc\uad00\ub9ac / 1140.\uc2a4\ucf00\uc904\ucc98\ub9ac
SHELL.WINDOWS.batchShellFiles	= 
SHELL.UNIX.batchShellFiles		= 

#\ubc30\uce58\uc258\ud30c\uc77c \uc811\uadfc \uac00\ub2a5 \ud3f4\ub354
SHELL.batchShellFolder	= 