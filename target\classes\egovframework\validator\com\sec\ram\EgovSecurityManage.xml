<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE form-validation PUBLIC 
    "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.1//EN" 
    "http://jakarta.apache.org/commons/dtds/validator_1_1.dtd">

<form-validation>

    <formset>
        <form name="authorManage">
            <field property="authorCode" depends="required, maxlength">
                <arg0 key="권한 코드" resource="true"/>
                <arg1 key="30" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>30</var-value>
				</var>
            </field>
            <field property="authorNm" depends="required, maxlength">
                <arg0 key="권한 명" resource="true"/>
                <arg1 key="60" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>60</var-value>
				</var>
            </field>
            <field property="authorDc" depends="maxlength">
                <arg0 key="설명" resource="true"/>
                <arg1 key="200" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>200</var-value>
				</var>
            </field>
        </form>
    </formset>

    <formset>
        <form name="groupManage">
            <field property="groupNm" depends="required, maxlength">
                <arg0 key="그룹 명" resource="true"/>
                <arg1 key="60" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>60</var-value>
				</var>
            </field>
            <field property="groupDc" depends="maxlength">
                <arg0 key="설명" resource="true"/>
                <arg1 key="100" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>100</var-value>
				</var>
            </field>
        </form>
    </formset>

    <formset>
        <form name="roleManage">
            <field property="roleNm" depends="required, maxlength">
                <arg0 key="롤 명" resource="true"/>
                <arg1 key="60" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>60</var-value>
				</var>
            </field>
            <field property="rolePtn" depends="required, maxlength">
                <arg0 key="롤  패턴" resource="true"/>
                <arg1 key="300" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>300</var-value>
				</var>
            </field>
            <field property="roleDc" depends="maxlength">
                <arg0 key="롤  설명" resource="true"/>
                <arg1 key="200" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>200</var-value>
				</var>
            </field>
            <field property="roleSort" depends="required, integer, maxlength">
                <arg0 key="롤 Sort" resource="true"/>
                <arg1 key="10" resource="true"/>
				<var>
				<var-name>maxlength</var-name>
				<var-value>10</var-value>
				</var>
            </field>            
        </form>
    </formset>
    
</form-validation>