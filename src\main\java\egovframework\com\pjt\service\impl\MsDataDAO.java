package egovframework.com.pjt.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import org.springframework.stereotype.Repository;

import egovframework.com.cmm.service.impl.EgovComAbstractDAO;
import egovframework.com.pjt.service.MsDataVO;
import egovframework.com.pjt.web.ImageController;

@Repository("msDataDAO")
public class MsDataDAO extends EgovComAbstractDAO {

	public List<MsDataVO> selectDataList() throws Exception {
        return selectList("MsData.rollList");
    }

	public List<MsDataVO> selectFrameByRoll(String roll) throws Exception {
	    return  selectList("MsData.frameList", roll);
	}

	public MsDataVO selectFile(String roll, String frame) throws Exception {
	    Map<String, Object> paramMap = new HashMap<>();
	    paramMap.put("rollNo", roll);
	    paramMap.put("frameNo", frame);
	    return (MsDataVO) selectOne("MsData.selectFile", paramMap);
	}

	/**
	 * MS_DATA_HISTORY 테이블에서 roll_no, frame_no에 해당하는 데이터 조회
	 * @param roll 권번호
	 * @param frame 페이지번호
	 * @return 조회된 데이터 목록
	 * @throws Exception
	 */
	public List<MsDataVO> selectHistoryData(String roll, String frame) throws Exception {
	    Map<String, Object> paramMap = new HashMap<>();
	    paramMap.put("rollNo", roll);
	    paramMap.put("frameNo", frame);
	    return selectList("MsData.selectHistoryData", paramMap);
	}

	/**
     * 토지대장 기본정보 조회
     *
     * @param paramMap 조회 파라미터
     * @return 토지대장 데이터 목록
     * @throws Exception
     */

    public List<MsDataVO> selectLandBasicInfo(Map<String, Object> paramMap) throws Exception {
        return selectList("MsData.selectLandBasicInfo", paramMap);
    }

    /**
     * 토지대장 연혁 조회
     *
     * @param paramMap 조회 파라미터
     * @return 토지대장 데이터 목록
     * @throws Exception
     */

    public List<MsDataVO> selectLandHistory(Map<String, Object> paramMap) throws Exception {
        return selectList("MsData.selectLandHistory", paramMap);
    }

    /**
     * 토지대장 날짜/사고/주소 조회
     *
     * @param paramMap 조회 파라미터
     * @return 토지대장 데이터 목록
     * @throws Exception
     */

    public List<MsDataVO> selectLandDateInfo(Map<String, Object> paramMap) throws Exception {
        return selectList("MsData.selectLandDateInfo", paramMap);
    }

    /**
     * 토지대장 소유자 조회
     *
     * @param paramMap 조회 파라미터
     * @return 토지대장 데이터 목록
     * @throws Exception
     */

    public List<MsDataVO> selectLandOwnerInfo(Map<String, Object> paramMap) throws Exception {
        return selectList("MsData.selectLandOwnerInfo", paramMap);
    }

    /**
     * 토지대장 색인정보 조회
     *
     * @param paramMap 조회 파라미터
     * @return 토지대장 데이터
     * @throws Exception
     */
    public MsDataVO selectLandIndexInfo(Map<String, Object> paramMap) throws Exception {
        return (MsDataVO) selectOne("MsData.selectLandIndexInfo", paramMap);
    }

    /**
     * 지목 코드 리스트 조회
     *
     * @return 지목 코드 리스트
     * @throws Exception
     */
    public List<Map<String, Object>> selectJimokCodeList() throws Exception {
        return selectList("MsData.selectJimokCodeList");
    }

    /**
     * 연혁 코드 리스트 조회
     *
     * @return 연혁 코드 리스트
     * @throws Exception
     */
    public List<Map<String, Object>> selectHistoryCodeList() throws Exception {
        return selectList("MsData.selectHistoryCodeList");
    }

    /**
     * 소유자 코드 리스트 조회
     *
     * @return 소유자 코드 리스트
     * @throws Exception
     */
    public List<Map<String, Object>> selectReasonCodeList() throws Exception {
        return selectList("MsData.selectReasonCodeList");
    }

    /**
     * 소유자 연호 코드 리스트 조회
     *
     * @return 소유자 연호 코드 리스트
     * @throws Exception
     */
    public List<Map<String, Object>> selectYearCodeList() throws Exception {
        return selectList("MsData.selectYearCodeList");
    }

    /**
     * 소유자 주소 리스트 조회
     *
     * @return 소유자 주소 리스트
     * @throws Exception
     */
    public List<Map<String, Object>> selectOwnerAddrList() throws Exception {
        return selectList("MsData.selecAddressList");
    }

    /**
     * 소유자 이름 리스트 조회
     *
     * @return 소유자 이름 리스트
     * @throws Exception
     */
    public List<Map<String, Object>> selectOwnerNameList() throws Exception {
        return selectList("MsData.selectNameList");
    }

    /**
     * 토지대장 기본정보 삭제
     *
     * @param paramMap 삭제 파라미터 (rollNo, frameNo)
     * @return 삭제된 행 수
     * @throws Exception
     */
    public int deleteLandBasicInfo(Map<String, Object> paramMap) throws Exception {
        return delete("MsData.deleteLandBasicInfo", paramMap);
    }

    /**
     * 토지대장 기본정보 저장
     *
     * @param dataList 저장할 데이터 목록
     * @return 저장된 행 수
     * @throws Exception
     */
    public int insertLandBasicInfo(List<MsDataVO> dataList) throws Exception {
        int count = 0;
        for (MsDataVO data : dataList) {
            count += insert("MsData.insertLandBasicInfo", data);
        }
        return count;
    }

    /**
     * 토지대장 연혁 삭제
     *
     * @param paramMap 삭제 파라미터 (rollNo, frameNo)
     * @return 삭제된 행 수
     * @throws Exception
     */
    public int deleteLandHistory(Map<String, Object> paramMap) throws Exception {
        return delete("MsData.deleteLandHistory", paramMap);
    }

    /**
     * 토지대장 연혁 저장
     *
     * @param dataList 저장할 데이터 목록
     * @return 저장된 행 수
     * @throws Exception
     */
    public int insertLandHistory(List<MsDataVO> dataList) throws Exception {
        int count = 0;
        for (MsDataVO data : dataList) {
            count += insert("MsData.insertLandHistory", data);
        }
        return count;
    }

    /**
     * 토지대장 날짜/사고/주소 삭제
     *
     * @param paramMap 삭제 파라미터 (rollNo, frameNo)
     * @return 삭제된 행 수
     * @throws Exception
     */
    public int deleteLandDateInfo(Map<String, Object> paramMap) throws Exception {
        return delete("MsData.deleteLandDateInfo", paramMap);
    }

    /**
     * 토지대장 날짜/사고/주소 저장
     *
     * @param dataList 저장할 데이터 목록
     * @return 저장된 행 수
     * @throws Exception
     */
    public int insertLandDateInfo(List<MsDataVO> dataList) throws Exception {
        int count = 0;
        for (MsDataVO data : dataList) {
            count += insert("MsData.insertLandDateInfo", data);
        }
        return count;
    }

    /**
     * 토지대장 소유자 삭제
     *
     * @param paramMap 삭제 파라미터 (rollNo, frameNo)
     * @return 삭제된 행 수
     * @throws Exception
     */
    public int deleteLandOwnerInfo(Map<String, Object> paramMap) throws Exception {
        return delete("MsData.deleteLandOwnerInfo", paramMap);
    }

    /**
     * 토지대장 소유자 저장
     *
     * @param dataList 저장할 데이터 목록
     * @return 저장된 행 수
     * @throws Exception
     */
    public int insertLandOwnerInfo(List<MsDataVO> dataList) throws Exception {
        int count = 0;
        for (MsDataVO data : dataList) {
            count += insert("MsData.insertLandOwnerInfo", data);
        }
        return count;
    }

    /**
     * 토지대장 색인정보 삭제
     *
     * @param paramMap 삭제 파라미터 (rollNo, frameNo)
     * @return 삭제된 행 수
     * @throws Exception
     */
    public int deleteLandIndexInfo(Map<String, Object> paramMap) throws Exception {
        return delete("MsData.deleteLandIndexInfo", paramMap);
    }

    /**
     * 토지대장 색인정보 저장
     *
     * @param data 저장할 데이터
     * @return 저장된 행 수
     * @throws Exception
     */
    public int insertLandIndexInfo(MsDataVO data) throws Exception {
        return insert("MsData.insertLandIndexInfo", data);
    }
    
    /**
     * 작업코드 별 특정 권, 페이지의 마지막 순번값 조회
     *
     * @param 조건 파라미터(rollNo, frameNo, workCode)
     * @return 마지막 순번 (int)
     * @throws Exception
     */
    public int selectMaxSrnoByTable(String rollNo, String frameNo, String workcode) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rollNo", rollNo);
        paramMap.put("frameNo", frameNo);
        paramMap.put("workcode", workcode);

        System.out.println("DAO 전달 paramMap → " + paramMap);
        
        Integer result = (Integer) selectOne("MsData.selectMaxSrnoByTable", paramMap);
        return result != null ? result : 0;
    }
}