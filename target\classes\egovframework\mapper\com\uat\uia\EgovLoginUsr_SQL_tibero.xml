<?xml version="1.0" encoding="UTF-8"?><!-- 
   수정일                 수정자            수정내용
  =========     =======    =================================================
  2011.08.26   	서준식            SSO 로그인을 위한 쿼리 추가
  2011.09.27    서준식            컬럼 변경 SUB_DN 을  CRTFC_DN_VALUE 로 변경
  2011.09.28    서준식            컬럼 변경 entrprsmber_id 을  ENTRPRS_MBER_ID 로 변경
  2020.07.06    신용호            비밀번호 만료여부 조회
  2021.05.30    정진오            디지털원패스 로그인을 위한 쿼리 추가
-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="LoginUsr">

	<!-- 로그인 처리를 위한 resultMap -->
	<resultMap id="login" type="egovframework.com.cmm.LoginVO">
		<result property="id" column="id"/>
		<result property="name" column="name"/>
		<result property="ihidNum" column="ihidNum"/>
		<result property="email" column="email"/>
		<result property="password" column="password"/>
		<result property="userSe" column="userSe"/>
		<result property="orgnztId" column="orgnztId"/>
		<result property="uniqId" column="uniqId"/>		
	</resultMap>
	
	<!-- 아이디 찾기를 위한 resultMap -->
	<resultMap id="id" type="egovframework.com.cmm.LoginVO">
		<result property="id" column="id"/>
	</resultMap>
	
	<!-- 비밀번호 처리를 위한 resultMap -->
	<resultMap id="password" type="egovframework.com.cmm.LoginVO">
		<result property="password" column="password"/>
	</resultMap>
	
	<!-- SSO용 ESNTL_ID를 이용한 로그인(2011.8.26) -->
	
	<select id="ssoLoginByEsntlId" resultMap="login">
		<!-- 일반회원 -->
		<if test="userSe != null and userSe == 'GNR'">
			
			SELECT mber_id AS id
			     , mber_nm AS name
			     , ihidNum AS ihidNum
			     , password AS password
			     , mber_email_adres AS email
			     , 'GNR' AS userSe
			     , '-' AS orgnztId
			     , ESNTL_ID AS uniqId
			  FROM COMTNGNRLMBER
			 WHERE ESNTL_ID = #{uniqId}
			   AND mber_sttus = 'P'
			
		</if>
		<!-- 기업회원 -->
		<if test="userSe != null and userSe == 'ENT'">
			
			SELECT ENTRPRS_MBER_ID AS id
			     , cmpny_nm AS name
			     , entrprs_mber_password AS password
			     , bizrno AS ihidNum
			     , applcnt_email_adres AS email
			     , 'ENT' AS userSe
			     , '-' AS orgnztId
			     , esntl_id AS uniqId
			  FROM COMTNENTRPRSMBER
			 WHERE ESNTL_ID = #{uniqId}
			   AND entrprs_mber_sttus = 'P'
			
		</if>
		<!-- 업무사용자 -->
		<if test="userSe != null and userSe == 'USR'">
			
			SELECT emplyr_id AS id
			     , user_nm AS name
			     , password AS password
			     , ihidnum AS ihidNum
			     , email_adres AS email
			     , 'USR' AS userSe
			     , orgnzt_id AS orgnztId
			     , esntl_id AS uniqId
			  FROM COMTNEMPLYRINFO
			 WHERE ESNTL_ID = #{uniqId}
			   AND emplyr_sttus_code = 'P'
			
		</if>
	</select>
	
	<!-- 일반 로그인 -->
	<select id="actionLogin" resultMap="login">
		<!-- 일반회원 -->
		<if test="userSe != null and userSe == 'GNR'">
			
			SELECT mber_id AS id
			     , mber_nm AS name
			     , ihidNum AS ihidNum
			     , password AS password
			     , mber_email_adres AS email
			     , 'GNR' AS userSe
			     , '-' AS orgnztId
			     , esntl_id AS uniqId
			  FROM COMTNGNRLMBER
			 WHERE mber_id = #{id}
			   AND password = #{password}
			   AND mber_sttus = 'P'
			
		</if>
		<!-- 기업회원 -->
		<if test="userSe != null and userSe == 'ENT'">
			
			SELECT ENTRPRS_MBER_ID AS id
			     , cmpny_nm AS name
			     , entrprs_mber_password AS password
			     , bizrno AS ihidNum
			     , applcnt_email_adres AS email
			     , 'ENT' AS userSe
			     , '-' AS orgnztId
			     , esntl_id AS uniqId
			  FROM COMTNENTRPRSMBER
			 WHERE ENTRPRS_MBER_ID = #{id}
			   AND entrprs_mber_password = #{password}
			   AND entrprs_mber_sttus = 'P'
			
		</if>
		<!-- 업무사용자 -->
		<if test="userSe != null and userSe == 'USR'">
			
			SELECT emplyr_id AS id
			     , user_nm AS name
			     , password AS password
			     , ihidnum AS ihidNum
			     , email_adres AS email
			     , 'USR' AS userSe
			     , orgnzt_id AS orgnztId
			     , esntl_id AS uniqId
			  FROM COMTNEMPLYRINFO
			 WHERE emplyr_id = #{id}
			   AND password = #{password}
			   AND emplyr_sttus_code = 'P'
			
		</if>
	</select>
	
	<!-- 인증서 로그인 -->
	<select id="actionCrtfctLogin" resultMap="login">
		
		SELECT emplyr_id AS id
		     , user_nm AS name
		     , password AS password
		     , ihidnum AS ihidNum
		     , email_adres AS email
		     , 'USR' AS userSe
		     , orgnzt_id AS orgnztId
		     , esntl_id AS uniqId
		  FROM COMTNEMPLYRINFO
		 WHERE CRTFC_DN_VALUE = #{dn}
		
	</select>
	
	<!-- 아이디 찾기 -->
	<select id="searchId" resultMap="id">
		<!-- 일반회원 -->
		<if test="userSe != null and userSe == 'GNR'">
			
			SELECT mber_id AS id
			  FROM COMTNGNRLMBER
			 WHERE mber_nm = #{name}
			   AND mber_email_adres = #{email}
			   AND mber_sttus = 'P'
			
		</if>
		<!-- 기업회원 -->
		<if test="userSe != null and userSe == 'ENT'">
			
			SELECT ENTRPRS_MBER_ID AS id
			  FROM COMTNENTRPRSMBER
			 WHERE cmpny_nm = #{name}
			   AND applcnt_email_adres = #{email}
			   AND entrprs_mber_sttus = 'P'
			
		</if>
		<!-- 업무사용자 -->
		<if test="userSe != null and userSe == 'USR'">
			
			SELECT emplyr_id AS id
			  FROM COMTNEMPLYRINFO
			 WHERE user_nm = #{name}
			   AND email_adres = #{email}
			   AND emplyr_sttus_code = 'P'
			
		</if>
	</select>
	
	<!-- 비밀번호 찾기 -->
	<select id="searchPassword" resultMap="password">
		<!-- 일반회원 -->
		<if test="userSe != null and userSe == 'GNR'">
			
			SELECT password AS password
			  FROM COMTNGNRLMBER
			 WHERE mber_id = #{id}
			   AND mber_nm = #{name}
			   AND mber_email_adres = #{email}
			   AND password_hint = #{passwordHint}
			   AND password_cnsr = #{passwordCnsr}
			   AND mber_sttus = 'P'
			
		</if>
		<!-- 기업회원 -->
		<if test="userSe != null and userSe == 'ENT'">
			
			SELECT entrprs_mber_password AS password
			  FROM COMTNENTRPRSMBER
			 WHERE ENTRPRS_MBER_ID = #{id}
			   AND cmpny_nm = #{name}
			   AND applcnt_email_adres = #{email}
			   AND entrprs_mber_password_hint = #{passwordHint}
			   AND entrprs_mber_password_cnsr = #{passwordCnsr}
			   AND entrprs_mber_sttus = 'P'
			
		</if>
		<!-- 업무사용자 -->
		<if test="userSe != null and userSe == 'USR'">
			
			SELECT password AS password
			  FROM COMTNEMPLYRINFO
			 WHERE emplyr_id = #{id}
			   AND user_nm = #{name}
			   AND email_adres = #{email}
			   AND password_hint = #{passwordHint}
			   AND password_cnsr = #{passwordCnsr}
			   AND emplyr_sttus_code = 'P'
			
		</if>
	</select>
	
	<!-- 변경된 비밀번호를 저장 -->
	<update id="updatePassword">
		<!-- 일반회원 -->
		<if test="userSe != null and userSe == 'GNR'">
			
			UPDATE COMTNGNRLMBER
			   SET password = #{password}
			     , CHG_PWD_LAST_PNTTM = sysdate
			 WHERE mber_id = #{id}
			
		</if>
		<!-- 기업회원 -->
		<if test="userSe != null and userSe == 'ENT'">
			
			UPDATE COMTNENTRPRSMBER
			   SET entrprs_mber_password = #{password}
			     , CHG_PWD_LAST_PNTTM = sysdate
			 WHERE ENTRPRS_MBER_ID = #{id}
			
		</if>
		<!-- 업무사용자 -->
		<if test="userSe != null and userSe == 'USR'">
			
			UPDATE COMTNEMPLYRINFO
			   SET password = #{password}
			     , CHG_PWD_LAST_PNTTM = sysdate
			 WHERE emplyr_id = #{id}
			
		</if>
	</update>
	
	<!-- 로그인인증제한 조회 -->
	<select id="selectLoginIncorrect" resultType="egovMap">
	<!-- 일반회원 -->
	<if test="userSe != null and userSe == 'GNR'">
	<![CDATA[
	SELECT  MBER_ID AS USER_ID
         ,  PASSWORD AS USER_PW
	     ,  MBER_NM AS USER_NM
	     ,  ESNTL_ID AS UNIQ_ID
	     ,  NVL(LOCK_AT,'N') LOCK_AT
	     ,  NVL(LOCK_CNT,0) AS LOCK_CNT
      FROM  COMTNGNRLMBER
	 WHERE  MBER_ID = #{id} 
	]]>
	</if>
	<!-- 기업회원 -->
	<if test="userSe != null and userSe == 'ENT'">
	<![CDATA[
	SELECT  ENTRPRS_MBER_ID AS USER_ID
         ,  ENTRPRS_MBER_PASSWORD  AS USER_PW
	     ,  CMPNY_NM AS USER_NM
	     ,  ESNTL_ID AS UNIQ_ID
	     ,  NVL(LOCK_AT,'N') LOCK_AT
	     ,  NVL(LOCK_CNT,0) AS LOCK_CNT
      FROM  COMTNENTRPRSMBER
	 WHERE  ENTRPRS_MBER_ID = #{id} 
	]]>
	</if>
	<!-- 업무사용자 -->
	<if test="userSe != null and userSe == 'USR'">
	<![CDATA[
	SELECT  EMPLYR_ID AS USER_ID
         ,  PASSWORD AS USER_PW
	     ,  USER_NM AS USER_NM
	     ,  ESNTL_ID AS UNIQ_ID
	     ,  NVL(LOCK_AT,'N') LOCK_AT
	     ,  NVL(LOCK_CNT,0) AS LOCK_CNT
      FROM  COMTNEMPLYRINFO
	 WHERE  EMPLYR_ID = #{id} 
	]]>
	</if>
	</select>

	<!-- 로그인인증제한 변경 > 일반회원 -->
	<update id="updateLoginIncorrectGNR">
		<!-- LOCK 해제 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'E')">
			<![CDATA[
			UPDATE  COMTNGNRLMBER
			   SET  LOCK_AT = NULL
			     ,  LOCK_CNT  = NULL
			     ,  LOCK_LAST_PNTTM = NULL     
			   WHERE MBER_ID = #{id}
			]]>
		</if>
		<!-- LOCK 설정 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'L')">
			<![CDATA[
			UPDATE  COMTNGNRLMBER
			   SET  LOCK_AT = 'Y'
			     ,  LOCK_CNT  = LOCK_CNT+1
			     ,  LOCK_LAST_PNTTM = sysdate
		      WHERE MBER_ID = #{id}
			]]>
		</if>
		<!-- LOCK 카운트 설정 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'C')">
			<![CDATA[
			UPDATE  COMTNGNRLMBER
			   SET  LOCK_CNT  = NVL(LOCK_CNT,0)+1
			     ,  LOCK_LAST_PNTTM = sysdate     
			   WHERE MBER_ID = #{id}
			]]>
		</if>
	</update>
	
	<!-- 로그인인증제한 변경 > 기업사용자 -->
	<update id="updateLoginIncorrectENT">
		<!-- LOCK 해제 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'E')">
			<![CDATA[
			UPDATE  COMTNENTRPRSMBER
			   SET  LOCK_AT = NULL
			     ,  LOCK_CNT  = NULL
			     ,  LOCK_LAST_PNTTM = NULL     
			   WHERE ENTRPRS_MBER_ID = #{id}
			]]>
		</if>
		<!-- LOCK 설정 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'L')">
			<![CDATA[
			UPDATE  COMTNENTRPRSMBER
			   SET  LOCK_AT = 'Y'
			     ,  LOCK_CNT  = LOCK_CNT+1
			     ,  LOCK_LAST_PNTTM = sysdate
			   WHERE ENTRPRS_MBER_ID = #{id}
			]]>
		</if>
		<!-- LOCK 카운트 설정 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'C')">
			<![CDATA[
			UPDATE  COMTNENTRPRSMBER
			   SET  LOCK_CNT  = NVL(LOCK_CNT,0)+1
			     ,  LOCK_LAST_PNTTM = sysdate     
			   WHERE ENTRPRS_MBER_ID = #{id}
			]]>
		</if>
	</update>

	<!-- 로그인인증제한 변경 > 업무사용자 -->
	<update id="updateLoginIncorrectUSR">
		<!-- LOCK 해제 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'E')">
			<![CDATA[
			UPDATE  COMTNEMPLYRINFO
			   SET  LOCK_AT = NULL
			     ,  LOCK_CNT  = NULL
			     ,  LOCK_LAST_PNTTM = NULL     
			   WHERE EMPLYR_ID = #{id}
			]]>
		</if>
		<!-- LOCK 설정 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'L')">
			<![CDATA[
			UPDATE  COMTNEMPLYRINFO
			   SET  LOCK_AT = 'Y'
			     ,  LOCK_CNT  = LOCK_CNT+1
			     ,  LOCK_LAST_PNTTM = sysdate
			   WHERE EMPLYR_ID = #{id}
			]]>
		</if>
		<!-- LOCK 카운트 설정 -->
		<if test="@egovframework.com.cmm.util.EgovMybatisUtil@isEquals(updateAt,'C')">
			<![CDATA[
			UPDATE  COMTNEMPLYRINFO
			   SET  LOCK_CNT  = NVL(LOCK_CNT,0)+1
			     ,  LOCK_LAST_PNTTM = sysdate     
			   WHERE EMPLYR_ID = #{id}
			]]>
		</if>
	</update>
	
	<!-- 비밀번호 만료 기간 체크 -->
	<select id="selectPassedDayChangePWD" resultType="int">
		<!-- 일반회원 -->
		<if test="userSe != null and userSe == 'GNR'">
			
			SELECT NVL(TO_DATE(sysdate) - TO_DATE(CHG_PWD_LAST_PNTTM),0)
			  FROM COMTNGNRLMBER
			 WHERE mber_id = #{id}
			
		</if>
		<!-- 기업회원 -->
		<if test="userSe != null and userSe == 'ENT'">
			
			SELECT NVL(TO_DATE(sysdate) - TO_DATE(CHG_PWD_LAST_PNTTM),0)
			  FROM COMTNENTRPRSMBER
			 WHERE ENTRPRS_MBER_ID = #{id}
			
		</if>
		<!-- 업무사용자 -->
		<if test="userSe != null and userSe == 'USR'">
			
			SELECT NVL(TO_DATE(sysdate) - TO_DATE(CHG_PWD_LAST_PNTTM),0)
			  FROM COMTNEMPLYRINFO
			 WHERE emplyr_id = #{id}
			
		</if>
	</select>

	<!-- 2021.05.30, 정진오, 디지털원패스 로그인을 위한 쿼리 추가 -->
	<select id="onepassLogin" resultMap="login">
		SELECT mber_id AS id
		     , mber_nm AS name
		     , password AS password
		     , ihidNum AS ihidNum
		     , mber_email_adres AS email
		     , 'GNR' AS userSe
		     , '-' AS orgnztId
		     , esntl_id AS uniqId
		  FROM COMTNGNRLMBER
		 WHERE mber_id = #{id}
		   AND mber_sttus = 'P'
		UNION ALL
		SELECT ENTRPRS_MBER_ID AS id
		     , cmpny_nm AS name
		     , entrprs_mber_password AS password
		     , bizrno AS ihidNum
		     , applcnt_email_adres AS email
		     , 'ENT' AS userSe
		     , '-' AS orgnztId
		     , esntl_id AS uniqId
		  FROM COMTNENTRPRSMBER
		 WHERE ENTRPRS_MBER_ID = #{id}
		   AND entrprs_mber_sttus = 'P'
		UNION ALL
		SELECT emplyr_id AS id
		     , user_nm AS name
		     , password AS password
		     , ihidnum AS ihidNum
		     , email_adres AS email
		     , 'USR' AS userSe
		     , orgnzt_id AS orgnztId
		     , esntl_id AS uniqId
		  FROM COMTNEMPLYRINFO
		 WHERE emplyr_id = #{id}
		   AND emplyr_sttus_code = 'P'
	</select>

</mapper>