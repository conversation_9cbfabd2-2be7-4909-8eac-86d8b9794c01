#Sent Note Box Management Message#

comUssIonNts.title=Sent Note Box Management

comUssIonNts.list.seq=seq
comUssIonNts.list.noteSj=Note Subject
comUssIonNts.list.rcverNm=Sender
comUssIonNts.list.openAt=open/not open
comUssIonNts.list.trnsmiteDateTime=Sender Time

comUssIonNts.detail.noteSj=Note Subject
comUssIonNts.detail.trnsmitNm=Sender
comUssIonNts.detail.trnsmitPnttm=Sender Time
comUssIonNts.detail.rcverNm=Receiver
comUssIonNts.detail.noteRecptn=Note Contents

comUssIonNts.detail.trnsmitAll=all
comUssIonNts.detail.trnsmitOpen=open
comUssIonNts.detail.trnsmitNotOpen=not open

comUssIonNts.popupTrnsmitCnfirm.title=Receiver List Popup

comUssIonNts.popupTrnsmitCnfirmList.seq=seq
comUssIonNts.popupTrnsmitCnfirmList.rcverID=receiver id
comUssIonNts.popupTrnsmitCnfirmList.rcverNm=receiver
comUssIonNts.popupTrnsmitCnfirmList.openAt=open/not open
comUssIonNts.popupTrnsmitCnfirmList.gbn=type
comUssIonNts.popupTrnsmitCnfirmList.rcverDateTime=receiver time


comUssIonNts.searchCondition.rcverDate=sender date
comUssIonNts.searchCondition.NOTE_SJ=note subject
comUssIonNts.searchCondition.NOTE_CN=note contents
comUssIonNts.searchCondition.searchFromDate=send start date
comUssIonNts.searchCondition.searchToDate=send end date

comUssIonNts.validate.deleteCnfirmt=Do you confirm the deletion of the selected Inbox?
comUssIonNts.validate.noDelList=Select a list to delete!
comUssIonNts.validate.dateFromCheck=Start date of search criteria is later than end date Check the date of the search term!
