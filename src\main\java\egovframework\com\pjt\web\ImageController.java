package egovframework.com.pjt.web;

import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.logging.Logger;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import egovframework.com.pjt.service.MsDataVO;

@Controller
@RequestMapping("/image")
public class ImageController {

    private static final Logger logger = Logger.getLogger(ImageController.class.getName());

    @Autowired
    private MsDataController msDataController;

    @GetMapping("/crop")
    public void cropImage(
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String rollNo,
            @RequestParam(required = false) String frameNo,
            @RequestParam(required = false, defaultValue = "0") String workcode,
            @RequestParam(required = false, defaultValue = "0") Integer x,
            @RequestParam(required = false, defaultValue = "0") Integer y,
            @RequestParam(required = false, defaultValue = "0") Integer width,
            @RequestParam(required = false, defaultValue = "0") Integer height,
            HttpServletResponse response) throws IOException {

        String imagePath = null;
        String imageFileName = null;

        // 파일명이 직접 제공된 경우
        if (fileName != null && !fileName.isEmpty()) {
            imagePath = "R:/48경남/1.완료/2023년/창녕구대장/이미지/B0513/";
            imageFileName = fileName;
        }
        // rollNo와 frameNo가 제공된 경우 MsDataController를 통해 파일 정보 조회
        else if (rollNo != null && !rollNo.isEmpty() && frameNo != null && !frameNo.isEmpty()) {
            try {
                // MsDataController의 메소드 직접 호출
                MsDataVO fileInfo = msDataController.getFileName(rollNo, frameNo);

                if (fileInfo != null) {
                    imagePath = fileInfo.getPathName();
                    imageFileName = fileInfo.getFileName();
                    
                    // 경로명 처리
                    if (imagePath != null) {
                    	
                    }
                    
                    // 파일명도 정규화 처리
                    if (imageFileName != null) {
                        imageFileName = imageFileName.trim();
                        // 파일명에 경로 구분자가 포함되어 있는 경우 처리
                        if (imageFileName.contains("/") || imageFileName.contains("\\")) {
                            // 마지막 구분자 이후의 문자열만 추출
                            int lastSlashIndex = Math.max(imageFileName.lastIndexOf('/'), imageFileName.lastIndexOf('\\'));
                            if (lastSlashIndex >= 0 && lastSlashIndex < imageFileName.length() - 1) {
                                imageFileName = imageFileName.substring(lastSlashIndex + 1);
                            }
                        }
                    }

                    logger.info("DB에서 가져온 경로: " + imagePath + ", 파일명: " + imageFileName);
                } else {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND, "파일 정보를 찾을 수 없습니다.");
                    return;
                }
            } catch (Exception e) {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "파일 정보 조회 중 오류가 발생했습니다: " + e.getMessage());
                return;
            }
        } else {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "파일명 또는 권/페이지 정보가 필요합니다.");
            return;
        }

        // 파일 경로가 없는 경우 기본 경로 사용
        if (imagePath == null || imagePath.trim().isEmpty()) {
            imagePath = "R:/48경남/1.완료/2023년/창녕구대장/이미지/B0513/";
        }

        // 경로 구분자 정규화 (백슬래시를 슬래시로 변환)
        imagePath = imagePath.replace("\\", "/");
        // 정규표현식을 사용하여 모든 백슬래시 변환
        imagePath = imagePath.replaceAll("\\\\", "/");

        // 경로 끝에 구분자 추가 확인
        if (!imagePath.endsWith("/")) {
            imagePath += "/";
        }

        logger.info("Loading image from: " + imagePath + imageFileName);

        // Java NIO를 사용하여 경로 처리
        File file;
        try {
            // 절대 경로인지 확인
            if (imagePath.matches("^[A-Za-z]:/.*") || imagePath.startsWith("/")) {
                // 절대 경로인 경우
                Path path = Paths.get(imagePath, imageFileName);
                file = path.toFile();
            } else {
                // 상대 경로인 경우
                file = new File(imagePath + imageFileName);
            }
            logger.info("최종 파일 경로: " + file.getAbsolutePath());
        } catch (Exception e) {
            logger.severe("경로 처리 중 오류 발생: " + e.getMessage());
            // 기본 방식으로 다시 시도
            file = new File(imagePath + imageFileName);
        }
        if (!file.exists()) {
            logger.warning("File not found: " + file.getAbsolutePath());
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "파일을 찾을 수 없습니다: " + file.getAbsolutePath());
            return;
        }

        BufferedImage original;
        try {
            logger.info("Reading image file: " + file.getAbsolutePath());
            original = ImageIO.read(file); // TIFF도 지원 (TwelveMonkeys 필요)
            if (original == null) {
                logger.warning("ImageIO returned null for file: " + file.getAbsolutePath());
                response.sendError(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "지원되지 않는 이미지 형식입니다.");
                return;
            }
        } catch (Exception e) {
            logger.severe("Error reading image: " + e.getMessage());
            response.sendError(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "이미지를 읽을 수 없습니다: " + e.getMessage());
            return;
        }

        // workcode에 따라 이미지를 자를 영역 지정
        if (x == 0 && y == 0 && width == 0 && height == 0) {
            // x, y, width, height가 지정되지 않은 경우 workcode에 따라 설정
            switch (workcode) {
                case "1": // 지목
                    x = 350;
                    y = 200;
                    width = 3600;
                    height = 1200;
                    break;
                case "2": // 연혁
                    x = 350;
                    y = 800;
                    width = 3600;
                    height = 1100;
                    break;
                case "3": // 날짜
                    x = 350;
                    y = 1500;
                    width = 3600;
                    height = 1200;
                    break;
                case "4": // 이름
                    x = 350;
                    y = 2500;
                    width = 3600;
                    height = 800;
                    break;
                case "5": // 적요
                    x = 3800;
                    y = 200;
                    width = 400;
                    height = 2800;
                    break;
            }
            logger.info("Crop area set by workcode " + workcode + ": x=" + x + ", y=" + y + ", width=" + width + ", height=" + height);
        }

        // 유효 영역 체크
        x = Math.max(0, x);
        y = Math.max(0, y);
        width = Math.min(width, original.getWidth() - x);
        height = Math.min(height, original.getHeight() - y);

        BufferedImage cropped = original.getSubimage(x, y, width, height);

        response.setContentType("image/jpeg");
        OutputStream out = new BufferedOutputStream(response.getOutputStream());
        ImageIO.write(cropped, "jpg", out);
        out.close();
    }

}
