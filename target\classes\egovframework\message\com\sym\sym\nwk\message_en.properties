# EgovNtwrk.xml
comSymSymNwk.ntwrk.validate.gtwy=Gateway
comSymSymNwk.ntwrk.validate.subnet=Subnet
comSymSymNwk.ntwrk.validate.domnServer=DNS
comSymSymNwk.ntwrk.validate.userNm=User Name
comSymSymNwk.ntwrk.validate.regstYmd=Registration Date
comSymSymNwk.ntwrk.validate.ntwrkIp=IP


comSymSymNwk.ntwrkList.title=Network Management
comSymSymNwk.NtwrkList.pageTop.title=Network Management
comSymSymNwk.NtwrkList.strManageIem.all=All
comSymSymNwk.NtwrkList.strUserNm=User Name
comSymSymNwk.NtwrkList.ntwrkId=Network ID
comSymSymNwk.NtwrkList.ntwrkIp=IP
comSymSymNwk.NtwrkList.manageIem=Management Topics
comSymSymNwk.NtwrkList.userNm=User
comSymSymNwk.NtwrkList.useAt=Usage Status
comSymSymNwk.NtwrkList.regstYmd=Registration Date


comSymSymNwk.ntwrkDetail.title=View Network Details
comSymSymNwk.ntwrkDetail.pageTop.title=View Network Details
comSymSymNwk.ntwrkDetail.ntwrkId=Network ID
comSymSymNwk.ntwrkDetail.ntwrkIp=IP
comSymSymNwk.ntwrkDetail.gtwy=Gateway
comSymSymNwk.ntwrkDetail.subnet=Subnet
comSymSymNwk.ntwrkDetail.domnServer=DNS
comSymSymNwk.ntwrkDetail.manageIem=Management Topics
comSymSymNwk.ntwrkDetail.userNm=User Name
comSymSymNwk.ntwrkDetail.useAt=Usage Status
comSymSymNwk.ntwrkDetail.regstYmd=Registration Date


comSymSymNwk.ntwrkUpdt.title=Modify network
comSymSymNwk.ntwrkUpdt.pageTop.title=Modify network
comSymSymNwk.ntwrkUpdt.ntwrkId=Network ID
comSymSymNwk.ntwrkUpdt.ntwrkIp=IP
comSymSymNwk.ntwrkUpdt.gtwy=Gateway
comSymSymNwk.ntwrkUpdt.subnet=Subnet
comSymSymNwk.ntwrkUpdt.domnServer=DNS
comSymSymNwk.ntwrkUpdt.manageIem=Management Topics
comSymSymNwk.ntwrkUpdt.userNm=User Name
comSymSymNwk.ntwrkUpdt.useAt=Usage Status
comSymSymNwk.ntwrkUpdt.regstYmd=Registration Date
comSymSymNwk.ntwrkUpdt.validate.save=Do you want to save it?
comSymSymNwk.ntwrkUpdt.validate.delete=Are you sure you want to delete?
comSymSymNwk.ntwrkUpdt.validate.ip.notAllowed=The exception is the IP address.
comSymSymNwk.ntwrkUpdt.validate.ip.formatMismatch=IP format mismatch.


comSymSymNwk.ntwrkRegist.title=Network Registration
comSymSymNwk.ntwrkRegist.pageTop.title=Network Registration
comSymSymNwk.ntwrkRegist.ntwrkId=Network ID
comSymSymNwk.ntwrkRegist.ntwrkIp=IP
comSymSymNwk.ntwrkRegist.gtwy=Gateway
comSymSymNwk.ntwrkRegist.subnet=Subnet
comSymSymNwk.ntwrkRegist.domnServer=DNS
comSymSymNwk.ntwrkRegist.manageIem=Management Topics
comSymSymNwk.ntwrkRegist.userNm=User Name
comSymSymNwk.ntwrkRegist.useAt=Usage Status
comSymSymNwk.ntwrkRegist.regstYmd=Registration Date
comSymSymNwk.ntwrkRegist.validate.save=Do you want to save it?
comSymSymNwk.ntwrkRegist.validate.delete=Are you sure you want to delete?
comSymSymNwk.ntwrkRegist.validate.ip.notAllowed=The exception is the IP address.
comSymSymNwk.ntwrkRegist.validate.ip.formatMismatch=IP format mismatch.



