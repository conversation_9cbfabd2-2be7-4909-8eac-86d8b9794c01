<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE form-validation PUBLIC 
    "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.1//EN" 
    "http://jakarta.apache.org/commons/dtds/validator_1_1.dtd">

<form-validation>

    <formset>

        <form name="entrprsManageVO"> 
            <field property="entrprsmberId" depends="required, maxlength">
                <arg0 key="기업회원아이디" resource="true"/>
                <arg1 key="20" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>20</var-value>
                </var>
            </field>
            
            <field property="cmpnyNm" depends="required, maxlength">
                <arg0 key="회사명" resource="true"/>
                <arg1 key="50" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>50</var-value>
                </var>
            </field>
            
            <field property="entrprsMberPassword" depends="required, password1, pwdCheckSeries, pwdCheckRepeat, pwdCheckComb3">
                <arg0 key="비밀번호" resource="true"/>
            </field>
            
            <field property="entrprsMberPasswordHint" depends="required">
                <arg0 key="비밀번호힌트" resource="true"/>
            </field>
            
            <field property="entrprsMberPasswordCnsr" depends="required, maxlength">
                <arg0 key="비밀번호정답" resource="true"/>
                <arg1 key="100" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>100</var-value>
                </var>
            </field>
            
            <field property="bizrno" depends="required">
                <arg0 key="사업자등록번호" resource="true"/>
            </field>
            
            <field property="areaNo" depends="required, maxlength, integer">
                <arg0 key="회사지역번호" resource="true"/>
                <arg1 key="4" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>4</var-value>
                </var>
            </field> 
            
            <field property="entrprsMiddleTelno" depends="required, maxlength, integer">
                <arg0 key="회사중간전화번호" resource="true"/>
                <arg1 key="4" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>4</var-value>
                </var>
            </field>
            
            <field property="entrprsEndTelno" depends="required, maxlength, integer">
                <arg0 key="회사마지막전화번호" resource="true"/>
                <arg1 key="4" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>4</var-value>
                </var>
            </field>
            
            <field property="zip" depends="required">
                <arg0 key="우편번호" resource="true"/>
            </field>
            
            <field property="adres" depends="required, maxlength">
                <arg0 key="주소" resource="true"/>
                <arg1 key="100" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>100</var-value>
                </var>
            </field>
            
            <field property="applcntNm" depends="required">
                <arg0 key="신청자이름" resource="true"/>
            </field>
            <!-- 
            <field property="applcntIhidnum" depends="required, ihidnum">
                <arg0 key="신청자주민등록번호" resource="true"/>
            </field>
             -->
            <field property="applcntEmailAdres" depends="required, email,maxlength">
                <arg0 key="신청자이메일주소" resource="true"/>
                <arg1 key="50" resource="true"/>
                <var>
	                <var-name>maxlength</var-name>
	                <var-value>50</var-value>
                </var>
            </field>
            
            <!--<field property="groupId" depends="required">
                <arg0 key="그룹코드" resource="true"/>
            </field> -->
            
            <field property="entrprsMberSttus" depends="required">
                <arg0 key="사용자상태코드" resource="true"/>
            </field>
            
        </form>
        
    </formset>

</form-validation>