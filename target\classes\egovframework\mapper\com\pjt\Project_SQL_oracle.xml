<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="Project">

	<resultMap id="project" type="egovframework.com.pjt.service.ProjectVO">
		<result property="id" column="id"/>
		<result property="projectName" column="projectName"/>
		<result property="url" column="url"/>
		<result property="userId" column="userID"/>
		<result property="password" column="password"/>
		<result property="typecode" column="typecode"/>
		<result property="workcode" column="workcode"/>
		<result property="skwon" column="skwon"/>
		<result property="ekwon" column="ekwon"/>
	</resultMap>

	<resultMap id="projectInfo" type="egovframework.com.pjt.service.ProjectVO">
		<result property="id" column="id"/>
		<result property="projectName" column="projectName"/>
		<result property="url" column="url"/>
		<result property="userId" column="userID"/>
		<result property="password" column="password"/>
	</resultMap>

	<resultMap id="projectName" type="egovframework.com.pjt.service.ProjectVO">
		<result property="projectName" column="projectName"/>
	</resultMap>

	<select id="selectProjectList" resultMap="project" parameterType="egovframework.com.cmm.LoginVO">
	    SELECT
	        PI.PROJECT_ID     AS id,
	        PI.PROJECT_NAME   AS projectName,
	        PI.DB_URL         AS url,
	        PI.DB_USERNAME    AS userID,
	        PI.DB_PASSWORD    AS password,
	        DECODE(PR.TYPE_CD,'1','입력','2','검수','') AS typecode,
	        PR.WORK_CD        AS workcode,
	        PR.SKWON_NO       AS skwon,
	        PR.EKWON_NO       AS ekwon
	    FROM PROJECT_INFO PI
	    JOIN PROJECT_ROLE PR ON PI.PROJECT_ID = PR.PROJECT_ID
	    WHERE PR.MBER_ID = #{id}
	    ORDER BY PI.PROJECT_NAME, PR.TYPE_CD, PR.WORK_CD, PR.SKWON_NO
	</select>

    <select id="selectProjectInfoByName" resultMap="projectInfo">
        SELECT
        	PROJECT_ID AS id,
        	PROJECT_NAME AS projectName,
        	DB_URL AS url,
        	DB_USERNAME AS userID,
        	DB_PASSWORD AS password
        FROM PROJECT_INFO
        WHERE PROJECT_NAME = #{projectName}
    </select>

    <select id="selectAllProjectInfo" resultMap="projectInfo">
        SELECT
        	PROJECT_ID AS id,
        	PROJECT_NAME AS projectName,
        	DB_URL AS url,
        	DB_USERNAME AS userID,
        	DB_PASSWORD AS password
        FROM PROJECT_INFO
        ORDER BY PROJECT_ID
    </select>

    <select id="selectProjectById" resultMap="projectInfo" parameterType="int">
        SELECT
        	PROJECT_ID AS id,
        	PROJECT_NAME AS projectName,
        	DB_URL AS url,
        	DB_USERNAME AS userID,
        	DB_PASSWORD AS password
        FROM PROJECT_INFO
        WHERE PROJECT_ID = #{value}
    </select>

    <insert id="insertProject" parameterType="egovframework.com.pjt.service.ProjectVO">
        INSERT INTO PROJECT_INFO (
            PROJECT_ID,
            PROJECT_NAME,
            DB_URL,
            DB_USERNAME,
            DB_PASSWORD
        ) VALUES (
            (SELECT NVL(MAX(PROJECT_ID), 0) + 1 FROM PROJECT_INFO),
            #{projectName},
            #{url},
            #{userId},
            #{password}
        )
    </insert>

    <update id="updateProject" parameterType="egovframework.com.pjt.service.ProjectVO">
        UPDATE PROJECT_INFO
        SET
            PROJECT_NAME = #{projectName},
            DB_URL = #{url},
            DB_USERNAME = #{userId},
            DB_PASSWORD = #{password}
        WHERE PROJECT_ID = #{id}
    </update>

    <delete id="deleteProject" parameterType="egovframework.com.pjt.service.ProjectVO">
        DELETE FROM PROJECT_INFO
        WHERE PROJECT_ID = #{id}
    </delete>

    <!-- 프로젝트 역할 정보 조회 -->
    <select id="selectProjectRoles" resultMap="project" parameterType="egovframework.com.pjt.service.ProjectVO">
        SELECT
            PR.MBER_ID AS mberId,
            GM.MBER_NM AS mberNm,
            PR.PROJECT_ID AS id,
            DECODE(PR.TYPE_CD,'1','입력','2','검수','') AS typecode,
            PR.WORK_CD AS workcode,
            PR.SKWON_NO AS skwon,
            PR.EKWON_NO AS ekwon
        FROM PROJECT_ROLE PR JOIN COMTNGNRLMBER GM ON PR.MBER_ID = GM.MBER_ID
        WHERE PR.PROJECT_ID = #{id}
        ORDER BY PR.TYPE_CD, PR.WORK_CD, PR.SKWON_NO
    </select>

    <!-- 프로젝트 역할 정보 삭제 -->
    <delete id="deleteProjectRoles" parameterType="egovframework.com.pjt.service.ProjectVO">
        DELETE FROM PROJECT_ROLE
        WHERE PROJECT_ID = #{id}
        AND   MBER_ID = #{mberId}
        AND   TYPE_CD = #{typecode}
    </delete>

    <!-- 프로젝트 역할 정보 추가 -->
    <insert id="insertProjectRole" parameterType="egovframework.com.pjt.service.ProjectVO">
        INSERT INTO PROJECT_ROLE (
            PROJECT_ID,
            MBER_ID,
            TYPE_CD,
            WORK_CD,
            SKWON_NO,
            EKWON_NO
        ) VALUES (
            #{id},
            #{mberId},
            SUBSTR(#{typecode}, 1, 1),
            #{workcode},
            #{skwon},
            #{ekwon}
        )
    </insert>

</mapper>