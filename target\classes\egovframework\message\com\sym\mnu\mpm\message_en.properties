# EgovMenuManage.xml
comSymMnuMpm.menuManage.validate.menuNo=Menu No.
comSymMnuMpm.menuManage.validate.menuOrdr=Menu Order
comSymMnuMpm.menuManage.validate.menuNm=Menu Name
comSymMnuMpm.menuManage.validate.upperMenuId=Upper Menu No.
comSymMnuMpm.menuManage.validate.progrmFileNm=Program File Name
comSymMnuMpm.menuManage.validate.menuDc=Menu Description
comSymMnuMpm.menuManage.validate.relateImagePath=Related Image Path
comSymMnuMpm.menuManage.validate.relateImageNm=Related Image Name


comSymMnuMpm.menuList.title=Menu Information Registration
comSymMnuMpm.menuList.pageTop.title=Menu List

comSymMnuMpm.menuList.menuNo=Menu No.
comSymMnuMpm.menuList.menuOrdr=Menu Order
comSymMnuMpm.menuList.menuNm=Menu Name
comSymMnuMpm.menuList.upperMenuId=Upper Menu No.
comSymMnuMpm.menuList.progrmFileNm=File Name
comSymMnuMpm.menuList.relateImageNm=Related Image Name
comSymMnuMpm.menuList.relateImagePath=Related Image Path
comSymMnuMpm.menuList.menuDc=Menu Description

comSymMnuMpm.menuList.newWindow=New Window
comSymMnuMpm.menuList.mvmnMenuList=Search Menu
comSymMnuMpm.menuList.searchFileNm=Program File Name Search

comSymMnuMpm.menuList.validate.checkVal=You can only modify or delete the detail view.
comSymMnuMpm.menuList.validate.checkVal.update=You can only modify or delete the detail view. Please reset and register.

comSymMnuMpm.menuList.validate.menuNo.notNull=Menu number are required.
comSymMnuMpm.menuList.validate.menuNo.onlyNumber=You can only enter numbers for menu numbers.
comSymMnuMpm.menuList.validate.menuOrdr.notNull=Menu order is required..
comSymMnuMpm.menuList.validate.menuOrdr.onlyNumber=Only numbers can be entered in menu order.
comSymMnuMpm.menuList.validate.upperMenuId.notNull=The upper menu number is required.
comSymMnuMpm.menuList.validate.upperMenuId.onlyNumber=Upper menu numbers can only be entered in numbers.
comSymMnuMpm.menuList.validate.progrmFileNm.notNull=Program file name is required.
comSymMnuMpm.menuList.validate.menuNm.notNull=Menu name is required.

comSymMnuMpm.menuList.validate.chkBrowse=Menu list data does not exist.
comSymMnuMpm.menuList.validate.chkObject=The menu does not exist. Please register after menu.


comSymMnuMpm.menuManage.title=Menu Management List
comSymMnuMpm.menuManage.pageTop.title=Menu Management List
comSymMnuMpm.menuManage.menuNo=Menu No.
comSymMnuMpm.menuManage.menuNmHn=Menu Name
comSymMnuMpm.menuManage.progrmFileNm=Program File Name
comSymMnuMpm.menuManage.menuDc=Menu Description
comSymMnuMpm.menuManage.upperMenuId=Upper Menu No.
comSymMnuMpm.menuManage.menuNm=Menu Name


comSymMnuMpm.menuDetailSelectUpdt.title=Menu Detailed View and Edit
comSymMnuMpm.menuDetailSelectUpdt.pageTop.title=Menu Detailed View and Edit
comSymMnuMpm.menuDetailSelectUpdt.menuNo=Menu No.
comSymMnuMpm.menuDetailSelectUpdt.menuOrder=Menu Order
comSymMnuMpm.menuDetailSelectUpdt.menuNm=Menu Name
comSymMnuMpm.menuDetailSelectUpdt.upperMenuId=Upper Menu No.
comSymMnuMpm.menuDetailSelectUpdt.progrmFileNm=Menu Name
comSymMnuMpm.menuDetailSelectUpdt.relateImageNm=Related Image Name
comSymMnuMpm.menuDetailSelectUpdt.relateImagePath=Related Image Path
comSymMnuMpm.menuDetailSelectUpdt.menuDc=Menu Description
comSymMnuMpm.menuDetailSelectUpdt.selectMenuSearch=Select Menu Search
comSymMnuMpm.menuDetailSelectUpdt.programFileNameSearch=Program File Name Search
comSymMnuMpm.menuDetailSelectUpdt.newWindow=New Window


comSymMnuMpm.menuRegist.title=Menu Information Registration
comSymMnuMpm.menuRegist.pageTop.title=Menu Information Registration
comSymMnuMpm.menuRegist.menuNo=Menu No.
comSymMnuMpm.menuRegist.menuOrder=Menu Order
comSymMnuMpm.menuRegist.menuNm=Menu Name
comSymMnuMpm.menuRegist.upperMenuId=Upper Menu No.
comSymMnuMpm.menuRegist.progrmFileNm=Menu Name
comSymMnuMpm.menuRegist.relateImageNm=Related Image Name
comSymMnuMpm.menuRegist.relateImagePath=Related Image Path
comSymMnuMpm.menuRegist.menuDc=Menu Description
comSymMnuMpm.menuRegist.selectMenuSearch=Select Menu Search
comSymMnuMpm.menuRegist.programFileNameSearch=Program File Name Search
comSymMnuMpm.menuRegist.newWindow=New Window


comSymMnuMpm.menuBndeRegist.title=Bulk Registration of Menu
comSymMnuMpm.menuBndeRegist.pageTop.title=Bulk Registration of Menu
comSymMnuMpm.menuBndeRegist.menuNo=Bulk List File
comSymMnuMpm.menuBndeRegist.validate.confirm.insert=Would you like to register the menu in bulk? \\nYou can not delete menu information, program list, program change history.
comSymMnuMpm.menuBndeRegist.validate.confirm.delete=Do you want to delete the menu in bulk? \\nThe menu information, program list, and program change history data are all deleted.
comSymMnuMpm.menuBndeRegist.validate.alert.checkFile=Please specify a file to upload.
comSymMnuMpm.menuBndeRegist.validate.checkFile=The file format is incorrect. \\nYou can upload only xls, xlsx!


comSymMnuMpm.menuMvmn.title=Move menu
comSymMnuMpm.menuMvmn.pageTop.title=Move menu
comSymMnuMpm.menuMvmn.menuNo=Menu Name to Move
comSymMnuMpm.menuMvmn.validate.alert.menu=The menu does not exist. Please register after using menu!


comSymMnuMpm.head.head=Common service test site
comSymMnuMpm.mainView.mainViewTitle=Ministry of the Interior and Safety Common service test site
comSymMnuMpm.mainView.selectBBSListPortlet=List of generated boards
comSymMnuMpm.mainView.selectCommuMasterListPortlet=List of generated Community boards
comSymMnuMpm.mainView.selectBlogListPortlet=List of generated Blogs
comSymMnuMpm.mainView.deptSchdulManageMainList=Department schedule management
comSymMnuMpm.mainView.indvdlSchdulManageMainList=My schedule
comSymMnuMpm.mainView.exchangeRateInformation=View submenu
comSymMnuMpm.mainView.subMenuList=\ubd80\uba54\ub274 \ubcf4\uae30
comSymMnuMpm.mainView.getBannerImage=Banner
comSymMnuMpm.main_bottom.mainBottomTitle=Bottom main
comSymMnuMpm.main_bottom.address=03171 Sejong Daero 209 (Government Seoul Government Office), Jongno-gu, Seoul / 30128 Sejong Special Self-Governing Province Government 2 Government Office 13 (Government Sejong Government Office)