#--------------------------
# for GPKI LDAP access
#--------------------------
#gpki.ldap.ip=ldap.gcc.go.kr
#gpki.ldap.ip=**********
gpki.ldap.ip=************
gpki.ldap.port=389
gpki.ldap.basedn=ou=Group of Server,o=Government of Korea,c=kr
gpki.ldap.attribute=usercertificate;binary

#--------------------------
# \uc778\uc99d\uc11c \uc815\ubcf4
# \uc2e4\uc81c \uc778\uc99d\uc11c \uad00\ub828 \ud30c\uc77c\ub4e4\uc740 \uac01 \uc18d\uc131\ub4e4\uc744 \uc870\ud569\ud574\uc11c \uc5bb\uc74c
#--------------------------
gpki.certificate.path = /home/<USER>/certificate/class1
gpki.certificate.server = server
gpki.privatekey.password = ******
#gpki.certificate.path = C:/Documents and Settings/Administrator/egovProps/gpkisecureweb/certs
#gpki.certificate.server = 1310505011
#gpki.privatekey.password = 8866kim4
