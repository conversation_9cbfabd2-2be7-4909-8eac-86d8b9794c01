# Conservative management comUssIonCtn
# EgovCtsnnManage.xml
comUssIonCtn.ctsnnManage.validate.usNm = Applicant
comUssIonCtn.ctsnnManage.validate.ctsnnNm = Light
comUssIonCtn.ctsnnManage.validate.ctsnnCd = Contour Code
comUssIonCtn.ctsnnManage.validate.occrrDe = date of occurrence
comUssIonCtn.ctsnnManage.validate.trgterNm = Target Name
comUssIonCtn.ctsnnManage.validate.relate = relationship
comUssIonCtn.ctsnnManage.validate.brth = Date of birth
comUssIonCtn.ctsnnManage.validate.sanctnDtNm = Certified

# EgovCtsnnManageList.jsp
comUssIonCtn.ctsnnManageList.title = List of Conservative Management
comUssIonCtn.ctsnnManageList.validate.searchFromDate = Signature application date The start date of the search condition is later than the end date. Please check the application date.
comUssIonCtn.ctsnnManageList.searchKeyword = Classification
comUssIonCtn.ctsnnManageList.selectedAll = All
comUssIonCtn.ctsnnManageList.occurDate = date of occurrence
comUssIonCtn.ctsnnManageList.searchFromDate = Start date of application
comUssIonCtn.ctsnnManageList.searchToDate = End date of application
comUssIonCtn.ctsnnManageList.searchNm = Applicant
comUssIonCtn.ctsnnManageList.searchConfmAt = Progress
comUssIonCtn.ctsnnManageList.searchConfmAt.A = Applying
comUssIonCtn.ctsnnManageList.searchConfmAt.C = Confirm
comUssIonCtn.ctsnnManageList.searchConfmAt.R = Reject
comUssIonCtn.ctsnnManageList.ctsnnCdNm = Light
comUssIonCtn.ctsnnManageList.usNm = Applicant
comUssIonCtn.ctsnnManageList.orgnztNm = Affiliation
comUssIonCtn.ctsnnManageList.sanctnDt = date of approval
comUssIonCtn.ctsnnManageList.sanctnerNm = Approver

# EgovCtsnnRegist.jsp
comUssIonCtn.ctsnnRegist.title = Registered Condolences
comUssIonCtn.ctsnnRegist.ctsnnApply = Apply for a congratulatory address
comUssIonCtn.ctsnnRegist.ctsnnAplyr = applicant
comUssIonCtn.ctsnnRegist.usNm = Applicant
comUssIonCtn.ctsnnRegist.usOrgnztNm = belonging
comUssIonCtn.ctsnnRegist.ctsnnNm = Light
comUssIonCtn.ctsnnRegist.ctsnnCd = Classification
comUssIonCtn.ctsnnRegist.occrrDe = Date of occurrence
comUssIonCtn.ctsnnRegist.trgterNm = Subject name
comUssIonCtn.ctsnnRegist.relate = relationship
comUssIonCtn.ctsnnRegist.brth = Date of birth
comUssIonCtn.ctsnnRegist.remark = Remarks
comUssIonCtn.ctsnnRegist.infrmlSanctnId = Author

# EgovCtsnnUpdt.jsp
comUssIonCtn.ctsnnUpdt.title = Edit the congratulations
comUssIonCtn.ctsnnUpdt.ctsnnAplyr = applicant
comUssIonCtn.ctsnnUpdt.usNm = Applicant
comUssIonCtn.ctsnnUpdt.usOrgnztNm = belongs
comUssIonCtn.ctsnnUpdt.ctsnnNm = Light
comUssIonCtn.ctsnnUpdt.EnterCtsnnNm = Please enter Light.
comUssIonCtn.ctsnnUpdt.ctsnnCd = Classification
comUssIonCtn.ctsnnUpdt.occrrDe = Date of occurrence
comUssIonCtn.ctsnnUpdt.trgterNm = Subject name
comUssIonCtn.ctsnnUpdt.EnterTrgterNm = Please enter Subject name.
comUssIonCtn.ctsnnUpdt.relate = relationship
comUssIonCtn.ctsnnUpdt.brth = Date of birth
comUssIonCtn.ctsnnUpdt.remark = Remarks
comUssIonCtn.ctsnnUpdt.infrmlSanctnId = Author

# EgovCtsnnManageDetail.jsp
comUssIonCtn.ctsnnManageDetail.title = Detailed information
comUssIonCtn.ctsnnManageDetail.ctsnnAplyr = applicant
comUssIonCtn.ctsnnManageDetail.usNm = Applicant
comUssIonCtn.ctsnnManageDetail.usOrgnztNm = belongs
comUssIonCtn.ctsnnManageDetail.ctsnnNm = Light
comUssIonCtn.ctsnnManageDetail.ctsnnCd = Classification
comUssIonCtn.ctsnnManageDetail.occrrDe = Date of occurrence
comUssIonCtn.ctsnnManageDetail.trgterNm = Subject name
comUssIonCtn.ctsnnManageDetail.relate = relationship
comUssIonCtn.ctsnnManageDetail.brth = Date of birth
comUssIonCtn.ctsnnManageDetail.remark = Remarks
comUssIonCtn.ctsnnManageDetail.infrmlSanctnId = Author
comUssIonCtn.ctsnnManageDetail.submit = Send

# EgovCtsnnConfm.jsp
comUssIonCtn.ctsnnConfm.title = Caretaker approval
comUssIonCtn.ctsnnConfm.submit = Transfer
comUssIonCtn.ctsnnConfm.ctsnnAplyr = applicant
comUssIonCtn.ctsnnConfm.usNm = Applicant
comUssIonCtn.ctsnnConfm.orgnztNm = Affiliation
comUssIonCtn.ctsnnConfm.ctsnnNm = Light
comUssIonCtn.ctsnnConfm.ctsnnCdNm = Classification
comUssIonCtn.ctsnnConfm.occrrDe = Date of occurrence
comUssIonCtn.ctsnnConfm.trgterNm = Subject name
comUssIonCtn.ctsnnConfm.relateNm = relationship
comUssIonCtn.ctsnnConfm.brth = Date of birth
comUssIonCtn.ctsnnConfm.remark = Remarks
comUssIonCtn.ctsnnConfm.infrmlSanctnId = Author
comUssIonCtn.ctsnnConfm.agree = Approval
comUssIonCtn.ctsnnConfm.disagree = Cmd

# EgovCtsnnConfmList.jsp
comUssIonCtn.ctsnnConfmList.title = Recipient List
comUssIonCtn.ctsnnConfmList.searchFromDate = Application date The start date of the search condition is later than the end date. Please check the application date.
comUssIonCtn.ctsnnConfmList.titleList = Recipient approval management list
comUssIonCtn.ctsnnConfmList.searchKeyword = Classification
comUssIonCtn.ctsnnConfmList.selectedAll = All
comUssIonCtn.ctsnnConfmList.applyDate = Application date
comUssIonCtn.ctsnnConfmList.searchFromDate = Start date of application
comUssIonCtn.ctsnnConfmList.searchToDate = End date of application
comUssIonCtn.ctsnnConfmList.searchNm = Applicant
comUssIonCtn.ctsnnConfmList.searchConfmAt = Progress
comUssIonCtn.ctsnnConfmList.searchConfmAt.A = Applying
comUssIonCtn.ctsnnConfmList.searchConfmAt.C = Agree
comUssIonCtn.ctsnnConfmList.searchConfmAt.R = Reject
comUssIonCtn.ctsnnConfmList.searchConfmAt.agree = Approval Processing
comUssIonCtn.ctsnnConfmList.searchConfmAt.detail = View details