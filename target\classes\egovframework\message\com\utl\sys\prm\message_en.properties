#Process Monitoring#
comUtlSysPrm.comUtlProcessMonList.title = Process Monitoring List
comUtlSysPrm.comUtlProcessMonDetail.title = Process Monitoring Details
comUtlSysPrm.comUtlProcessMonDetail.summary = This table provides information about the process monitoring targets and consists of process name, status, administrator name, and administrator email.
comUtlSysPrm.comUtlProcessMonModify.title = Modifying Process Monitoring
comUtlSysPrm.comUtlProcessMonRegist.title = Register Process Monitoring
comUtlSysPrm.comUtlProcessMonLogList.title = Process Monitoring Log List
comUtlSysPrm.comUtlProcessMonLogDetail.title = Process Monitoring Log Details
comUtlSysPrm.comUtlProcessMonLogDetail.summary = This table provides the process monitoring log target information and consists of the logID, process ID, process name, status, log, and the last generation date.

comUtlSysPrm.comUtlProcessMon.seq = No
comUtlSysPrm.comUtlProcessMon.logID = LogID
comUtlSysPrm.comUtlProcessMon.processID = Process ID
comUtlSysPrm.comUtlProcessMon.processName = Process Name
comUtlSysPrm.comUtlProcessMon.status = Status
comUtlSysPrm.comUtlProcessMon.logInfo = Log
comUtlSysPrm.comUtlProcessMon.managerName = Manager
comUtlSysPrm.comUtlProcessMon.managerEmail = Email
comUtlSysPrm.comUtlProcessMon.createdDateTime = Created Datetime
comUtlSysPrm.comUtlProcessMon.lastCreatedDateTime = Lastly Created Datetime
comUtlSysPrm.comUtlProcessMon.searchBgnDe = Enter Start Date
comUtlSysPrm.comUtlProcessMon.searchBgnHour = Select Start Hour
comUtlSysPrm.comUtlProcessMon.searchEndDe = Enter End Date
comUtlSysPrm.comUtlProcessMon.searchEndHour = Select End hour