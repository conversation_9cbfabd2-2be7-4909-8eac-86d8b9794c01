<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd">

	<bean id="excelZipService" class="org.egovframe.rte.fdl.excel.impl.EgovExcelServiceImpl">
		<property name="mapClass" value="egovframework.com.sym.ccm.zip.service.impl.EgovCcmExcelZipMapping" />
		<property name="sqlSessionTemplate" ref="egov.sqlSessionTemplate" />
	</bean>
	
	<bean id="excelRdnmadZipService" class="org.egovframe.rte.fdl.excel.impl.EgovExcelServiceImpl">
		<property name="mapClass" value="egovframework.com.sym.ccm.zip.service.impl.EgovCcmExcelRdnmadZipMapping" />
		<property name="sqlSessionTemplate" ref="egov.sqlSessionTemplate" />
	</bean>

</beans>
