package egovframework.com.pjt.service;

import java.io.Serializable;

public class MsDataVO implements Serializable {

	private static final long serialVersionUID = -4583279425178947382L;

    private String rollNo;
    private String frameNo;
    private String srNo;
    private String munserType;
    private String pathName;
    private String fileName;

    private String jimok;        // 지목
    private String jijuk;        // 지적
    private String jijukUnit;    // 지적 단위
    private String grade1;       // 과세가격1
    private String grade2;       // 과세가격2
    private String grideUnit;    // 과세가격 단위
    private String jimokLine;    // 지목 라인
    private String jijukLine;    // 지적 라인
    private String gradeLine;    // 과세가격 라인

    private String history;      // 연혁

    private String yearCode;     // 연호 코드
    private String hisDate;      // 날짜
    private String yearCode_d;     // 삭제 연호 코드
    private String hisDate_d;      // 삭제 날짜
    
    private String reason;       // 사고
    private String address;      // 주소
    private String name;         // 소유자
    private String percent;      // 소유자 비율

	private String jimeung;		 // 지명
	private String jibun; 		 // 지번
    private String grade;        // 등급
    private String jukyo;        // 적요


    // Getter / Setter
public String getJijukUnit() {
	return jijukUnit;
}

public void setJijukUnit(String jijukUnit) {
	this.jijukUnit = jijukUnit;
}

public String getGrideUnit() {
	return grideUnit;
}

public void setGrideUnit(String grideUnit) {
	this.grideUnit = grideUnit;
}
    public String getRollNo() {
        return rollNo;
    }

    public void setRollNo(String rollNo) {
        this.rollNo = rollNo;
    }

    public String getFrameNo() {
        return frameNo;
    }

    public void setFrameNo(String frameNo) {
        this.frameNo = frameNo;
    }

	public String getSrNo() {
		return srNo;
	}

	public void setSrNo(String srNo) {
		this.srNo = srNo;
	}

	public String getMunserType() {
		return munserType;
	}

	public void setMunserType(String munserType) {
		this.munserType = munserType;
	}

	public String getPathName() {
		return pathName;
	}

	public void setPathName(String pathName) {
		this.pathName = pathName;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getJimok() {
		return jimok;
	}

	public void setJimok(String jimok) {
		this.jimok = jimok;
	}

	public String getJijuk() {
		return jijuk;
	}

	public void setJijuk(String jijuk) {
		this.jijuk = jijuk;
	}

	public String getGrade1() {
		return grade1;
	}

	public void setGrade1(String grade1) {
		this.grade1 = grade1;
	}

	public String getGrade2() {
		return grade2;
	}

	public void setGrade2(String grade2) {
		this.grade2 = grade2;
	}

	public String getJimokLine() {
		return jimokLine;
	}

	public void setJimokLine(String jimokLine) {
		this.jimokLine = jimokLine;
	}

	public String getGradeLine() {
		return gradeLine;
	}

	public void setGradeLine(String gradeLine) {
		this.gradeLine = gradeLine;
	}

	public String getJijukLine() {
		return jijukLine;
	}

	public void setJijukLine(String jijukLine) {
		this.jijukLine = jijukLine;
	}

	public String getGradeUnit() {
		return grideUnit;
	}

	public void setGradeUnit(String gradeUnit) {
		this.grideUnit = gradeUnit;
	}

	public String getHistory() {
		return history;
	}

	public void setHistory(String history) {
		this.history = history;
	}

	public String getGrade() {
		return grade;
	}

	public void setGrade(String grade) {
		this.grade = grade;
	}

	public String getJukyo() {
		return jukyo;
	}

	public void setJukyo(String jukyo) {
		this.jukyo = jukyo;
	}

	public String getYearCode() {
		return yearCode;
	}

	public void setYearCode(String yearCode) {
		this.yearCode = yearCode;
	}

	public String getHisDate() {
		return hisDate;
	}

	public void setHisDate(String hisDate) {
		this.hisDate = hisDate;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPercent() {
		return percent;
	}

	public void setPercent(String percent) {
		this.percent = percent;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getJimeung() {
		return jimeung;
	}

	public void setJimeung(String jimeung) {
		this.jimeung = jimeung;
	}

	public String getJibun() {
		return jibun;
	}

	public void setJibun(String jibun) {
		this.jibun = jibun;
	}

	public String getYearCode_d() {
		return yearCode_d;
	}

	public void setYearCode_d(String yearCode_d) {
		this.yearCode_d = yearCode_d;
	}

	public String getHisDate_d() {
		return hisDate_d;
	}

	public void setHisDate_d(String hisDate_d) {
		this.hisDate_d = hisDate_d;
	}
}

