#Transmission and reception monitoring#
comUtlSysTrm.trsmrcvMntrngList.title = Transmission and Reception Monitoring List
comUtlSysTrm.trsmrcvMntrngDetail.title = Transmission and Reception Monitoring Details
comUtlSysTrm.trsmrcvMntrngDetail.summary = Provides details about registered transmission and reception monitoring.
comUtlSysTrm.trsmrcvMntrngUpdt.title = Modifying Transmission and Reception Monitoring
comUtlSysTrm.trsmrcvMntrngUpdt.summary = It provides a function to modify transmission and reception monitoring.
comUtlSysTrm.trsmrcvMntrngRegist.title = Register Transmission and Reception Monitoring
comUtlSysTrm.trsmrcvMntrngLogList.title = Transmission and Reception Monitoring Log List
comUtlSysTrm.trsmrcvMntrngLogDetail.title = Transmission and Reception Monitoring Log Details
comUtlSysTrm.trsmrcvMntrngLogDetail.summary = Provides detailed information about the sending and receiving monitor logs.
         
comUtlSysTrm.trsmrcvMntrng.logID = LogID
comUtlSysTrm.trsmrcvMntrng.interOperationID = Link ID
comUtlSysTrm.trsmrcvMntrng.interOperationName = Link Name
comUtlSysTrm.trsmrcvMntrng.interOperationPopup = Link information check pop-up provided
comUtlSysTrm.trsmrcvMntrng.testClassName = Test Class Name
comUtlSysTrm.trsmrcvMntrng.managerName = Manager
comUtlSysTrm.trsmrcvMntrng.managerEmail = Manager Email
comUtlSysTrm.trsmrcvMntrng.monitoringDateTime = Monitoring Datetime
comUtlSysTrm.trsmrcvMntrng.provider = Provider
comUtlSysTrm.trsmrcvMntrng.provideSystem = Provider System
comUtlSysTrm.trsmrcvMntrng.service = Services
comUtlSysTrm.trsmrcvMntrng.requester = Requester
comUtlSysTrm.trsmrcvMntrng.requestSystem = Reguesting System
comUtlSysTrm.trsmrcvMntrng.status = Status
comUtlSysTrm.trsmrcvMntrng.logInfo = Log Information

comUtlSysTrm.trsmrcvMntrng.searchStartDate = Search start date
comUtlSysTrm.trsmrcvMntrng.searchStartHour = Search start hour
comUtlSysTrm.trsmrcvMntrng.searchStartMin = Search start minute
comUtlSysTrm.trsmrcvMntrng.searchEndDate = Search end date
comUtlSysTrm.trsmrcvMntrng.searchEndHour = Search end hour
comUtlSysTrm.trsmrcvMntrng.searchEndMin = Search end minute