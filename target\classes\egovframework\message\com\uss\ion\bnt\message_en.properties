#\ub2f9\uc9c1\uad00\ub9ac comUssIonBnt
# EgovBndtCeckManage.xml
comUssIonBnt.bndtCeckManage.validate.bndtCeckSe = Duty check division
comUssIonBnt.bndtCeckManage.validate.bndtCeckCd = Check code on duty
comUssIonBnt.bndtCeckManage.validate.bndtCeckCdNm = Check Code Name
comUssIonBnt.bndtCeckManage.validate.useAt = Whether to use

#EgovBndtDiary.xml
comUssIonBnt.bndtDiary.validate.bndtId = Job ID
comUssIonBnt.bndtDiary.validate.bndtDe = date on watch
comUssIonBnt.bndtDiary.validate.bndtCeckSe = Duty check division
comUssIonBnt.bndtDiary.validate.bndtCeckCd = Check code on duty
comUssIonBnt.bndtDiary.validate.chckSttus = Check Status

# EgovBndtManage.xml
comUssIonBnt.bndtManage.validate.bndtId = Job ID
comUssIonBnt.bndtManage.validate.bndtDe = Date on watch
comUssIonBnt.bndtManage.validate.remark = Remarks

# EgovBndtManageList.jsp
comUssIonBnt.bndtManageList.title = Watch Management List
comUssIonBnt.bndtManageList.bndtExcelRegist = Register your job excel
comUssIonBnt.bndtManageList.bndtList = Watchlist

# EgovBndtManageRegist.jsp comUssIonBnt.bndtManageRegist.
comUssIonBnt.bndtManageRegist.title = Register your job

# EgovBndtManageDetail.jsp
comUssIonBnt.bndtManageDetail.title = Watch Details

# EgovBndtManageUpdt.jsp
comUssIonBnt.bndtManageUpdt.title = Show correction

# EgovBndtManageBndeListPop.jsp
comUssIonBnt.bndtManageBndeListPop.title = Register in Bulk
comUssIonBnt.bndtManageBndeListPop.bndtExcelFile = Watcher Excel file
comUssIonBnt.bndtManageBndeListPop.file = Bulk file
comUssIonBnt.bndtManageBndeListPop.upload = Upload

# EgovBndtDiaryDetail.jsp
comUssIonBnt.bndtDiaryDetail.title = Detailed logbook

# EgovBndtDiaryRegist.jsp
comUssIonBnt.bndtDiaryRegist.title = Log on the watch log

# EgovBndtDiaryUpdt.jsp
comUssIonBnt.bndtDiaryUpdt.title = Edit watch diary

# EgovBndtCeckManageList.jsp
comUssIonBnt.bndtCeckManageList.title = Watch Management List

# EgovBndtCeckManageRegist.jsp
comUssIonBnt.bndtCeckManageRegist.title = Register watches check

# EgovBndtCeckManageUpdt.jsp
comUssIonBnt.bndtCeckManageUpdt.title = Edit watch checks

# EgovBndtCeckManageDetail.jsp
comUssIonBnt.bndtCeckManageDetail.title = Detailed view of duty check

#Common comUssIonBnt.common.
comUssIonBnt.common.schdulSe = Turnover date
comUssIonBnt.common.year = year
comUssIonBnt.common.year.prev = Year Previous
comUssIonBnt.common.year.next = after year
comUssIonBnt.common.month.prev = previous Month
comUssIonBnt.common.month.next = next month
comUssIonBnt.common.month = month
comUssIonBnt.common.toNewWindow = Move to new window
comUssIonBnt.common.submit = Send
comUssIonBnt.common.sun = Sun
comUssIonBnt.common.mon = Mon
comUssIonBnt.common.tues = Tue
comUssIonBnt.common.wed = Wed
comUssIonBnt.common.thurs = Thur
comUssIonBnt.common.fri = Fri
comUssIonBnt.common.sat = Sat
comUssIonBnt.common.watcher = Watcher
comUssIonBnt.common.writeComplete = Completed
comUssIonBnt.common.diary = Diary
comUssIonBnt.common.insert = Registration
comUssIonBnt.common.yetInsert = Unregistered
comUssIonBnt.common.bndtIdName = Name of Watcher
comUssIonBnt.common.bndt = Watch
comUssIonBnt.common.orgnztNm = Affiliation
comUssIonBnt.common.bndtDe = date on watch
comUssIonBnt.common.remark = Remarks
comUssIonBnt.common.watcherName = Watch Subject
comUssIonBnt.common.validate.fileNull = Please specify the file to upload.
comUssIonBnt.common.validate.formNotMatch = The file format is incorrect. \\n Only xls, XLS, xlsx, XLSX \\nYou can upload!
comUssIonBnt.common.validate.excelProcessComplete = You have completed the registration process for Excel.
comUssIonBnt.common.altIconImg = Image of title icon
comUssIonBnt.common.bndtCeckCdNm = Watch checklist
comUssIonBnt.common.bndtCeckCd.good = Good
comUssIonBnt.common.bndtCeckCd.bad = Bad
comUssIonBnt.common.validate.fieldRequiredInput = field is required.
comUssIonBnt.common.requiredInputSign = Show Mandatory Input
comUssIonBnt.common.searchBndtCeckSe = Show division
comUssIonBnt.common.selectedAll = All
comUssIonBnt.common.bndtCeckCd = Check code on duty
comUssIonBnt.common.bndtCeckCdName = Job code check code
comUssIonBnt.common.useAt = Enabled?
comUssIonBnt.common.useAt.y = Enabled
comUssIonBnt.common.useAt.n = Not used
comUssIonBnt.common.bndtCeckTemp1 = Watch check classification