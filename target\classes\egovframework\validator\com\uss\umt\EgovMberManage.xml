<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE form-validation PUBLIC 
    "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.1//EN" 
    "http://jakarta.apache.org/commons/dtds/validator_1_1.dtd">

<form-validation>

    <formset>

        <form name="mberManageVO"> 
        
            <field property="mberId" depends="required, maxlength">
                <arg0 key="일반회원아이디" resource="true"/>
                <arg1 key="20" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>20</var-value>
                </var>
            </field>
            
            <field property="mberNm" depends="required, maxlength">
                <arg0 key="일반회원이름" resource="true"/>
                <arg1 key="50" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>50</var-value>
                </var>
            </field>
            
            <field property="password" depends="required, password1, pwdCheckSeries, pwdCheckRepeat, pwdCheckComb3">
                <arg0 key="비밀번호" resource="true"/>
            </field>
            
            <field property="passwordHint" depends="required">
                <arg0 key="비밀번호힌트" resource="true"/>
            </field>
            
            <field property="passwordCnsr" depends="required, maxlength">
                <arg0 key="비밀번호정답" resource="true"/>
                <arg1 key="100" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>100</var-value>
                </var>
            </field>
            <!-- 
            <field property="ihidnum" depends="required, ihidnum">
                <arg0 key="주민등록번호" resource="true"/>
            </field>
            -->
            <field property="areaNo" depends="required, maxlength, integer">
                <arg0 key="집지역번호" resource="true"/>
                <arg1 key="4" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>4</var-value>
                </var>
            </field> 
            
            <field property="middleTelno" depends="required, maxlength, integer">
                <arg0 key="집중간전화번호" resource="true"/>
                <arg1 key="4" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>4</var-value>
                </var>
            </field>
            
            <field property="endTelno" depends="required, maxlength, integer">
                <arg0 key="집마지막전화번호" resource="true"/>
                <arg1 key="4" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>4</var-value>
                </var>
            </field>
            
            <field property="mberFxnum" depends="maxlength">
                <arg0 key="팩스번호" resource="true"/>
                <arg1 key="15" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>15</var-value>
                </var>
            </field>
            
            <field property="moblphonNo" depends="required, maxlength">
                <arg0 key="핸드폰번호" resource="true"/>
                <arg1 key="15" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>15</var-value>
                </var>
            </field>
            
            <field property="mberEmailAdres" depends="required, email, maxlength">
                <arg0 key="이메일주소" resource="true"/>
                <arg1 key="50" resource="true"/>
                <var>
	                <var-name>maxlength</var-name>
	                <var-value>50</var-value>
                </var>
            </field>
            
            <field property="zip" depends="required">
                <arg0 key="우편번호" resource="true"/>
                <arg1 key="6" resource="true"/>
                <var>
	                <var-name>maxlength</var-name>
	                <var-value>6</var-value>
                </var>
            </field>
            
            <field property="adres" depends="required, maxlength">
                <arg0 key="주소" resource="true"/>
                <arg1 key="100" resource="true"/>
                <var>
                <var-name>maxlength</var-name>
                <var-value>100</var-value>
                </var>
            </field>
            
            <!--<field property="groupId" depends="required">
                <arg0 key="그룹코드" resource="true"/>
            </field>-->
            
            <field property="mberSttus" depends="required">
                <arg0 key="일반회원상태코드" resource="true"/>
            </field>
            
        </form>
        
    </formset>

</form-validation>