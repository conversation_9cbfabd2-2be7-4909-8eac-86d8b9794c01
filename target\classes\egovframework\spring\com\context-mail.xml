<?xml version="1.0" encoding="UTF-8"?> 
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
    xmlns:p="http://www.springframework.org/schema/p"  
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd"> 
     
    <!-- 일반용   
    <bean id="mntrngMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl" 
        p:host="STMP서버주소"  
        p:username="아이디" 
        p:password="비밀번호" /> 
    --> 
    
    <!-- 메일 연동 인터페이스에서 첨부파일 미사용 -->
    <bean id="EMSMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl" 
        p:host="STMP서버주소" 
        p:port="465"  
        p:protocol="smtps" 
        p:username="아이디" 
        p:password="비밀번호"> 
        <property name="javaMailProperties"> 
            <props> 
                <prop key="mail.smtps.auth">true</prop> 
                <prop key="mail.smtps.startls.enable">true</prop> 
                <prop key="mail.smtps.debug">true</prop> 
            </props> 
        </property> 
    </bean>
     
    <!-- 메일 연동 인터페이스에서 첨부파일 사용 -->
	<bean id="egovMultiPartEmail" class="egovframework.com.cop.ems.service.EgovMultiPartEmail"
		p:host="이메일SMTP주소"
		p:port="587"
		p:id="아이디"
		p:password="비밀번호"
		p:senderName="System"
		p:emailAddress="이메일주소" />
	  
    <!-- 모니터링 서비스에서 사용 gmail, hanmail 용 -->
    <bean id="mntrngMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl" 
        p:host="이메일SMTP주소"
        p:port="587"
        p:protocol="smtp"
        p:username="아이디" 
        p:password="비밀번호">
        <property name="javaMailProperties"> 
            <props>
                <prop key="mail.smtps.auth">true</prop> 
                <prop key="mail.smtps.startls.enable">true</prop> 
                <prop key="mail.smtps.debug">true</prop> 
            </props> 
        </property> 
    </bean>
    
    <!-- 스케줄러에서 메일발송시 설정 -->
    <bean id="mntrngMessage" class="org.springframework.mail.SimpleMailMessage" 
        p:from="SYSTEM &lt; <EMAIL> &gt;"   
        p:subject="{모니터링종류} 상태통보." 
        p:text="* {모니터링종류}  상태통보.&#13;{에러내용}"/> 

</beans> 