# EgovSystemCntc.xml
comSsiSyiSim.systemCntc.validate.cntcNm=Contact Name
comSsiSyiSim.systemCntc.validate.cntcType=Contact Type
comSsiSyiSim.systemCntc.validate.provdInsttId=Provider
comSsiSyiSim.systemCntc.validate.provdSysId=Providing System
comSsiSyiSim.systemCntc.validate.provdSvcId=Providing Service
comSsiSyiSim.systemCntc.validate.requstInsttId=Request Agency
comSsiSyiSim.systemCntc.validate.requstSysId=Request System
comSsiSyiSim.systemCntc.validate.validBeginDe=Effective Start Date
comSsiSyiSim.systemCntc.validate.useAt=Select Usage Status


comSsiSyiSim.systemCntcList.title=System Contact List
comSsiSyiSim.systemCntcList.pageTop.title=System Contact List
comSsiSyiSim.systemCntcList.index=Index
comSsiSyiSim.systemCntcList.cntcId=System Contact ID
comSsiSyiSim.systemCntcList.cntcNm=System Contact Name
comSsiSyiSim.systemCntcList.confmAt=Approval Status
comSsiSyiSim.systemCntcList.confmAt.Y=Approved
comSsiSyiSim.systemCntcList.confmAt.N=Unapproved


comSsiSyiSim.systemCntcDetail.title=System Contact Detailed view
comSsiSyiSim.systemCntcDetail.pageTop.title=System Contact Detailed view
comSsiSyiSim.systemCntcDetail.cntcId=Contact ID
comSsiSyiSim.systemCntcDetail.cntcNm=Contact Name
comSsiSyiSim.systemCntcDetail.cntcType=Contact Type
comSsiSyiSim.systemCntcDetail.provdInsttId=Provider
comSsiSyiSim.systemCntcDetail.provdSysId=Providing System
comSsiSyiSim.systemCntcDetail.provdSvcId=Providing Service
comSsiSyiSim.systemCntcDetail.requstInsttId=Request Agency
comSsiSyiSim.systemCntcDetail.requstSysId=Request System
comSsiSyiSim.systemCntcDetail.validBeginDe=Effective Start Date
comSsiSyiSim.systemCntcDetail.validEndDe=Effective End Date
comSsiSyiSim.systemCntcDetail.useAt=Usage Status
comSsiSyiSim.systemCntcDetail.confmAt=Select Approval Status
comSsiSyiSim.systemCntcDetail.selectProvdInsttId=Select Provider
comSsiSyiSim.systemCntcDetail.selectProvdSysId=Select Providing System
comSsiSyiSim.systemCntcDetail.selectProvdSvcId=Select Providing Service
comSsiSyiSim.systemCntcDetail.selectRequstInsttId=Select Request Agency
comSsiSyiSim.systemCntcDetail.selectRequstSysId=Select Request System
comSsiSyiSim.systemCntcDetail.selectUseAt=Select Usage Status
comSsiSyiSim.systemCntcDetail.selectConfmAt=Select Approval Status


comSsiSyiSim.systemCntcUpdt.title=System Contact Modification
comSsiSyiSim.systemCntcUpdt.pageTop.title=System Contact Modification
comSsiSyiSim.systemCntcUpdt.cntcId=Contact ID
comSsiSyiSim.systemCntcUpdt.cntcNm=Contact Name
comSsiSyiSim.systemCntcUpdt.cntcType=Contact Type
comSsiSyiSim.systemCntcUpdt.provdInsttId=Provider
comSsiSyiSim.systemCntcUpdt.provdSysId=Providing System
comSsiSyiSim.systemCntcUpdt.provdSvcId=Providing Service
comSsiSyiSim.systemCntcUpdt.requstInsttId=Request Agency
comSsiSyiSim.systemCntcUpdt.requstSysId=Request System
comSsiSyiSim.systemCntcUpdt.validBeginDe=Effective Start Date
comSsiSyiSim.systemCntcUpdt.validEndDe=Effective End Date
comSsiSyiSim.systemCntcUpdt.useAt=Usage Status
comSsiSyiSim.systemCntcUpdt.confmAt=Select Approval Status
comSsiSyiSim.systemCntcUpdt.selectProvdInsttId=Select Provider
comSsiSyiSim.systemCntcUpdt.selectProvdSysId=Select Providing System
comSsiSyiSim.systemCntcUpdt.selectProvdSvcId=Select Providing Service
comSsiSyiSim.systemCntcUpdt.selectRequstInsttId=Select Request Agency
comSsiSyiSim.systemCntcUpdt.selectRequstSysId=Select Request System
comSsiSyiSim.systemCntcUpdt.selectUseAt=Select Usage Status
comSsiSyiSim.systemCntcUpdt.selectConfmAt=Select Approval Status
comSsiSyiSim.systemCntcUpdt.validate.checkValidDe=The valid start date can not be greater than the valid end date.


comSsiSyiSim.systemCntcRegist.title=System Contact Registration
comSsiSyiSim.systemCntcRegist.pageTop.title=System Contact Registration
comSsiSyiSim.systemCntcRegist.cntcId=Contact ID
comSsiSyiSim.systemCntcRegist.cntcNm=Contact Name
comSsiSyiSim.systemCntcRegist.cntcType=Contact Type
comSsiSyiSim.systemCntcRegist.provdInsttId=Provider
comSsiSyiSim.systemCntcRegist.provdSysId=Providing System
comSsiSyiSim.systemCntcRegist.provdSvcId=Providing Service
comSsiSyiSim.systemCntcRegist.requstInsttId=Request Agency
comSsiSyiSim.systemCntcRegist.requstSysId=Request System
comSsiSyiSim.systemCntcRegist.validBeginDe=Effective Start Date
comSsiSyiSim.systemCntcRegist.validEndDe=Effective End Date
comSsiSyiSim.systemCntcRegist.useAt=Usage Status
comSsiSyiSim.systemCntcRegist.confmAt=Select Approval Status
comSsiSyiSim.systemCntcRegist.selectProvdInsttId=Select Provider
comSsiSyiSim.systemCntcRegist.selectProvdSysId=Select Providing System
comSsiSyiSim.systemCntcRegist.selectProvdSvcId=Select Providing Service
comSsiSyiSim.systemCntcRegist.selectRequstInsttId=Select Request Agency
comSsiSyiSim.systemCntcRegist.selectRequstSysId=Select Request System
comSsiSyiSim.systemCntcRegist.selectUseAt=Select Usage Status
comSsiSyiSim.systemCntcRegist.selectConfmAt=Select Approval Status
comSsiSyiSim.systemCntcRegist.validate.checkValidDe=The valid start date can not be greater than the valid end date.


