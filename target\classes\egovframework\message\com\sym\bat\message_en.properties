comSymBat.batchOpertList.title=View Batch Job Management List
comSymBat.batchOpertList.pageTop.title=Batch Job Management List
comSymBat.batchOpertList.batchOpertId=Batch Job ID
comSymBat.batchOpertList.batchOpertNm=Batch Job Name
comSymBat.batchOpertList.batchProgrm=Batch Program
comSymBat.batchOpertList.paramtr=Parameter
comSymBat.batchOpertList.validate.searchKeyword=Please select a search condition.
comSymBat.batchOpertList.searchKeyword=Search Keyword


comSymBat.batchOpertDetail.title=View Batch Job Details
comSymBat.BatchOpertDetail.pageTop.title=View Batch Job Details
comSymBat.batchOpertDetail.batchOpertId=Batch Job ID
comSymBat.batchOpertDetail.batchOpertNm=Batch Job Name
comSymBat.batchOpertDetail.batchProgrm=Batch Program
comSymBat.batchOpertDetail.paramtr=Parameter


comSymBat.batchOpertUpdt.title=Edit Batch Job
comSymBat.batchOpertUpdt.pageTop.title=Edit Batch Job
comSymBat.batchOpertUpdt.batchOpertId=Batch Job ID
comSymBat.batchOpertUpdt.batchOpertNm=Batch Job Name
comSymBat.batchOpertUpdt.batchProgrm=Batch Program
comSymBat.batchOpertUpdt.paramtr=Parameter


comSymBat.batchOpertRegist.title=Registration of Batch Jobs
comSymBat.batchOpertRegist.pageTop.title=Registration of Batch Jobs
comSymBat.batchOpertRegist.batchOpertNm=Batch Job Name
comSymBat.batchOpertRegist.batchProgrm=Batch Program
comSymBat.batchOpertRegist.paramtr=Parameter


comSymBat.batchResultList.title=View Batch Result Management List
comSymBat.batchResultList.pageTop.title=Batch Result List
comSymBat.batchResultList.searchCondition=Search Condition
comSymBat.batchResultList.sttus.value00=All
comSymBat.batchResultList.sttus.value01=Normal
comSymBat.batchResultList.sttus.value02=Abnormal
comSymBat.batchResultList.sttus.value03=Performing
comSymBat.batchResultList.batchResultId=Batch Result ID
comSymBat.batchResultList.batchSchdulId=Batch Schedule ID
comSymBat.batchResultList.batchOpertNm=Batch Job Name
comSymBat.batchResultList.sttusNm=Condition
comSymBat.batchResultList.executBeginTime=Execution Start Time
comSymBat.batchResultList.executEndTime=Execution End time
comSymBat.batchResultList.validate.searchCondition=Please select a search condition.
comSymBat.batchResultList.validate.inputSearchStartDate=Please enter search start date.
comSymBat.batchResultList.validate.inputSearchEndDate=Please enter search end date.
comSymBat.batchResultList.validate.searchStartDate=Start date
comSymBat.batchResultList.validate.searchEndDate=End date
comSymBat.batchResultList.validate.strTmpFromDate=The date format is invalid.
comSymBat.batchResultList.validate.strTmpFromEndDate=Start date can not be greater than end date.
comSymBat.batchResultList.searchKeyword=Search Keyword
comSymBat.batchResultList.searchStartDate=Search Start Date
comSymBat.batchResultList.searchEndDate=Search End Date


comSymBat.batchResultDetail.title=Detailed View Of Batch Result
comSymBat.batchResultDetail.pageTop.title=Detailed View Of Batch Result
comSymBat.batchResultDetail.batchResultId=Batch Result ID
comSymBat.batchResultDetail.batchSchdulId=Batch Schedule ID
comSymBat.batchResultDetail.batchOpertId=Batch Job ID
comSymBat.batchResultDetail.batchOpertNm=Batch Job Name
comSymBat.batchResultDetail.batchProgrm=Batch Program
comSymBat.batchResultDetail.paramtr=Parameter
comSymBat.batchResultDetail.sttusNm=Condition
comSymBat.batchResultDetail.errorInfo=Error information
comSymBat.batchResultDetail.executBeginTime=Start Execution Time
comSymBat.batchResultDetail.executEndTime=Execution End Time


comSymBat.batchSchdulList.title=Batch Schedule Management List
comSymBat.batchSchdulList.pageTop.title=Batch Schedule Management List
comSymBat.batchSchdulList.batchSchdulId=Batch Schedule ID
comSymBat.batchSchdulList.batchOpertNm=Batch Job Name
comSymBat.batchSchdulList.batchProgrm=Batch Program
comSymBat.batchSchdulList.executCycleNm=Execution Cycle
comSymBat.batchSchdulList.executSchdul=Execution Schedule
comSymBat.batchSchdulList.searchCondition=Search Condition
comSymBat.batchSchdulList.searchKeyword=Search Keyword


comSymBat.batchSchdulDetail.title=Batch Schedule Detailed View
comSymBat.batchSchdulDetail.pageTop.title=Batch Schedule Detailed View
comSymBat.batchSchdulDetail.batchSchdulId=Batch Schedule ID
comSymBat.batchSchdulDetail.batchOpertId=Batch Job ID
comSymBat.batchSchdulDetail.batchOpertNm=Batch Job Name
comSymBat.batchSchdulDetail.executCycleNm=Execution Cycle


comSymBat.batchSchdulUpdt.title=Modify Batch Schedule
comSymBat.batchSchdulUpdt.pageTop.title=Modify Batch Schedule
comSymBat.batchSchdulUpdt.validate.executSchdulDfkSes.length=The weekday is the required input value when the execution cycle is selected as weekly.
comSymBat.batchSchdulUpdt.validate.cycleMonth.executSchdulDay=When the execution cycle is monthly, the execution schedule (days) is a required input value.
comSymBat.batchSchdulUpdt.validate.cycleYear.executSchdulMonth=When the execution cycle is annually, the execution schedule (month) is a required input value.
comSymBat.batchSchdulUpdt.validate.cycleYear.executSchdulDay=When the execution cycle is annually, the execution schedule (days) is a mandatory input value.
comSymBat.batchSchdulUpdt.validate.checkDate=The execution schedule (month / day) is not valid.
comSymBat.batchSchdulUpdt.validate.executSchdulDeNm=The execution schedule (date) is a required input value when the execution cycle is only once.
comSymBat.batchSchdulUpdt.validate.executSchdulDeNm.isDate=The execution schedule (date) is invalid.
comSymBat.batchSchdulUpdt.batchSchdulId=Batch Schedule ID
comSymBat.batchSchdulUpdt.batchOpertId=Batch Job ID
comSymBat.batchSchdulUpdt.batchOpertNm=Batch Job Name
comSymBat.batchSchdulUpdt.executCycleNm=Execution Cycle
comSymBat.batchSchdulUpdt.executSchdulDeNm=Execution schedule(Date)
comSymBat.batchSchdulUpdt.executSchdulMonth=Execution schedule(Month)
comSymBat.batchSchdulUpdt.executSchdulDay=Execution schedule(Day)
comSymBat.batchSchdulUpdt.executSchdulHour=Execution schedule(Hour)
comSymBat.batchSchdulUpdt.executSchdulMnt=Execution schedule(Minute)
comSymBat.batchSchdulUpdt.executSchdulSecnd=Execution schedule(Second)
comSymBat.batchSchdulUpdt.spnMonth=Month
comSymBat.batchSchdulUpdt.spnDay=Day
comSymBat.batchSchdulUpdt.spnHH=Hour
comSymBat.batchSchdulUpdt.spnMM=Minute
comSymBat.batchSchdulUpdt.spnSS=Second


comSymBat.batchSchdulRegist.title=Registration Of Batch Schedule
comSymBat.batchSchdulRegist.pageTop.title=Registration Of Batch Schedule
comSymBat.batchSchdulRegist.validate.executSchdulDfkSes.length=The weekday is the required input value when the execution cycle is selected as weekly.
comSymBat.batchSchdulRegist.validate.cycleMonth.executSchdulDay=When the execution cycle is monthly, the execution schedule (days) is a required input value.
comSymBat.batchSchdulRegist.validate.cycleYear.executSchdulMonth=When the execution cycle is annually, the execution schedule (month) is a required input value.
comSymBat.batchSchdulRegist.validate.cycleYear.executSchdulDay=When the execution cycle is annually, the execution schedule (days) is a mandatory input value.
comSymBat.batchSchdulRegist.validate.checkDate=The execution schedule (month / day) is not valid.
comSymBat.batchSchdulRegist.validate.executSchdulDeNm=The execution schedule (date) is a required input value when the execution cycle is only once.
comSymBat.batchSchdulRegist.validate.executSchdulDeNm.isDate=The execution schedule (date) is invalid.
comSymBat.batchSchdulRegist.batchSchdulId=Batch Schedule ID
comSymBat.batchSchdulRegist.batchOpertId=Batch Job ID
comSymBat.batchSchdulRegist.batchOpertNm=Batch Job Name
comSymBat.batchSchdulRegist.executCycleNm=Execution Cycle
comSymBat.batchSchdulRegist.executSchdulDeNm=Execution schedule(Date)
comSymBat.batchSchdulRegist.executSchdulMonth=Execution schedule(Month)
comSymBat.batchSchdulRegist.executSchdulDay=Execution schedule(Day)
comSymBat.batchSchdulRegist.executSchdulHour=Execution schedule(Hour)
comSymBat.batchSchdulRegist.executSchdulMnt=Execution schedule(Minute)
comSymBat.batchSchdulRegist.executSchdulSecnd=Execution schedule(Second)
comSymBat.batchSchdulRegist.spnMonth=Month
comSymBat.batchSchdulRegist.spnDay=Day
comSymBat.batchSchdulRegist.spnHH=Hour
comSymBat.batchSchdulRegist.spnMM=Minute
comSymBat.batchSchdulRegist.spnSS=Second


comSymBat.batchOpertListPopup.title=View Batch Jobs
comSymBat.batchOpertListPopup.pageTop.title=View Batch Jobs
comSymBat.batchOpertListPopup.validate.searchKeyword=Please select a search condition.
comSymBat.batchOpertListPopup.searchCondition=Search Condition
comSymBat.batchOpertListPopup.batchOpertId=Batch Job ID
comSymBat.batchOpertListPopup.batchOpertNm=Batch Job Name
comSymBat.batchOpertListPopup.batchProgrm=Batch Program
comSymBat.batchOpertListPopup.paramtr=Parameter




