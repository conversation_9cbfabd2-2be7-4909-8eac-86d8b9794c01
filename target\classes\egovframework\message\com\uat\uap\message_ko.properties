# EgovLoginPolicy.xml
comUatUap.loginPolicy.validate.ipInfo=IP\uc815\ubcf4
comUatUap.loginPolicy.validate.lmttAt=IP\uc81c\ud55c\uc5ec\ubd80


comUatUap.LoginPolicyList.title=\ub85c\uadf8\uc778\uc815\ucc45 \ubaa9\ub85d\uc870\ud68c
comUatUap.LoginPolicyList.caption=\ub85c\uadf8\uc778\uc815\ucc45 \uad00\ub9ac
comUatUap.LoginPolicyList.userName=\uc0ac\uc6a9\uc790 \uba85
comUatUap.LoginPolicyList.userNameSearch=\uc0ac\uc6a9\uc790\uba85\uac80\uc0c9
comUatUap.LoginPolicyList.userId=\uc0ac\uc6a9\uc790 ID
comUatUap.LoginPolicyList.ipInfo=IP \uc815\ubcf4
comUatUap.LoginPolicyList.restricted=\uc81c\ud55c\uc5ec\ubd80
comUatUap.LoginPolicyList.validate.checkCount=\uc120\ud0dd\ub41c  \ub85c\uadf8\uc778\uc815\ucc45\uc774 \uc5c6\uc2b5\ub2c8\ub2e4.
comUatUap.LoginPolicyList.validate.checkField=\uc870\ud68c\ub41c \uacb0\uacfc\uac00 \uc5c6\uc2b5\ub2c8\ub2e4.
comUatUap.LoginPolicyList.validate.checkIds=\uc870\ud68c \ud6c4 \ub4f1\ub85d\ud558\uc2dc\uae30 \ubc14\ub78d\ub2c8\ub2e4.
comUatUap.LoginPolicyList.validate.checkReg=\uc774\ubbf8 \ub85c\uadf8\uc778\uc815\ucc45\uc774 \ub4f1\ub85d\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.
comUatUap.LoginPolicyList.validate.checkedCounter.onlyOne=\ub4f1\ub85d\ub300\uc0c1 \ud558\ub098\ub9cc \uc120\ud0dd\ud558\uc2ed\uc2dc\uc624.
comUatUap.LoginPolicyList.validate.checkedCounter.none=\uc120\ud0dd\ub41c \ub4f1\ub85d\ub300\uc0c1\uc774  \uc5c6\uc2b5\ub2c8\ub2e4.
comUatUap.LoginPolicyList.validate.delete=\uc0ad\uc81c\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?


comUatUap.loginPolicyUpdt.title=\ub85c\uadf8\uc778\uc815\ucc45 \uc218\uc815
comUatUap.loginPolicyUpdt.pageTop.title=\ub85c\uadf8\uc778\uc815\ucc45 \uc218\uc815
comUatUap.loginPolicyUpdt.emplyrId=\uc0ac\uc6a9\uc790ID
comUatUap.loginPolicyUpdt.emplyrNm=\uc0ac\uc6a9\uc790\uba85
comUatUap.loginPolicyUpdt.ipInfo=IP\uc815\ubcf4
comUatUap.loginPolicyUpdt.lmttAt=IP\uc81c\ud55c\uc5ec\ubd80
comUatUap.loginPolicyUpdt.regDate=\ub4f1\ub85d\uc77c\uc2dc
comUatUap.loginPolicyUpdt.validate.confirm.save=\uc800\uc7a5 \ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
comUatUap.loginPolicyUpdt.validate.confirm.delete=\uc0ad\uc81c \ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
comUatUap.loginPolicyUpdt.validate.info.exceptionIP=\uc608\uc678 \uc544\uc774\ud53c \uc785\ub2c8\ub2e4.
comUatUap.loginPolicyUpdt.validate.info.invalidForm=\ud615\uc2dd\uc774 \uc77c\uce58 \ud558\uc9c0\uc54a\uc2b5\ub2c8\ub2e4.


comUatUap.loginPolicyRegist.title=\ub85c\uadf8\uc778\uc815\ucc45 \ub4f1\ub85d
comUatUap.loginPolicyRegist.pageTop.title=\ub85c\uadf8\uc778\uc815\ucc45 \ub4f1\ub85d
comUatUap.loginPolicyRegist.emplyrId=\uc0ac\uc6a9\uc790ID
comUatUap.loginPolicyRegist.emplyrNm=\uc0ac\uc6a9\uc790\uba85
comUatUap.loginPolicyRegist.ipInfo=IP\uc815\ubcf4
comUatUap.loginPolicyRegist.lmttAt=IP\uc81c\ud55c\uc5ec\ubd80
comUatUap.loginPolicyRegist.regDate=\ub4f1\ub85d\uc77c\uc2dc
comUatUap.loginPolicyRegist.validate.confirm.save=\uc800\uc7a5 \ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
comUatUap.loginPolicyRegist.validate.confirm.delete=\uc0ad\uc81c \ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
comUatUap.loginPolicyRegist.validate.info.exceptionIP=\uc608\uc678 \uc544\uc774\ud53c \uc785\ub2c8\ub2e4.
comUatUap.loginPolicyRegist.validate.info.invalidForm=\ud615\uc2dd\uc774 \uc77c\uce58 \ud558\uc9c0\uc54a\uc2b5\ub2c8\ub2e4.

