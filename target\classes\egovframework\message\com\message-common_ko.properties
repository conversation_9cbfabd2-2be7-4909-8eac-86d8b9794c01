fail.common.msg=\uc5d0\ub7ec\uac00 \ubc1c\uc0dd\ud588\uc2b5\ub2c8\ub2e4!
fail.common.sql=sql \uc5d0\ub7ec\uac00 \ubc1c\uc0dd\ud588\uc2b5\ub2c8\ub2e4! error code: {0}, error msg: {1}
info.nodata.msg=\ud574\ub2f9 \ub370\uc774\ud130\uac00 \uc5c6\uc2b5\ub2c8\ub2e4.

#UI Common  resource#
table.num=\ubc88\ud638
table.regdate=\ub4f1\ub85d\uc77c
table.reger=\ub4f1\ub85d\uc790
table.select=\uc120\ud0dd
title.html=egovframe common component
title.detail=\uc0c1\uc138\uc870\ud68c
title.inquire=\uc870\ud68c
title.update=\uc218\uc815
title.create=\ub4f1\ub85d
title.delete=\uc0ad\uc81c
title.save=\uc800\uc7a5
title.list=\ubaa9\ub85d
title.searchCondition=\uac80\uc0c9\uc870\uac74
title.search=\uac80\uc0c9\uc5b4
title.reply=\ub2f5\uae00
title.scrap=\uc2a4\ud06c\ub7a9
title.comment=\ub313\uae00
title.attachedFileSelect=\ud30c\uc77c\uc120\ud0dd
title.attachedFileDelete=\ud30c\uc77c\uc0ad\uc81c
title.link=\ub9c1\ud06c
title.management=\uad00\ub9ac
title.all=\uc804\uccb4

input.select=\uc120\ud0dd\ud558\uc138\uc694
input.cSelect=\uc120\ud0dd
input.input=\uc785\ub825
input.button=\ubc84\ud2bc
input.selectAll.title=\uc804\uccb4\uc120\ud0dd\uccb4\ud06c\ubc15\uc2a4
input.yes=\uc608
input.no=\uc544\ub2c8\uc624

select.searchCondition=\uc870\ud68c\uc870\uac74 \uc120\ud0dd

button.select=\uc120\ud0dd
button.search=\uac80\uc0c9
button.use=\uc0ac\uc6a9
button.notUsed=\uc0ac\uc6a9\uc911\uc9c0
button.inquire=\uc870\ud68c
button.update=\uc218\uc815
button.create=\ub4f1\ub85d
button.delete=\uc0ad\uc81c
button.deleteDatabase=\uc644\uc804\uc0ad\uc81c
button.close=\ub2eb\uae30
button.save=\uc800\uc7a5
button.list=\ubaa9\ub85d
button.reset=\ucde8\uc18c
button.passwordUpdate=\uc554\ud638\ubcc0\uacbd
button.subscribe=\uac00\uc785\uc2e0\uccad
button.realname=\uc2e4\uba85\ud655\uc778
button.moveToGpin=GPIN\uc2e4\uba85\ud655\uc778\uc73c\ub85c \uc774\ub3d9
button.moveToIhidnum=\uc8fc\ubbfc\ub4f1\ub85d\ubc88\ud638 \uc2e4\uba85\ud655\uc778\uc73c\ub85c \uc774\ub3d9
button.agree=\ub3d9\uc758
button.disagree=\ube44\ub3d9\uc758
button.possible=\uac00\ub2a5
button.impossible=\ubd88\uac00\ub2a5
button.qnaregist=Q&A\ub4f1\ub85d
button.cnsltregist=\uc0c1\ub2f4\ub4f1\ub85d
button.preview=\ubbf8\ub9ac\ubcf4\uae30
button.next=\ub2e4\uc74c
button.add=\ubc14\ub85c\ucd94\uac00
button.confirm=\ud655\uc778
button.back = \ub4a4\ub85c
button.yes = \uc608
button.no = \uc544\ub2c8\uc624
button.home = \ud648
button.user = \uc0ac\uc6a9\uc790\uc9c0\uc6d0
button.cop = \ud611\uc5c5
button.wrkstart = \ucd9c\uadfc
button.wrkend = \ud1f4\uadfc
button.reply = \ub2f5\uae00
button.scrap = \uc2a4\ud06c\ub7a9
button.comment = \ub313\uae00
button.excel = \uc5d1\uc140
button.init=\ucd08\uae30\ud654
button.acknowledgment=\uc2b9\uc778
button.cancelAcknowledgment=\uc2b9\uc778\ucde8\uc18c
button.bulkUpload=\uc77c\uad04\ub4f1\ub85d
button.log = \ub85c\uadf8
button.set = \uc124\uc815
button.move = \uc774\ub3d9
button.answer = \ub2f5\ubcc0

#UI Common  Message#
common.noScriptTitle.msg=\uc790\ubc14\uc2a4\ud06c\ub9bd\ud2b8\ub97c \uc9c0\uc6d0\ud558\uc9c0 \uc54a\ub294 \ube0c\ub77c\uc6b0\uc800\uc5d0\uc11c\ub294 \uc77c\ubd80 \uae30\ub2a5\uc744 \uc0ac\uc6a9\ud558\uc2e4 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
common.searchCondition.msg=\uc774 \ub808\uc774\uc544\uc6c3\uc740 \ud558\ub2e8 \uc815\ubcf4\ub97c \ub300\ud55c \uac80\uc0c9 \uc815\ubcf4\ub85c \uad6c\uc131\ub418\uc5b4 \uc788\uc2b5\ub2c8\ub2e4.

common.summary.list={0}\uc758 \ub0b4\uc5ed\uc5d0 \ub300\ud55c \ubaa9\ub85d\uc744 \ucd9c\ub825\ud569\ub2c8\ub2e4.
common.summary.regist={0}\uc758 \ub4f1\ub85d \uac00\ub2a5\ud55c \ub0b4\uc6a9\uc744 \uc785\ub825\ud558\uc5ec \ub4f1\ub85d \ubc84\ud2bc\uc744 \ud074\ub9ad\ud558\uc5ec \ub4f1\ub85d\ud55c\ub2e4.
common.summary.update={0}\uc758 \ub4f1\ub85d \uac00\ub2a5\ud55c \ub0b4\uc6a9\uc744 \uc785\ub825\ud558\uc5ec \ub4f1\ub85d \ubc84\ud2bc\uc744 \ud074\ub9ad\ud558\uc5ec \uc218\uc815\ud55c\ub2e4.
common.summary.inqire={0}\uc758 \ub0b4\uc5ed\uc5d0 \ub300\ud55c \uc0c1\uc138\uc870\ud68c \ub0b4\uc5ed\uc744 \ucd9c\ub825\ud569\ub2c8\ub2e4.

common.save.msg=\uc800\uc7a5\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
common.regist.msg=\ub4f1\ub85d\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
common.delete.msg=\uc0ad\uc81c\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
common.update.msg=\uc218\uc815\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
common.nodata.msg=\uc790\ub8cc\uac00 \uc5c6\uc2b5\ub2c8\ub2e4. \ub2e4\ub978 \uac80\uc0c9\uc870\uac74\uc744 \uc120\ud0dd\ud574\uc8fc\uc138\uc694
common.required.msg=(\uc740)\ub294 \ud544\uc218\uc785\ub825\ud56d\ubaa9\uc785\ub2c8\ub2e4.
common.acknowledgement.msg=\uc2b9\uc778\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
common.acknowledgementcancel.msg=\uc2b9\uc778\ucde8\uc18c\ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
common.nocomment.msg=\ub313\uae00\uc774 \uc5c6\uc2b5\ub2c8\ub2e4.
common.noguest.msg=\uc791\uc131\ub41c \ubc29\uba85\ub85d\uc774 \uc5c6\uc2b5\ub2c8\ub2e4.

success.request.msg = \uc694\uccad\ucc98\ub9ac\uac00 \uc131\uacf5\uc801\uc73c\ub85c \uc218\ud589\ub418\uc5c8\uc2b5\ub2c8\ub2e4.
success.common.select=\uc815\uc0c1\uc801\uc73c\ub85c \uc870\ud68c\ub418\uc5c8\uc2b5\ub2c8\ub2e4.
success.common.insert=\uc815\uc0c1\uc801\uc73c\ub85c \ub4f1\ub85d\ub418\uc5c8\uc2b5\ub2c8\ub2e4.
success.common.update=\uc815\uc0c1\uc801\uc73c\ub85c \uc218\uc815\ub418\uc5c8\uc2b5\ub2c8\ub2e4.
success.common.delete=\uc815\uc0c1\uc801\uc73c\ub85c \uc0ad\uc81c\ub418\uc5c8\uc2b5\ub2c8\ub2e4.

common.imposbl.fileupload = \ub354 \uc774\uc0c1 \ud30c\uc77c\uc744 \ucca8\ubd80\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
common.isConfmDe.msg=\uc2b9\uc778\uc77c\uc790\ub97c \ud655\uc778 \ubc14\ub78d\ub2c8\ub2e4.
common.isExist.msg = \uc774\ubbf8 \uc874\uc7ac\ud558\uac70\ub098 \uacfc\uac70\uc5d0 \ub4f1\ub85d\uc774 \ub418\uc5c8\ub358 \uc0c1\ud0dc\uc785\ub2c8\ub2e4.

fail.common.insert = \uc0dd\uc131\uc774 \uc2e4\ud328\ud558\uc600\uc2b5\ub2c8\ub2e4.
fail.common.update = \uc218\uc815\uc774 \uc2e4\ud328\ud558\uc600\uc2b5\ub2c8\ub2e4.
fail.common.delete = \uc0ad\uc81c\uac00 \uc2e4\ud328\ud558\uc600\uc2b5\ub2c8\ub2e4.
fail.common.delete.upperMenuExist = \ucc38\uc870\ub418\ub294 \uba54\ub274\uac00 \uc788\uc5b4 \uc0ad\uc81c\uac00 \uc2e4\ud328\ud558\uc600\uc2b5\ub2c8\ub2e4.
fail.common.select = \uc870\ud68c\uc5d0 \uc2e4\ud328\ud558\uc600\uc2b5\ub2c8\ub2e4.
fail.common.login = \ub85c\uadf8\uc778 \uc815\ubcf4\uac00 \uc62c\ubc14\ub974\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.
fail.common.loginIncorrect = {0}\ud68c \uc774\uc0c1 \ub85c\uadf8\uc778 \uc811\uc18d\uc774 \uc2dc\ub3c4 \ub418\uc5b4 \uacc4\uc815\uc774 \uc7a0\uaca8\uc2b5\ub2c8\ub2e4!
fail.common.login.password = \ud328\uc2a4\uc6cc\ub4dc \uc790\ub9ac \uc218\uac00 \uc77c\uce58 \ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.(8\uc790\ub9ac \uc774\uc0c1 20\uc790\ub9ac \uc774\ud558)
fail.common.idsearch = \uc544\uc774\ub514\ub97c \ucc3e\uc744\uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
fail.common.pwsearch = \ube44\ubc00\ubc88\ud638\ub97c \ucc3e\uc744\uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
fail.request.msg = \uc694\uccad\ucc98\ub9ac\ub97c \uc2e4\ud328\ud558\uc600\uc2b5\ub2c8\ub2e4.
fail.common.login.ip = \ub4f1\ub85d\ub41c IP\uac00 \uc544\ub2c8\ubbc0\ub85c \ub85c\uadf8\uc778\uc774 \uac70\ubd80\ub418\uc5c8\uc2b5\ub2c8\ub2e4.

#UI User Message#
fail.user.passwordUpdate1=\ud604\uc7ac \ube44\ubc00\ubc88\ud638\uac00 \ub9de\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.
fail.user.passwordUpdate2=\ube44\ubc00\ubc88\ud638\uc640 \ube44\ubc00\ubc88\ud638 \ud655\uc778\uc774 \uc77c\uce58\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.
info.user.rlnmCnfirm=\uc8fc\ubbfc\ub4f1\ub85d\ubc88\ud638\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc2e4\uba85\ud655\uc778\uc744 \ud558\uc2ed\uc2dc\uc624.
success.user.rlnmCnfirm=\ud589\uc815\uc548\uc804\ubd80\uc758 \uc8fc\ubbfc\ub4f1\ub85d\uc790\ub8cc\uc640 \uc77c\uce58\ud569\ub2c8\ub2e4.
fail.user.rlnmCnfirm=\ud589\uc815\uc548\uc804\ubd80\uc758 \uc8fc\ubbfc\ub4f1\ub85d\uc790\ub8cc\uc640 \uc77c\uce58\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.
fail.user.connectFail=\uc2dc\uc2a4\ud15c \uc7a5\uc560\uac00 \ubc1c\uc0dd\ud588\uc2b5\ub2c8\ub2e4.(\uc778\uc99d\uc11c\ubc84 \uc5f0\uacb0 \uc2e4\ud328)
info.user.rlnmPinCnfirm=\uacf5\uacf5 \uc544\uc774\ud540 \uc544\uc774\ub514\ub97c \uc0ac\uc6a9\ud558\uc5ec \uc2e4\uba85\ud655\uc778\uc744 \ud558\uc2ed\uc2dc\uc624.
success.user.rlnmPinCnfirm=\uacf5\uacf5\uc544\uc774\ud540\uc758 \ub4f1\ub85d\uc790\ub8cc\uc640 \uc77c\uce58\ud569\ub2c8\ub2e4.
fail.user.rlnmPinCnfirm=\uacf5\uacf5\uc544\uc774\ud540\uc758 \ub4f1\ub85d\uc790\ub8cc\uc640 \uc77c\uce58\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.

#Vlidator Errors#
errors.prefix=<div class="error">
errors.suffix=</div><br/>

errors.required={0}\uc740(\ub294) \ud544\uc218 \uc785\ub825\uac12\uc785\ub2c8\ub2e4.
errors.minlength={0}\uc740(\ub294) {1}\uc790 \uc774\uc0c1 \uc785\ub825\ud574\uc57c \ud569\ub2c8\ub2e4.
errors.maxlength={0}\uc740(\ub294) {1}\uc790 \uc774\uc0c1 \uc785\ub825\ud560\uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
errors.invalid={0}\uc740(\ub294) \uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \uac12\uc785\ub2c8\ub2e4.
errors.minInteger={0}\uc740(\ub294) \uc720\ud6a8\ud55c \uac12\uc774 \uc544\ub2d9\ub2c8\ub2e4. 1 \uc774\uc0c1\uc758 \uac12\uc744 \uc785\ub825\ud574\uc57c \ud569\ub2c8\ub2e4.
errors.byte={0}\uc740(\ub294)  byte\ud0c0\uc785\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.
errors.short={0}\uc740(\ub294)  short\ud0c0\uc785\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.
errors.integer={0}\uc740(\ub294) integer \ud0c0\uc785\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.
errors.long={0}\uc740(\ub294) long \ud0c0\uc785\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.
errors.float={0}\uc740(\ub294) float \ud0c0\uc785\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.
errors.double={0}\uc740(\ub294) double \ud0c0\uc785\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.

errors.date={0}\uc740(\ub294) \ub0a0\uc9dc \uc720\ud615\uc774 \uc544\ub2d9\ub2c8\ub2e4.
errors.range={0}\uc740(\ub294) {1}\uacfc {2} \uc0ac\uc774\uc758 \uac12\uc774\uc5b4\uc57c \ud569\ub2c8\ub2e4.
errors.creditcard={0}\uc740(\ub294) \uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \uc2e0\uc6a9\uce74\ub4dc \ubc88\ud638\uc785\ub2c8\ub2e4.
errors.email={0}\uc740(\ub294) \uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \uc774\uba54\uc77c \uc8fc\uc18c\uc785\ub2c8\ub2e4.

errors.ihidnum=\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 \uc8fc\ubbfc\ub4f1\ub85d\ubc88\ud638\uc785\ub2c8\ub2e4.
errors.korean={0}\uc740(\ub294) \ud55c\uae00\uc744 \uc785\ub825\ud558\uc154\uc57c \ud569\ub2c8\ub2e4.
errors.ip=\uc720\ud6a8\ud558\uc9c0 \uc54a\uc740 IP\uc8fc\uc18c\uc785\ub2c8\ub2e4.
errors.english={0}\uc740(\ub294) \uc601\ubb38\ub9cc \uc0ac\uc6a9\ud558\uc2e4\uc218 \uc788\uc2b5\ub2c8\ub2e4.
errors.notKorean={0}\uc740(\ub294) \ud55c\uae00\uc744 \uc0ac\uc6a9\ud558\uc2e4\uc218 \uc5c6\uc2b5\ub2c8\ub2e4.

errors.password1={0}\uc740(\ub294) 8~20\uc790 \ub0b4\uc5d0\uc11c \uc785\ub825\ud574\uc57c \ud569\ub2c8\ub2e4.
errors.password2={0}\uc740(\ub294) \ud55c\uae00,\ud2b9\uc218\ubb38\uc790,\ub744\uc5b4\uc4f0\uae30\ub294 \ud5c8\uc6a9\ub418\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.
errors.password3={0}\uc740(\ub294) \uc21c\ucc28\uc801\uc778 \uc22b\uc790\ub97c 4\uac1c\uc774\uc0c1 \uc5f0\uc18d\ud574\uc11c \uc0ac\uc6a9\ud560\uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
errors.password4={0}\uc740(\ub294) \ubc18\ubcf5\ub418\ub294 \ubb38\uc790\ub098 \uc22b\uc790\ub97c 4\uac1c\uc774\uc0c1 \uc5f0\uc18d\ud574\uc11c \uc0ac\uc6a9\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
errors.pwdcheckcomb3={0}\uc740(\ub294) \uc601\ubb38\uc790,\uc22b\uc790,\ud2b9\uc218\ubb38\uc790(~!@#$%^&*?)\uc758 \uc870\ud569\uc73c\ub85c \uc774\ub8e8\uc5b4 \uc838\uc57c \ud569\ub2c8\ub2e4.
errors.pwdcheckcomb4={0}\uc740(\ub294) \uc601\ubb38\ub300\ubb38\uc790,\uc601\ubb38\uc18c\ubb38\uc790,\uc22b\uc790,\ud2b9\uc218\ubb38\uc790(~!@#$%^&*?)\uc758 \uc870\ud569\uc73c\ub85c \uc774\ub8e8\uc5b4 \uc838\uc57c \ud569\ub2c8\ub2e4.
errors.pwdcheckseries={0}\uc740(\ub294) \uc21c\ucc28\uc801\uc778 \ubb38\uc790\ub098 \uc22b\uc790\ub97c 3\uac1c\uc774\uc0c1 \uc5f0\uc18d\ud574\uc11c \uc0ac\uc6a9\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.
errors.pwdcheckrepeat={0}\uc740(\ub294) \ubc18\ubcf5\ub418\ub294 \ubb38\uc790\ub098 \uc22b\uc790\ub97c 3\uac1c\uc774\uc0c1 \uc5f0\uc18d\ud574\uc11c \uc0ac\uc6a9\ud560 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.

error.security.runtime.error = error

info.password.rule.password1 = - 8\uc774\uc0c1 20\uc790\ub9ac \uc774\ud558 
info.password.rule.pwdcheckcomb3 = - \uc601\ubb38\uc790,\uc22b\uc790,\ud2b9\uc218\ubb38\uc790(~!@#$%^&*?)\uc758 \uc870\ud569 
info.password.rule.pwdcheckcomb4 = - \uc601\ubb38\ub300\ubb38\uc790,\uc601\ubb38\uc18c\ubb38\uc790,\uc22b\uc790,\ud2b9\uc218\ubb38\uc790(~!@#$%^&*?)\uc758 \uc870\ud569 
info.password.rule.pwdcheckseries = - 3\uc790\ub9ac \uc774\uc0c1 \ubc18\ubcf5 \ub610\ub294 \uc5f0\uc18d\ub41c \ubb38\uc790 \uc0ac\uc6a9 \uae08\uc9c0. \uc608)kkk , 321 \ub4f1 \uc0ac\uc6a9\uae08\uc9c0 

#Auth Errors#
errors.auth.invalid=\ud574\ub2f9 \uae30\ub2a5\uc5d0 \ub300\ud55c \ucc98\ub9ac \uad8c\ud55c\uc774 \uc5c6\uc2b5\ub2c8\ub2e4.

#Xss Errors#
errors.xss.checkerUser=\ud574\ub2f9 \uae30\ub2a5\uc5d0 \ub300\ud55c \uc0ac\uc6a9 \ubc0f \ucc98\ub9ac \uad8c\ud55c\uc774 \uc5c6\uc2b5\ub2c8\ub2e4.

#File Upload / Download
errors.file.extension=\uc9c0\uc6d0\ub418\ub294 \ud30c\uc77c\uc720\ud615\uc774 \uc544\ub2d9\ub2c8\ub2e4.
errors.file.transfer=\ud30c\uc77c\uc804\uc1a1\uc911 \uc624\ub958\uac00 \ubc1c\uc0dd\ud588\uc2b5\ub2c8\ub2e4.
success.file.transfer=\ud30c\uc77c\uc804\uc1a1\uc774 \uc644\ub8cc\ub418\uc5c8\uc2b5\ub2c8\ub2e4.
errors.file.extension.none=\ud30c\uc77c \ud655\uc7a5\uc790\uac00 \uc5c6\uc2b5\ub2c8\ub2e4.
errors.file.extension.deny=\ud5c8\uc6a9\ub418\uc9c0 \uc54a\ub294 \ud30c\uc77c \ud655\uc7a5\uc790 \uc785\ub2c8\ub2e4.
