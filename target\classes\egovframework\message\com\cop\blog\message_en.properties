# Blog Main List
comCopBlog.articleBlogList.regCategory=Category Registration
comCopBlog.articleBlogList.setListNumbers=Set Number of Posts
comCopBlog.articleBlogList.listNumber5=5 lines
comCopBlog.articleBlogList.listNumber10=10 lines
comCopBlog.articleBlogList.listNumber15=15 lines
comCopBlog.articleBlogList.listNumber20=20 lines
comCopBlog.articleBlogList.listNumber30=30 lines
comCopBlog.articleBlogList.validate.noResult=No posts! \\nPlease register your post.
comCopBlog.articleBlogList.validate.limitSize=Comments can be up to 500bytes.
comCopBlog.articleBlogList.validate.noContent=The text does not exist!
comCopBlog.articleBlogList.validate.occurError=An error has occurred.
comCopBlog.articleBlogList.validate.noBoard=There are no registered boards.\\nPlease create a board through category registration.

#\uac8c\uc2dc\ud310\uc18d\uc131\uad00\ub9ac BBS Master#
comCopBlog.blogMasterVO.title=Blog

comCopBlog.blogMasterVO.list.blogNm=Blog Name
comCopBlog.blogMasterVO.list.useAt=Usage Status

comCopBlog.blogMasterVO.detail.blogNm=Blog Name
comCopBlog.blogMasterVO.detail.blogIntrcn=About Blog
comCopBlog.blogMasterVO.detail.useAt=Usage Status
comCopBlog.blogMasterVO.detail.blogAdres=Blog Address

comCopBlog.blogMasterVO.regist.blogNm=Blog Name
comCopBlog.blogMasterVO.regist.blogIntrcn=About Blog
comCopBlog.blogMasterVO.regist.useAt=Usage Status

comCopBlog.blogMasterVO.updt.blogNm=Blog Name
comCopBlog.blogMasterVO.updt.blogIntrcn=About Blog
comCopBlog.blogMasterVO.updt.useAt=Usage Status



comCopBlog.blogUserVO.title=Blog User

comCopBlog.blogUserVO.emplyrId=User ID
comCopBlog.blogUserVO.emplyrNm=User Name
comCopBlog.blogUserVO.mberSttus=Member Status
comCopBlog.blogUserVO.etc=Etc


#Manage blog users#
comCopBlog.blogUseMgrMain.title=Blog User Main

comCopBlog.blogUseMgrMain.btnBoard=Personal Blog Management
comCopBlog.blogUseMgrMain.btnUser=User Management

comCopBlog.blogUseMgrMain.btnMemberWithdrawal=Member Withdrawal
comCopBlog.blogUseMgrMain.btnMemberJoin=Member Join

comCopBlog.blogUseMgrMain.titleContents=Content Area


comCopBlog.blogUseMgrBaseTmpl.title=Blog Default Template
comCopBlog.blogUseMgrBaseTmpl.more=More
comCopBlog.blogUseMgrBaseTmpl.noList=There is no registered contents.

comCopBlog.validate.blogUserCheck=A blog already exists with the same account.
