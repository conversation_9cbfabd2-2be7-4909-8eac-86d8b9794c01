# Manage notes
# EgovMemoTodo.xml
comCopSmtMtm.memoToDo.validate.todoNm = To do title
comCopSmtMtm.memoToDo.validate.todoDe = Date of task
comCopSmtMtm.memoToDo.validate.todoBeginHour = Do's start (at)
comCopSmtMtm.memoToDo.validate.todoBeginMin = Start day (in minutes)
comCopSmtMtm.memoToDo.validate.todoEndHour = End Task (at)
comCopSmtMtm.memoToDo.validate.todoEndMin = End Task Days (minutes)
comCopSmtMtm.memoToDo.validate.wrterNm = Author Name
comCopSmtMtm.memoToDo.validate.todoCn = What to do

# EgovMemoTodoList.jsp
comCopSmtMtm.memoTodoList.title = View your to-do list
comCopSmtMtm.memoTodoList.bgnDe = Search start date
comCopSmtMtm.memoTodoList.endDe = Search end date
comCopSmtMtm.memoTodoList.searchBgnDe = Enter View Start Date
comCopSmtMtm.memoTodoList.searchEndDe = Enter View End Date
comCopSmtMtm.memoTodoList.bgnDeEvalEndDe = The search end date can not be earlier than the search start date.
comCopSmtMtm.memoTodoList.searchDeVal = Please select date lookup.
comCopSmtMtm.memoTodoList.searchbgnDeVal = Please enter search start date.
comCopSmtMtm.memoTodoList.searchEndDeVal = Please enter the search end date.
comCopSmtMtm.memoTodoList.aearchCndVal = Please select a task condition.
comCopSmtMtm.memoTodoList.searchDe = Select date lookup
comCopSmtMtm.memoTodoList.searchDe0 = Date of task
comCopSmtMtm.memoTodoList.searchDe1 = Date created
comCopSmtMtm.memoTodoList.searchCnd = To-do condition
comCopSmtMtm.memoTodoList.searchCnd0 = To do title
comCopSmtMtm.memoTodoList.searchCnd1 = What to do
comCopSmtMtm.memoTodoList.toDoListToday = Today
comCopSmtMtm.memoTodoList.frstRegisterPnttm = Date created

# EgovMemoTodoListToday.jsp
comCopSmtMtm.memoTodoListToday.title = View today's todo list
comCopSmtMtm.memoTodoListToday.allList = Full list

# EgovMemoTodoRegist.jsp
comCopSmtMtm.memoTodoRegist.title = Register your note to do
comCopSmtMtm.memoTodoRegist.todoHourMin = Can not be less than minutes at the beginning of the task.

# EgovMemoTodoDetail.jsp
comCopSmtMtm.memoTodoDetail.title = Show details of notes

# EgovMemoTodoUpdt.jsp
comCopSmtMtm.memoTodoUpdt.title = Edit Notes
comCopSmtMtm.memoTodoUpdt.hour = hours
comCopSmtMtm.memoTodoUpdt.minutes = minutes