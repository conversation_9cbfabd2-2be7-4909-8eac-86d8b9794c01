package egovframework.com.pjt.web;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import egovframework.com.pjt.service.MsDataService;
import egovframework.com.pjt.service.MsDataVO;

@Controller
@RequestMapping("/msdata")
public class MsDataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MsDataController.class);

    @Resource(name = "msDataService")
    private MsDataService msDataService;

    @ResponseBody
    @GetMapping("/kwonList")
    public List<MsDataVO> getRoll() throws Exception {
        return msDataService.getDataList();
    }

    @ResponseBody
    @GetMapping("/pageList")
    public List<MsDataVO> getFrame(@RequestParam String roll) throws Exception {
    	return msDataService.getFrameByRoll(roll);
    }

    @ResponseBody
    @GetMapping("/fileName")
    public MsDataVO getFileName(@RequestParam String roll, @RequestParam String frame) throws Exception {
        return msDataService.getFile(roll, frame);
    }
    
    @ResponseBody
    @GetMapping("/maxNo")
    public int getMaxSr(@RequestParam String rollNo, @RequestParam String frameNo, @RequestParam String workcode) throws Exception {
    	LOGGER.info("Controller 전달값 → rollNo = " + rollNo + ", frameNo = " + frameNo + ", workcode = " + workcode);
    	return msDataService.getMaxSrNo(rollNo, frameNo, workcode);
    }

    /**
     * 토지대장 템플릿 페이지 - 통합 함수
     *
     * @param request 요청 객체
     * @param model 모델 객체
     * @param workcode 작업코드 (1: 기본정보, 2: 연혁, 3: 날짜, 4: 소유자, 5: 색인)
     * @param rollNo 권 번호
     * @param frameNo 페이지 번호
     * @return 뷰 이름
     * @throws Exception
     */
    @GetMapping("/land/template.do")
    public String landTemplate(
            HttpServletRequest request,
            ModelMap model,
            @RequestParam(value = "workcode", defaultValue = "0") String workcode,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo) throws Exception {

        // 모델에 기본 파라미터 추가
        model.addAttribute("rollNo", rollNo);
        model.addAttribute("frameNo", frameNo);
        model.addAttribute("workcode", workcode);

        LOGGER.info("Loading data for roll: {}, frame: {}, workcode: {}", rollNo, frameNo, workcode);

        // workcode에 따라 다른 템플릿 반환
        String viewName;
        switch (workcode) {
            case "1":
                viewName = "egovframework/com/pjt/land/landBasicInfo";
                LOGGER.debug("토지대장 기본정보 템플릿 요청: roll_no={}, frame_no={}", rollNo, frameNo);
                break;
            case "2":
                viewName = "egovframework/com/pjt/land/landHistory";
                LOGGER.debug("토지대장 연혁 템플릿 요청: roll_no={}, frame_no={}", rollNo, frameNo);
                break;
            case "3":
                viewName = "egovframework/com/pjt/land/landDateInfo";
                LOGGER.debug("토지대장 날짜 템플릿 요청: roll_no={}, frame_no={}", rollNo, frameNo);
                break;
            case "4":
                viewName = "egovframework/com/pjt/land/landOwnerInfo";
                LOGGER.debug("토지대장 소유자 템플릿 요청: roll_no={}, frame_no={}", rollNo, frameNo);
                break;
            case "5":
                viewName = "egovframework/com/pjt/land/landIndexInfo";
                LOGGER.debug("토지대장 색인 템플릿 요청: roll_no={}, frame_no={}", rollNo, frameNo);
                break;
            default:
                viewName = "egovframework/com/pjt/land/landBasicInfo";
                LOGGER.debug("토지대장 기본 템플릿 요청: roll_no={}, frame_no={}", rollNo, frameNo);
                break;
        }

        return viewName;
    }

    /**
     * 토지대장 기본정보 페이지 (기존 호환성 유지)
     */
    @GetMapping("/land/landBasicInfo.do")
    public String landBasicInfo(
            HttpServletRequest request,
            ModelMap model,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo) throws Exception {

        model.addAttribute("rollNo", rollNo);
        model.addAttribute("frameNo", frameNo);
        model.addAttribute("workcode", "1");

        return "egovframework/com/pjt/land/landBasicInfo";
    }

    /**
     * 토지대장 연혁 페이지 (기존 호환성 유지)
     */
    @GetMapping("/land/landHistory.do")
    public String landHistory(
            HttpServletRequest request,
            ModelMap model,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo) throws Exception {

        model.addAttribute("rollNo", rollNo);
        model.addAttribute("frameNo", frameNo);
        model.addAttribute("workcode", "2");

        return "egovframework/com/pjt/land/landHistory";
    }

    /**
     * 토지대장 날짜/사고/주소 페이지 (기존 호환성 유지)
     */
    @GetMapping("/land/landDateInfo.do")
    public String landDateInfo(
            HttpServletRequest request,
            ModelMap model,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo) throws Exception {

        model.addAttribute("rollNo", rollNo);
        model.addAttribute("frameNo", frameNo);
        model.addAttribute("workcode", "3");

        return "egovframework/com/pjt/land/landDateInfo";
    }

    /**
     * 토지대장 소유자 페이지 (기존 호환성 유지)
     */
    @GetMapping("/land/landOwnerInfo.do")
    public String landOwnerInfo(
            HttpServletRequest request,
            ModelMap model,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo) throws Exception {

        model.addAttribute("rollNo", rollNo);
        model.addAttribute("frameNo", frameNo);
        model.addAttribute("workcode", "4");

        return "egovframework/com/pjt/land/landOwnerInfo";
    }

    /**
     * 토지대장 색인정보 페이지 (기존 호환성 유지)
     */
    @GetMapping("/land/landIndexInfo.do")
    public String landIndexInfo(
            HttpServletRequest request,
            ModelMap model,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo) throws Exception {

        model.addAttribute("rollNo", rollNo);
        model.addAttribute("frameNo", frameNo);
        model.addAttribute("workcode", "5");

        return "egovframework/com/pjt/land/landIndexInfo";
    }


    /**
     * 토지대장 데이터 조회 (AJAX)
     *
     * @param request 요청 객체
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @return 결과 맵
     * @throws Exception
     */
    @GetMapping("/land/getData.do")
    @ResponseBody
    public Map<String, Object> getData(
            HttpServletRequest request,
            @RequestParam(value = "roll_no", defaultValue = "0") String rollNo,
            @RequestParam(value = "frame_no", defaultValue = "0") String frameNo,
            @RequestParam(value = "type", defaultValue = "all") String type) throws Exception {

        Map<String, Object> resultMap = new HashMap<>();

        try {
            Map<String, Object> dataMap = new HashMap<>();

            // 요청 타입에 따른 데이터 조회
            if ("basic".equals(type) || "all".equals(type)) {
                List<MsDataVO> basicInfoList = msDataService.getLandBasicInfo(rollNo, frameNo);
                dataMap.put("basicInfo", basicInfoList);
            }

            if ("history".equals(type) || "all".equals(type)) {
                List<MsDataVO> historyList = msDataService.getLandHistory(rollNo, frameNo);
                dataMap.put("history", historyList);
            }

            if ("date".equals(type) || "all".equals(type)) {
                List<MsDataVO> dateInfoList = msDataService.getLandDateInfo(rollNo, frameNo);
                dataMap.put("dateInfo", dateInfoList);
            }

            if ("owner".equals(type) || "all".equals(type)) {
                List<MsDataVO> ownerInfoList = msDataService.getLandOwnerInfo(rollNo, frameNo);
                dataMap.put("ownerInfo", ownerInfoList);
            }

            if ("index".equals(type) || "all".equals(type)) {
                MsDataVO indexInfo = msDataService.getLandIndexInfo(rollNo, frameNo);
                dataMap.put("indexInfo", indexInfo);
            }

            resultMap.put("success", true);
            resultMap.put("data", dataMap);

            LOGGER.debug("토지대장 데이터 조회: roll_no={}, frame_no={}, type={}", rollNo, frameNo, type);
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "데이터 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("토지대장 데이터 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }

    /**
     * 지목 코드 리스트 조회 (AJAX)
     *
     * @return 지목 코드 리스트
     * @throws Exception
     */
    @GetMapping("/code/jimok.do")
    @ResponseBody
    public Map<String, Object> getJimokCodeList() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            List<Map<String, Object>> jimokCodeList = msDataService.getJimokCodeList();
            resultMap.put("success", true);
            resultMap.put("data", jimokCodeList);

            LOGGER.debug("지목 코드 리스트 조회: {} 개", jimokCodeList.size());
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "지목 코드 리스트 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("지목 코드 리스트 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }

    /**
     * 연혁 코드 리스트 조회 (AJAX)
     *
     * @return 연혁 코드 리스트
     * @throws Exception
     */
    @GetMapping("/code/history.do")
    @ResponseBody
    public Map<String, Object> getHistoryCodeList() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            List<Map<String, Object>> historyCodeList = msDataService.getHistoryCodeList();
            resultMap.put("success", true);
            resultMap.put("data", historyCodeList);

            LOGGER.debug("연혁 코드 리스트 조회: {} 개", historyCodeList.size());
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "연혁 코드 리스트 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("연혁 코드 리스트 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }

    /**
     * 소유자 코드 리스트 조회 (AJAX)
     *
     * @return 소유자 코드 리스트
     * @throws Exception
     */
    @GetMapping("/code/reason.do")
    @ResponseBody
    public Map<String, Object> getReasonCodeList() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            List<Map<String, Object>> ownerCodeList = msDataService.getReasonCodeList();
            resultMap.put("success", true);
            resultMap.put("data", ownerCodeList);

            LOGGER.debug("소유자 코드 리스트 조회: {} 개", ownerCodeList.size());
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "소유자 코드 리스트 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("소유자 코드 리스트 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }

    /**
     * 소유자 연호 코드 리스트 조회 (AJAX)
     *
     * @return 소유자 연호 코드 리스트
     * @throws Exception
     */
    @GetMapping("/code/year.do")
    @ResponseBody
    public Map<String, Object> getYearCodeList() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            List<Map<String, Object>> yearCodeList = msDataService.getYearCodeList();
            resultMap.put("success", true);
            resultMap.put("data", yearCodeList);

            LOGGER.debug("소유자 연호 코드 리스트 조회: {} 개", yearCodeList.size());
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "소유자 연호 코드 리스트 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("소유자 연호 코드 리스트 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }
    
    @GetMapping("/code/address.do")
    @ResponseBody
    public Map<String, Object> getOwnerAddrList() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            List<Map<String, Object>> addressList = msDataService.getOwnerAddrList();
            resultMap.put("success", true);
            resultMap.put("data", addressList);

            LOGGER.debug("소유자 주소 리스트 조회: {} 개", addressList.size());
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "소유자 주소 리스트 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("소유자 주소 리스트 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }
    
    @GetMapping("/code/name.do")
    @ResponseBody
    public Map<String, Object> getOwnerNameList() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            List<Map<String, Object>> nameList = msDataService.getOwnerNameList();
            resultMap.put("success", true);
            resultMap.put("data", nameList);

            LOGGER.debug("소유자 이름 리스트 조회: {} 개", nameList.size());
        } catch (Exception e) {
            resultMap.put("success", false);
            resultMap.put("message", "소유자 이름 리스트 조회 중 오류가 발생했습니다: " + e.getMessage());
            LOGGER.error("소유자 이름 리스트 조회 중 오류 발생: {}", e.getMessage(), e);
        }

        return resultMap;
    }
    
    


    /**
     * 토지대장 기본정보 저장
     */
    @RequestMapping(value = "/land/saveLandBasicInfo.do", method = RequestMethod.POST, consumes = "application/json")
    @ResponseBody
    public Map<String, Object> saveLandBasicInfo(
            HttpServletRequest request,
            @RequestBody Map<String, Object> requestData) throws Exception {

        Map<String, Object> result = new HashMap<>();

        try {
            // 요청 데이터에서 필요한 정보 추출
            String rollNo = (String) requestData.get("rollNo");
            String frameNo = (String) requestData.get("frameNo");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataListMap = (List<Map<String, Object>>) requestData.get("dataList");

            // MsDataVO 리스트로 변환
            List<MsDataVO> dataList = new java.util.ArrayList<>();

            for (Map<String, Object> item : dataListMap) {
                MsDataVO data = new MsDataVO();
                data.setRollNo((String) item.get("rollNo"));
                data.setFrameNo((String) item.get("frameNo"));
                data.setSrNo(String.valueOf(item.get("srNo")));

                // 선택적 필드 설정
                if (item.containsKey("jimok")) data.setJimok((String) item.get("jimok"));
                if (item.containsKey("jimokLine")) data.setJimokLine((String) item.get("jimokLine"));
                if (item.containsKey("jijuk")) data.setJijuk((String) item.get("jijuk"));
                if (item.containsKey("jijukUnit")) data.setJijukUnit((String) item.get("jijukUnit"));
                if (item.containsKey("grade1")) data.setGrade1((String) item.get("grade1"));
                if (item.containsKey("gradeLine")) data.setGradeLine((String) item.get("gradeLine"));
                if (item.containsKey("gradeUnit")) data.setGradeUnit((String) item.get("gradeUnit"));
                if (item.containsKey("grade2")) data.setGrade2((String) item.get("grade2"));

                dataList.add(data);
            }

            boolean success = msDataService.saveLandBasicInfo(rollNo, frameNo, dataList);
            result.put("success", success);
            if (success) {
                result.put("message", "토지대장 기본정보가 저장되었습니다.");
            } else {
                result.put("message", "토지대장 기본정보 저장에 실패했습니다.");
            }
        } catch (Exception e) {
            LOGGER.error("토지대장 지목 저장 중 오류 발생: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "오류가 발생했습니다: " + e.getMessage());
        }

        return result;
    }

    /**
     * 토지대장 연혁 저장
     */
    @RequestMapping(value = "/land/saveLandHistory.do", method = RequestMethod.POST, consumes = "application/json")
    @ResponseBody
    public Map<String, Object> saveLandHistory(
            HttpServletRequest request,
            @RequestBody Map<String, Object> requestData) throws Exception {

        Map<String, Object> result = new HashMap<>();

        try {
            // 요청 데이터에서 필요한 정보 추출
            String rollNo = (String) requestData.get("rollNo");
            String frameNo = (String) requestData.get("frameNo");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataListMap = (List<Map<String, Object>>) requestData.get("dataList");

            // MsDataVO 리스트로 변환
            List<MsDataVO> dataList = new java.util.ArrayList<>();

            for (Map<String, Object> item : dataListMap) {
                MsDataVO data = new MsDataVO();
                data.setRollNo((String) item.get("rollNo"));
                data.setFrameNo((String) item.get("frameNo"));
                data.setSrNo(String.valueOf(item.get("srNo")));

                // 선택적 필드 설정
                if (item.containsKey("history")) data.setHistory((String) item.get("history"));

                dataList.add(data);
            }

            boolean success = msDataService.saveLandHistory(rollNo, frameNo, dataList);
            result.put("success", success);
            if (success) {
                result.put("message", "토지대장 연혁이 저장되었습니다.");
            } else {
                result.put("message", "토지대장 연혁 저장에 실패했습니다.");
            }
        } catch (Exception e) {
            LOGGER.error("토지대장 연혁 저장 중 오류 발생: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "오류가 발생했습니다: " + e.getMessage());
        }

        return result;
    }

    /**
     * 토지대장 날짜/사고/주소 저장
     */
    @RequestMapping(value = "/land/saveLandDateInfo.do", method = RequestMethod.POST, consumes = "application/json")
    @ResponseBody
    public Map<String, Object> saveLandDateInfo(
            HttpServletRequest request,
            @RequestBody Map<String, Object> requestData) throws Exception {

        Map<String, Object> result = new HashMap<>();

        try {
            // 요청 데이터에서 필요한 정보 추출
            String rollNo = (String) requestData.get("rollNo");
            String frameNo = (String) requestData.get("frameNo");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataListMap = (List<Map<String, Object>>) requestData.get("dataList");

            // MsDataVO 리스트로 변환
            List<MsDataVO> dataList = new java.util.ArrayList<>();

            for (Map<String, Object> item : dataListMap) {
                MsDataVO data = new MsDataVO();
                data.setRollNo((String) item.get("rollNo"));
                data.setFrameNo((String) item.get("frameNo"));
                data.setSrNo(String.valueOf(item.get("srNo")));

                // 선택적 필드 설정
                if (item.containsKey("yearCode")) data.setYearCode((String) item.get("yearCode"));
                if (item.containsKey("hisDate")) data.setHisDate((String) item.get("hisDate"));
                if (item.containsKey("yearCode_d")) data.setYearCode((String) item.get("yearCode_d"));
                if (item.containsKey("hisDate_d")) data.setHisDate((String) item.get("hisDate_d"));
                if (item.containsKey("reason")) data.setReason((String) item.get("reason"));
                if (item.containsKey("address")) data.setAddress((String) item.get("address"));

                dataList.add(data);
            }

            boolean success = msDataService.saveLandDateInfo(rollNo, frameNo, dataList);
            result.put("success", success);
            if (success) {
                result.put("message", "토지대장 날짜/사고/주소 정보가 저장되었습니다.");
            } else {
                result.put("message", "토지대장 날짜/사고/주소 정보 저장에 실패했습니다.");
            }
        } catch (Exception e) {
            LOGGER.error("토지대장 날짜/사고/주소 정보 저장 중 오류 발생: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "오류가 발생했습니다: " + e.getMessage());
        }

        return result;
    }

    /**
     * 토지대장 소유자 저장
     */
    @RequestMapping(value = "/land/saveLandOwnerInfo.do", method = RequestMethod.POST, consumes = "application/json")
    @ResponseBody
    public Map<String, Object> saveLandOwnerInfo(
            HttpServletRequest request,
            @RequestBody Map<String, Object> requestData) throws Exception {

        Map<String, Object> result = new HashMap<>();

        try {
            // 요청 데이터에서 필요한 정보 추출
            String rollNo = (String) requestData.get("rollNo");
            String frameNo = (String) requestData.get("frameNo");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataListMap = (List<Map<String, Object>>) requestData.get("dataList");

            // MsDataVO 리스트로 변환
            List<MsDataVO> dataList = new java.util.ArrayList<>();

            for (Map<String, Object> item : dataListMap) {
                MsDataVO data = new MsDataVO();
                data.setRollNo((String) item.get("rollNo"));
                data.setFrameNo((String) item.get("frameNo"));
                data.setSrNo(String.valueOf(item.get("srNo")));

                // 선택적 필드 설정
                if (item.containsKey("name")) data.setName((String) item.get("name"));

                dataList.add(data);
            }

            boolean success = msDataService.saveLandOwnerInfo(rollNo, frameNo, dataList);
            result.put("success", success);
            if (success) {
                result.put("message", "토지대장 소유자 정보가 저장되었습니다.");
            } else {
                result.put("message", "토지대장 소유자 정보 저장에 실패했습니다.");
            }
        } catch (Exception e) {
            LOGGER.error("토지대장 소유자 정보 저장 중 오류 발생: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "오류가 발생했습니다: " + e.getMessage());
        }

        return result;
    }

    /**
     * 토지대장 색인정보 저장
     */
    @RequestMapping(value = "/land/saveLandIndexInfo.do", method = RequestMethod.POST, consumes = "application/json")
    @ResponseBody
    public Map<String, Object> saveLandIndexInfo(
            HttpServletRequest request,
            @RequestBody Map<String, Object> requestData) throws Exception {

        Map<String, Object> result = new HashMap<>();

        try {
            // 요청 데이터에서 필요한 정보 추출
            String rollNo = (String) requestData.get("rollNo");
            String frameNo = (String) requestData.get("frameNo");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataListMap = (List<Map<String, Object>>) requestData.get("dataList");

            // 색인 데이터는 단일 레코드이므로 dataListMap의 첫 번째 항목만 사용
            Map<String, Object> item = dataListMap.get(0);
            MsDataVO data = new MsDataVO();
            data.setRollNo((String) item.get("rollNo"));
            data.setFrameNo((String) item.get("frameNo"));
            data.setSrNo("0");  // 색인 데이터는 단일 레코드이므로 srNo를 0으로 고정

            // 선택적 필드 설정
            if (item.containsKey("jimeung")) data.setJimeung((String) item.get("jimeung"));
            if (item.containsKey("jibun")) data.setJibun((String) item.get("jibun"));
            if (item.containsKey("grade")) data.setGrade((String) item.get("grade"));
            if (item.containsKey("jukyo")) data.setJukyo((String) item.get("jukyo"));

            boolean success = msDataService.saveLandIndexInfo(rollNo, frameNo, data);
            result.put("success", success);
            if (success) {
                result.put("message", "토지대장 색인정보가 저장되었습니다.");
            } else {
                result.put("message", "토지대장 색인정보 저장에 실패했습니다.");
            }
        } catch (Exception e) {
            LOGGER.error("토지대장 색인정보 저장 중 오류 발생: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "오류가 발생했습니다: " + e.getMessage());
        }

        return result;
    }
}