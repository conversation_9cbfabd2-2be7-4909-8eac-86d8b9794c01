comCopSymEms.regist.title=\ubc1c\uc1a1\uba54\uc77c
comCopSymEms.regist.summary={0}\uc758 \ub4f1\ub85d \uac00\ub2a5\ud55c \ub0b4\uc6a9\uc744 \uc785\ub825\ud558\uc5ec \ub4f1\ub85d \ubc84\ud2bc\uc744 \ud074\ub9ad\ud558\uc5ec \ub4f1\ub85d\ud55c\ub2e4.
comCopSymEms.regist.receiver = \ubc1b\ub294\uc0ac\ub78c
comCopSymEms.regist.title2 = \uc81c\ubaa9
comCopSymEms.regist.content = \ubc1c\uc2e0\ub0b4\uc6a9
comCopSymEms.regist.atch = \ucca8\ubd80\ud30c\uc77c

comCopSymEms.inqire.sender = \ubcf4\ub0b4\ub294\uc0ac\ub78c
comCopSymEms.inqire.receiver = \ubc1b\ub294\uc0ac\ub78c
comCopSymEms.inqire.title = \uc81c\ubaa9
comCopSymEms.inqire.sendCn = \ubc1c\uc2e0\ub0b4\uc6a9
comCopSymEms.inqire.sendResult = \ubc1c\uc2e0\uacb0\uacfc
comCopSymEms.inqire.xml = XML\uba54\uc77c\ubcf4\uae30
comCopSymEms.inqire.atch = \ucca8\ubd80\ud30c\uc77c
comCopSymEms.inqire.summary={0}\uc758 \ub0b4\uc5ed\uc5d0 \ub300\ud55c \uc0c1\uc138\uc870\ud68c \ub0b4\uc5ed\uc744 \ucd9c\ub825\ud569\ub2c8\ub2e4.

comCopSymEms.searchCondition.title = \uc81c\ubaa9
comCopSymEms.searchCondition.cn = \ub0b4\uc6a9
comCopSymEms.searchCondition.sender = \ubcf4\ub0b8\uc774

comCopSymEms.list.status = \uc0c1\ud0dc
comCopSymEms.list.receiver = \uc218\uc2e0\uc790
comCopSymEms.list.sender = \ubc1c\uc2e0\uc790
comCopSymEms.list.title = \uc81c\ubaa9
comCopSymEms.list.cn = \ub0b4\uc6a9
comCopSymEms.list.regdate = \ub0a0\uc9dc
comCopSymEms.list.summary={0}\uc758 \ub0b4\uc5ed\uc5d0 \ub300\ud55c \ubaa9\ub85d\uc744 \ucd9c\ub825\ud569\ub2c8\ub2e4.