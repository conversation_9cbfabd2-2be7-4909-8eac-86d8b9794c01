#-----------------------------------------------------------------------
#
#   client.properties : \ud074\ub77c\uc774\uc5b8\ud2b8\uc758 OS\uc815\ubcf4\ub97c \ub098\ud0c0\ub0b4\ub294 Config \ud30c\uc77c
#   
#-----------------------------------------------------------------------
#   1.  key = value \uad6c\uc870\uc785\ub2c8\ub2e4.
#   2.  key\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \ud3ec\ud568\ubd88\uac00, value\uac12\uc740 \uacf5\ubc31\ubb38\uc790\ub97c \uac00\ub2a5
#   3.  key\uac12\uc73c\ub85c \ud55c\uae00\uc744 \uc0ac\uc6a9\ubd88\uac00,   value\uac12\uc740 \ud55c\uae00\uc0ac\uc6a9\uc774 \uac00\ub2a5
#   4.  \uc904\uc744 \ubc14\uafc0 \ud544\uc694\uac00 \uc788\uc73c\uba74 '\'\ub97c \ub77c\uc778\uc758 \ub05d\uc5d0 \ucd94\uac00(\ub9cc\uc57d  '\'\ubb38\uc790\ub97c \uc0ac\uc6a9\ud574\uc57c \ud558\ub294 \uacbd\uc6b0\ub294 '\\'\ub97c \uc0ac\uc6a9)
#   5.  Windows\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '\\' or '/'  ('\' \uc0ac\uc6a9\ud558\uba74 \uc548\ub428)
#   6.  Unix\uc5d0\uc11c\uc758 \ub514\ub809\ud1a0\ub9ac \ud45c\uc2dc : '/'
#   7.  \uc8fc\uc11d\ubb38 \ucc98\ub9ac\ub294  #\uc0ac\uc6a9
#   8.  value\uac12 \ub4a4\uc5d0 \uc2a4\ud398\uc774\uc2a4\uac00 \uc874\uc7ac\ud558\ub294 \uacbd\uc6b0 \uc11c\ube14\ub9bf\uc5d0\uc11c \ucc38\uc870\ud560\ub54c\ub294 \uc5d0\ub7ec\ubc1c\uc0dd\ud560 \uc218 \uc788\uc73c\ubbc0\ub85c trim()\ud558\uac70\ub098 \ub9c8\uc9c0\ub9c9 \uacf5\ubc31\uc5c6\uc774 properties \uac12\uc744 \uc124\uc815\ud560\uac83
#-----------------------------------------------------------------------

# \ud074\ub77c\uc774\uc5b8\ud2b8 OS \uc720\ud615 \ubaa9\ub85d
WINDOWS95 = Windows 95
WINDOWS98 = Windows 98
WINDOWSNT = Windows NT
WINDOWSNT5.0 = Windows 2000
WINDOWSNT5.1 = Windows XP
WINDOWSNT5.2 = Windows Server 2003
WINDOWSNT6.0 = Windows Vista
WIN32 = Windows XP
MSN2.5 = MSN 2.5 on Windows 98
MAC_POWERPC = Mac OS X
SUNOS5.9SUN4U = SunOS 5.0