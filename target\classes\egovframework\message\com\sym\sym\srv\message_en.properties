# EgovServer.xml
comSymSymSrv.server.validate.serverNm=Server Name
comSymSymSrv.server.validate.regstYmd=Registration Date

# EgovServerEqpmn.xml
comSymSymSrv.serverEqpmn.validate.serverEqpmnNm=Name of server equipment
comSymSymSrv.serverEqpmn.validate.serverEqpmnIp=Server Equipment IP
comSymSymSrv.serverEqpmn.validate.serverEqpmnMngrNm=Manager
comSymSymSrv.serverEqpmn.validate.mngrEmailAddr=Admin email address
comSymSymSrv.serverEqpmn.validate.opersysmInfo=OS information
comSymSymSrv.serverEqpmn.validate.cpuInfo=CPU Information
comSymSymSrv.serverEqpmn.validate.moryInfo=Memory information
comSymSymSrv.serverEqpmn.validate.hdDisk=Hard disk
comSymSymSrv.serverEqpmn.validate.regstYmd=Registration Date


comSymSymSrv.serverEqpmnList.title=Server Equipment List
comSymSymSrv.serverEqpmnList.pageTop.title=Server H/W Management
comSymSymSrv.serverEqpmnList.caption=Server H/W Management
comSymSymSrv.serverEqpmnList.serverEqpmnId=Server H/W ID
comSymSymSrv.serverEqpmnList.serverEqpmnNm=Server H/W Name
comSymSymSrv.serverEqpmnList.serverEqpmnIp=Server H/W IP
comSymSymSrv.serverEqpmnList.serverEqpmnMngrNm=Manager
comSymSymSrv.serverEqpmnList.regstYmd=Registration Date


comSymSymSrv.serverEqpmnDetail.title=Detailed View Of Server Equipment
comSymSymSrv.serverEqpmnDetail.pageTop.title=Detailed View Of Server H/W
comSymSymSrv.serverEqpmnDetail.serverEqpmnId=Server H/W ID
comSymSymSrv.serverEqpmnDetail.serverEqpmnNm=Server H/W Name
comSymSymSrv.serverEqpmnDetail.serverEqpmnIp=Server H/W IP
comSymSymSrv.serverEqpmnDetail.serverEqpmnMngrNm=Manager
comSymSymSrv.serverEqpmnDetail.mngrEmailAddr=Manager email
comSymSymSrv.serverEqpmnDetail.opersysmInfo=OS Information
comSymSymSrv.serverEqpmnDetail.cpuInfo=CPU Information
comSymSymSrv.serverEqpmnDetail.moryInfo=Memory Information
comSymSymSrv.serverEqpmnDetail.hdDisk=Hard Disk
comSymSymSrv.serverEqpmnDetail.etcInfo=Other information
comSymSymSrv.serverEqpmnDetail.regstYmd=Registration Date


comSymSymSrv.serverEqpmnUpdt.title=Modify Server Equipment
comSymSymSrv.serverEqpmnUpdt.pageTop.title=Modify Server H/W
comSymSymSrv.serverEqpmnUpdt.serverEqpmnId=Server H/W ID
comSymSymSrv.serverEqpmnUpdt.serverEqpmnNm=Server H/W Name
comSymSymSrv.serverEqpmnUpdt.serverEqpmnIp=Server H/W IP
comSymSymSrv.serverEqpmnUpdt.serverEqpmnMngrNm=Manager
comSymSymSrv.serverEqpmnUpdt.mngrEmailAddr=Manager email
comSymSymSrv.serverEqpmnUpdt.opersysmInfo=OS Information
comSymSymSrv.serverEqpmnUpdt.cpuInfo=CPU Information
comSymSymSrv.serverEqpmnUpdt.moryInfo=Memory Information
comSymSymSrv.serverEqpmnUpdt.hdDisk=Hard Disk
comSymSymSrv.serverEqpmnUpdt.etcInfo=Other information
comSymSymSrv.serverEqpmnUpdt.regstYmd=Registration Date
comSymSymSrv.serverEqpmnUpdt.validate.ip.notAllowed=An unacceptable ip.
comSymSymSrv.serverEqpmnUpdt.validate.save=Do you want to save it?
comSymSymSrv.serverEqpmnUpdt.validate.delete=Are you sure you want to delete?
comSymSymSrv.serverEqpmnUpdt.validate.ip.formatMismatch=IP format mismatch.


comSymSymSrv.serverEqpmnRegist.title=Register Server Equipment
comSymSymSrv.serverEqpmnRegist.pageTop.title=Register Server H/W
comSymSymSrv.serverEqpmnRegist.serverEqpmnNm=Server H/W Name
comSymSymSrv.serverEqpmnRegist.serverEqpmnIp=Server H/W IP
comSymSymSrv.serverEqpmnRegist.serverEqpmnMngrNm=Manager
comSymSymSrv.serverEqpmnRegist.mngrEmailAddr=Manager email
comSymSymSrv.serverEqpmnRegist.opersysmInfo=OS Information
comSymSymSrv.serverEqpmnRegist.cpuInfo=CPU Information
comSymSymSrv.serverEqpmnRegist.moryInfo=Memory Information
comSymSymSrv.serverEqpmnRegist.hdDisk=Hard Disk
comSymSymSrv.serverEqpmnRegist.etcInfo=Other information
comSymSymSrv.serverEqpmnRegist.regstYmd=Registration Date
comSymSymSrv.serverEqpmnRegist.validate.save=Do you want to save it?
comSymSymSrv.serverEqpmnRegist.validate.ip.notAllowed=The exception is the IP address.
comSymSymSrv.serverEqpmnRegist.validate.ip.formatMismatch=IP format mismatch.
comSymSymSrv.serverEqpmnRegist.validate.email.formatMismatch=Invalid email address.


comSymSymSrv.serverList.title=View Server List
comSymSymSrv.serverList.pageTop.title=Server S/W Management
comSymSymSrv.serverList.serverId=Server S/W ID
comSymSymSrv.serverList.serverNm=Server S/W Name
comSymSymSrv.serverList.serverKndNm=Server S/W Type
comSymSymSrv.serverList.regstYmd=Registration Date
comSymSymSrv.serverList.regServerHW=Server H/W Registration


comSymSymSrv.serverEqpmnRelateRegist.title=Server H/W Relationship Registration
comSymSymSrv.serverEqpmnRelateRegist.pageTop.title=Server H/W Registration
comSymSymSrv.serverEqpmnRelateRegist.validate.noServer=There is no changed server H/W.
comSymSymSrv.serverEqpmnRelateRegist.validate.noResults=No query results found.
comSymSymSrv.serverEqpmnRelateRegist.validate.save=Do you want to save it?
comSymSymSrv.serverEqpmnRelateRegist.serverEqpmnId=Server H/W ID
comSymSymSrv.serverEqpmnRelateRegist.serverEqpmnNm=Server H/W Name
comSymSymSrv.serverEqpmnRelateRegist.serverEqpmnIp=Server H/W IP
comSymSymSrv.serverEqpmnRelateRegist.serverEqpmnMngrNm=Manager
comSymSymSrv.serverEqpmnRelateRegist.regYn=Registration Status
comSymSymSrv.serverEqpmnRelateRegist.regYn.Y=Registered
comSymSymSrv.serverEqpmnRelateRegist.regYn.N=Unregistered


comSymSymSrv.serverDetail.title=Detailed Information Of Server Information
comSymSymSrv.serverDetail.pageTop.title1=Detailed View Of S/W
comSymSymSrv.serverDetail.serverId=Server S/W ID
comSymSymSrv.serverDetail.serverNm=Server S/W Name
comSymSymSrv.serverDetail.serverKndNm=Server S/W Type
comSymSymSrv.serverDetail.regstYmd=Registration Date
comSymSymSrv.serverDetail.pageTop.title2=Server H/W List
comSymSymSrv.serverDetail.serverEqpmnNm=Server H/W Name
comSymSymSrv.serverDetail.serverEqpmnIp=Server H/W IP
comSymSymSrv.serverDetail.cpuInfo=CPU Information
comSymSymSrv.serverDetail.moryInfo=Memory Information
comSymSymSrv.serverDetail.serverEqpmnMngrNm=Manager
comSymSymSrv.serverDetail.opersysmInfo=OS Information
comSymSymSrv.serverDetail.hdDisk=Hard Disk
comSymSymSrv.serverDetail.etcInfo=Other information


comSymSymSrv.serverUpdt.title=Edit Server Information
comSymSymSrv.serverUpdt.pageTop.title=Modify Server Software
comSymSymSrv.serverUpdt.validate.save=Do you want to save it?
comSymSymSrv.serverUpdt.validate.delete=Are you sure you want to delete?
comSymSymSrv.serverUpdt.serverId=Server S/W ID
comSymSymSrv.serverUpdt.serverNm=Server S/W Name
comSymSymSrv.serverUpdt.serverKndNm=Server S/W Type
comSymSymSrv.serverUpdt.regstYmd=Registration Date


comSymSymSrv.serverRegist.title=Server Information Registration
comSymSymSrv.serverRegist.pageTop.title=Server Software Registration
comSymSymSrv.serverRegist.serverNm=Server S/W Name
comSymSymSrv.serverRegist.serverKndNm=Server S/W Type
comSymSymSrv.serverRegist.regstYmd=Registration Date
comSymSymSrv.serverRegist.validate.save=Do you want to save it?




