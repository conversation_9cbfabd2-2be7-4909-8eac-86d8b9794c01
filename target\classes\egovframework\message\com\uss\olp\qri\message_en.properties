#Questionnaire Respond Information Message#
comUssOlpQri.title=Questionnaire Respond Information Management
comUssOlpQri.titleManage=Questionnaire Respond Information Management
comUssOlpQri.searchCondition.ETC_ANSWER_CN=Contents of Other Answer
comUssOlpQri.searchCondition.RESPOND_ANSWER_CN=Contents of Respond Answer
comUssOlpQri.searchCondition.RESPOND_NM=Respond Name

comUssOlpQri.regist.qestnrCn=Questionnaire Contents
comUssOlpQri.regist.qestnrQesitmCn=Questionnaire Question Contents
comUssOlpQri.regist.qestnTyCode=Questionnaire Type
comUssOlpQri.regist.qustnrIemCn=Questionnaire Item Contents
comUssOlpQri.regist.respondAnswerCn=Contents of Respond Answer
comUssOlpQri.regist.etcAnswerCn=Contents of Other Answer
comUssOlpQri.regist.respondNm=Respond Name

comUssOlpQri.regist.qestnType=Question Type
comUssOlpQri.regist.qustnrqestn=Questionnaire Question
comUssOlpQri.regist.qustnrIem=Questionnaire Item
comUssOlpQri.regist.writerName=Writer Name
comUssOlpQri.regist.registerDate=Register Date

comUssOlpQri.regist.objectiveQuest=Objective Question
comUssOlpQri.regist.subjectiveQuest=Subjective Question

comUssOlpQri.regist.newWindow=New Window

comUssOlpQri.title.submit=Submit

comUssOlpQri.regist.mxmmChoiseCo=Number of Maximum Choice

comUssOlpQri.title.template=Template
comUssOlpQri.title.etcAnswer=Possibility of Other Answer
comUssOlpQri.title.inputSubjQuest=Input of Subjective Question
comUssOlpQri.title.checkBox=Check Box

comUssOlpQri.alt.compulsoryInput=Compulsory Input Indicator

#Javascript alert#
comUssOlpQri.alert.input=must be written!