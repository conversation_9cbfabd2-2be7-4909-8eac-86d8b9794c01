#\ub85c\uadf8\uc778\ud654\uba74\uc774\ubbf8\uc9c0 \uad00\ub9ac#
ussIonLsi.loginScrinImageList.loginScrinImageList=\ub85c\uadf8\uc778\ud654\uba74\uc774\ubbf8\uc9c0 \ubaa9\ub85d
ussIonLsi.loginScrinImageList.imageNm=\uc774\ubbf8\uc9c0 \uba85
ussIonLsi.loginScrinImageList.image=\uc774\ubbf8\uc9c0
ussIonLsi.loginScrinImageList.imageDc=\uc774\ubbf8\uc9c0 \uc124\uba85
ussIonLsi.loginScrinImageList.reflctAtt=\ubc18\uc601\uc5ec\ubd80
ussIonLsi.loginScrinImageList.noImage=\uc120\ud0dd\ub41c \ub85c\uadf8\uc778\ud654\uba74 \uc774\ubbf8\uc9c0\uac00 \uc5c6\uc2b5\ub2c8\ub2e4.
ussIonLsi.loginScrinImageList.failInquire=\uc870\ud68c\ub41c \uacb0\uacfc\uac00 \uc5c6\uc2b5\ub2c8\ub2e4.
ussIonLsi.loginScrinImageList.deleteImage=\uc0ad\uc81c\ud558\uaca0\uc2b5\ub2c8\uae4c?

ussIonLsi.loginScrinImageRegist.loginScrinImageRegist=\ub85c\uadf8\uc778\ud654\uba74\uc774\ubbf8\uc9c0 \ub4f1\ub85d
ussIonLsi.loginScrinImageRegist.mainImageNm=\uc774\ubbf8\uc9c0 \uba85
ussIonLsi.loginScrinImageRegist.mainImage=\uc774\ubbf8\uc9c0
ussIonLsi.loginScrinImageRegist.mainImageId=\uc774\ubbf8\uc9c0ID
ussIonLsi.loginScrinImageRegist.mainImageDc=\uc774\ubbf8\uc9c0 \uc124\uba85
ussIonLsi.loginScrinImageRegist.mainImageReflctAt=\ubc18\uc601\uc5ec\ubd80
ussIonLsi.loginScrinImageRegist.mainImageregDate=\ub4f1\ub85d\uc77c\uc2dc
ussIonLsi.loginScrinImageRegist.saveImage=\uc800\uc7a5 \ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
ussIonLsi.loginScrinImageRegist.ImageReq=\uc774\ubbf8\uc9c0\ub294 \ud544\uc218 \uc785\ub825\uac12\uc785\ub2c8\ub2e4.
ussIonLsi.loginScrinImageRegist.deleteImage=\uc0ad\uc81c\ud558\uaca0\uc2b5\ub2c8\uae4c?

ussIonLsi.loginScrinImageUpdt.loginScrinImageUpdt=\ub85c\uadf8\uc778\ud654\uba74\uc774\ubbf8\uc9c0 \uc218\uc815
ussIonLsi.loginScrinImageUpdt.mainImageNm=\uc774\ubbf8\uc9c0 \uba85
ussIonLsi.loginScrinImageUpdt.mainImage=\uc774\ubbf8\uc9c0
ussIonLsi.loginScrinImageUpdt.mainImageId=\uc774\ubbf8\uc9c0ID
ussIonLsi.loginScrinImageUpdt.mainImageDc=\uc774\ubbf8\uc9c0 \uc124\uba85
ussIonLsi.loginScrinImageUpdt.mainImageReflctAt=\ubc18\uc601\uc5ec\ubd80
ussIonLsi.loginScrinImageUpdt.mainImageregDate=\ub4f1\ub85d\uc77c\uc2dc
ussIonLsi.loginScrinImageUpdt.saveImage=\uc800\uc7a5 \ud558\uc2dc\uaca0\uc2b5\ub2c8\uae4c?
ussIonLsi.loginScrinImageUpdt.deleteImage=\uc0ad\uc81c\ud558\uaca0\uc2b5\ub2c8\uae4c?

ussIonLsi.loginScrinImageView.loginScrinImageView=\ub85c\uadf8\uc778\ud654\uba74\uc774\ubbf8\uc9c0 \ubc18\uc601
ussIonLsi.loginScrinImageView.loginScrinImageViewDc=\uc774 \ud398\uc774\uc9c0\ub294 750. \ub85c\uadf8\uc778\uc774\ubbf8\uc9c0 \uad00\ub9ac\uc5d0 \ub4f1\ub85d\ud55c \uc774\ubbf8\uc9c0\ub4e4\uc774 \ubc18\uc601\ub418\ub294 \uacf3\uc785\ub2c8\ub2e4.