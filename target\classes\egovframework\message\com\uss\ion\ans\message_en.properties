# Anniversary Management comUssIonAns
# EgovAnnvrsryManage.xml
comUssIonAns.annvrsryManage.validate.usid = User ID
comUssIonAns.annvrsryManage.validate.annvrsrySe = Anniversary Category
comUssIonAns.annvrsryManage.validate.annvrsryDe = Dates
comUssIonAns.annvrsryManage.validate.cldrSe = solar / lunar separation
comUssIonAns.annvrsryManage.validate.annvrsryNm = Anniversary Name

# EgovAnnvrsryManageList.jsp
comUssIonAns.annvrsryManageList.title = View anniversary management list
comUssIonAns.annvrsryManageList.arrParam.directProgress = Anniversary bulk
comUssIonAns.annvrsryManageList.annvrsryManageList = Anniversary Management List
comUssIonAns.annvrsryManageList.excelRegiser = Anniversary Excel Registration
comUssIonAns.annvrsryMainList.option=If the yearly repetition option is used, yearly inquiry will be applied every year even if it is not current year

# EgovAnnvrsryDetail.jsp
comUssIonAns.annvrsryDetail.title = View anniversary details
comUssIonAns.annvrsryDetail.annvrsryDetail = Anniversary Details

# EgovAnnvrsryRegist.jsp
comUssIonAns.annvrsryRegist.title = Anniversary registration

# EgovAnnvrsryUpdt.jsp
comUssIonAns.annvrsryUpdt.title = Edit Anniversary

# EgovAnnvrsryGdcc.jsp
comUssIonAns.annvrsryGdcc.title = View anniversary details
comUssIonAns.annvrsryGdcc.icon = title icon image
comUssIonAns.annvrsryGdcc.intro = Anniversary Guide
comUssIonAns.annvrsryGdcc.alarm = Anniversary Alarm Screen
comUssIonAns.annvrsryGdcc.cldrSe1 = Solar
comUssIonAns.annvrsryGdcc.cldrSe2 = Lunar

# EgovAnnvrsryMainList.jsp
comUssIonAns.annvrsryMainList.title = Anniversary list Main list
comUssIonAns.annvrsryMainList.summary = Provides a list of  upcoming anniversaries 
comUssIonAns.annvrsryMainList.listTitle = Upcoming Anniversaries
comUssIonAns.annvrsryMainList.guide= The left menu on all-in-one common components is 930. Anniversary dates are included between D-day and the current time. You can see detailed information when you click the anniversary name.

# EgovAnnvrsryManageBndeListPop.jsp
comUssIonAns.annvrsryManageBndeListPop.title = Bulk anniversary registration
comUssIonAns.annvrsryManageBndeListPop.validate.fileValue = Please specify the file to upload.
comUssIonAns.annvrsryManageBndeListPop.validate.fileForm = The file format is incorrect. \\n Only xls, XLS, xlsx, XLSX \\nYou can upload!
comUssIonAns.annvrsryManageBndeListPop.validate.excelProcess = Completed anniversary Excel registration
comUssIonAns.annvrsryManageBndeListPop.excelFile = Anniversary Excel file
comUssIonAns.annvrsryManageBndeListPop.AllFile = Bulk file
comUssIonAns.annvrsryManageBndeListPop.upload = Upload
comUssIonAns.annvrsryManageBndeListPop.cldrSe1 = Solar calendar
comUssIonAns.annvrsryManageBndeListPop.cldrSe2 = Lunar calendar

#Common
comUssIonAns.common.yearList = Year
comUssIonAns.common.year = year
comUssIonAns.common.toNewWindow = Go to new window
comUssIonAns.common.annvrsryNm = Anniversary Title
comUssIonAns.common.annvrsryDe = Anniversary (positive / negative)
comUssIonAns.common.cldrSe1 = solar
comUssIonAns.common.cldrSe2 = lunar
comUssIonAns.common.memo = Notes
comUssIonAns.common.annvrsrySetup = Notification status
comUssIonAns.common.annvrsryBeginDday = Previous reminder
comUssIonAns.common.reptitSe = Repeatability
comUssIonAns.common.annvrsryTemp1 = Applicant
comUssIonAns.common.annvrsryTemp2 = belongs
comUssIonAns.common.annvrsryTemp3 = Anniversary Category
comUssIonAns.common.annvrsryTemp4 = Anniversary
comUssIonAns.common.reptitSeEvery = Repeat every year
comUssIonAns.common.annvrsryNm = Anniversary Name
comUssIonAns.common.memo = Notes
comUssIonAns.common.annvrsryBeginDe = Notification start date
comUssIonAns.common.annvrsryBeginDdayAlarm = Previous reminder
comUssIonAns.common.annvrsryTemp5 = Notification settings
comUssIonAns.common.userName = applicant name
comUssIonAns.common.annvrsryBeginDe.7 = one week
comUssIonAns.common.annvrsryBeginDe.3 = 3 days
comUssIonAns.common.annvrsryBeginDe.2 = 2 days
comUssIonAns.common.annvrsryBeginDe.1 = 1 day
comUssIonAns.common.annvrsryBeginDe.alarm = Notify me since
comUssIonAns.common.alarm = Notifications
comUssIonAns.common.init = Initialize
comUssIonAns.common.duplicate=Duplicated data. Check your data.