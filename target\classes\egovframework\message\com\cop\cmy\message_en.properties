#Community Master#
comCopCmy.commuMasterVO.title=Community Master Management

comCopCmy.commuMasterVO.list.cmmntyNm=Community Title
comCopCmy.commuMasterVO.list.useAt=Available Options

comCopCmy.commuMasterVO.detail.cmmntyNm=Community Title
comCopCmy.commuMasterVO.detail.cmmntyIntrcn=Community Explanation
comCopCmy.commuMasterVO.detail.useAt=Available Options
comCopCmy.commuMasterVO.detail.commuAdres=Community Domain

comCopCmy.commuMasterVO.regist.cmmntyNm=Community Title
comCopCmy.commuMasterVO.regist.cmmntyIntrcn=Community Explanation
comCopCmy.commuMasterVO.regist.useAt=Available Options

comCopCmy.commuMasterVO.updt.cmmntyNm=Community Title
comCopCmy.commuMasterVO.updt.cmmntyIntrcn=Community Explanation
comCopCmy.commuMasterVO.updt.useAt=Available Options
comCopCmy.commuMasterVO.updt.yes=Yes
comCopCmy.commuMasterVO.updt.no=No


comCopCmy.commuMasterVO.list.board=Board List

comCopCmy.commuUserVO.title=Community User Management

comCopCmy.commuUserVO.emplyrId=User ID
comCopCmy.commuUserVO.emplyrNm=User Name
comCopCmy.commuUserVO.mberSttus=Member Status
comCopCmy.commuUserVO.etc=etc


#Community Usage Management#
comCopCmy.communityUseMgrMain.title=Community Usage Management Main

comCopCmy.communityUseMgrMain.btnBoard=Board Management
comCopCmy.communityUseMgrMain.btnUser=Member Management

comCopCmy.communityUseMgrMain.btnMemberWithdrawal=Delete Your Account
comCopCmy.communityUseMgrMain.btnMemberJoin=Sign Up

comCopCmy.communityUseMgrMain.titleContents=Content Area


comCopCmy.communityUseMgrBaseTmpl.title=Community Usage Management Default Template
comCopCmy.communityUseMgrBaseTmpl.more=Load More
comCopCmy.communityUseMgrBaseTmpl.noList=Currently there is no content on the list.

comCopCmy.commuMain.joinMember.info.success=Member registration has been processed.
comCopCmy.commuMain.joinMember.info.fail=already registered as a member.
comCopCmy.commuMain.deleteMember.info.success=Your account has been deleted.
comCopCmy.commuMain.deleteMember.info.admin=Administrator accounts can not be deleted.
