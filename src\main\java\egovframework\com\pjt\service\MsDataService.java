package egovframework.com.pjt.service;

import java.util.List;
import java.util.Map;

public interface MsDataService {

    /**
     * 권 목록 조회
     */
    public List<MsDataVO> getDataList() throws Exception;

    /**
     * 프레임 목록 조회
     */
    public List<MsDataVO> getFrameByRoll(String roll) throws Exception;

    /**
     * 파일 정보 조회
     */
    public MsDataVO getFile(String roll, String frame) throws Exception;

    /**
     * 토지대장 기본정보 조회
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @return 토지대장 데이터 목록
     * @throws Exception
     */
    List<MsDataVO> getLandBasicInfo(String rollNo, String frameNo) throws Exception;

    /**
     * 토지대장 연혁 조회
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @return 토지대장 데이터 목록
     * @throws Exception
     */
    List<MsDataVO> getLandHistory(String rollNo, String frameNo) throws Exception;

    /**
     * 토지대장 날짜/사고/주소 조회
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @return 토지대장 데이터 목록
     * @throws Exception
     */
    List<MsDataVO> getLandDateInfo(String rollNo, String frameNo) throws Exception;

    /**
     * 토지대장 소유자 조회
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @return 토지대장 데이터 목록
     * @throws Exception
     */
    List<MsDataVO> getLandOwnerInfo(String rollNo, String frameNo) throws Exception;

    /**
     * 토지대장 색인정보 조회
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @return 토지대장 데이터
     * @throws Exception
     */
    MsDataVO getLandIndexInfo(String rollNo, String frameNo) throws Exception;

    /**
     * 토지대장 기본정보 저장
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @param dataList 저장할 데이터 목록
     * @return 저장 결과 (성공: true, 실패: false)
     * @throws Exception
     */
    boolean saveLandBasicInfo(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception;

    /**
     * 토지대장 연혁 저장
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @param dataList 저장할 데이터 목록
     * @return 저장 결과 (성공: true, 실패: false)
     * @throws Exception
     */
    boolean saveLandHistory(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception;

    /**
     * 토지대장 날짜/사고/주소 저장
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @param dataList 저장할 데이터 목록
     * @return 저장 결과 (성공: true, 실패: false)
     * @throws Exception
     */
    boolean saveLandDateInfo(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception;

    /**
     * 토지대장 소유자 저장
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @param dataList 저장할 데이터 목록
     * @return 저장 결과 (성공: true, 실패: false)
     * @throws Exception
     */
    boolean saveLandOwnerInfo(String rollNo, String frameNo, List<MsDataVO> dataList) throws Exception;

    /**
     * 토지대장 색인정보 저장
     *
     * @param rollNo 권 번호
     * @param frameNo 프레임 번호
     * @param data 저장할 데이터
     * @return 저장 결과 (성공: true, 실패: false)
     * @throws Exception
     */
    boolean saveLandIndexInfo(String rollNo, String frameNo, MsDataVO data) throws Exception;

    /**
     * 지목 코드 리스트 조회
     *
     * @return 지목 코드 리스트
     * @throws Exception
     */
    List<Map<String, Object>> getJimokCodeList() throws Exception;

    /**
     * 연혁 코드 리스트 조회
     *
     * @return 연혁 코드 리스트
     * @throws Exception
     */
    List<Map<String, Object>> getHistoryCodeList() throws Exception;

    /**
     * 소유자 코드 리스트 조회
     *
     * @return 소유자 코드 리스트
     * @throws Exception
     */
    List<Map<String, Object>> getReasonCodeList() throws Exception;

    /**
     * 소유자 연호 코드 리스트 조회
     *
     * @return 소유자 연호 코드 리스트
     * @throws Exception
     */
    List<Map<String, Object>> getYearCodeList() throws Exception;
    
    /**
    * 소유자 주소 리스트 조회
    */
    List<Map<String, Object>> getOwnerAddrList() throws Exception;
    
    /**
    * 소유자 이름 리스트 조회
    */
    List<Map<String, Object>> getOwnerNameList() throws Exception;
   
    /**
     * 작업코드 별 특정 권, 페이지의 마지막 순번값 조회
     *
     * @return 마지막 순번 (int)
     * @throws Exception
     */
    int getMaxSrNo(String rollNo, String frameNo, String workcode) throws Exception;


}

