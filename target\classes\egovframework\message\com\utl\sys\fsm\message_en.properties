# File system monitoring #
comUtlSysFsm.fileSysMntrng.title = File system monitoring
comUtlSysFsm.fileSysMntrngLog.title = File system monitoring log

comUtlSysFsm.fileSysMntrng.fileSysNm.label = File system name
comUtlSysFsm.fileSysMntrng.fileSysManageNm.label = File system administration name
comUtlSysFsm.fileSysMntrng.fileSysMg.label = File system size
comUtlSysFsm.fileSysMntrng.fileSysThrhld.label = Filesystem Threshold
comUtlSysFsm.fileSysMntrng.fileSysUsgQty.label = File system usage
comUtlSysFsm.fileSysMntrng.mngrNm.label = Administrator name
comUtlSysFsm.fileSysMntrng.mngrEmailAddr.label = Administrator email address
comUtlSysFsm.fileSysMntrng.mntrngSttus.label = Monitoring status
comUtlSysFsm.fileSysMntrng.creatDt.label = Date last created

comUtlSysFsm.fileSysMntrngList.seq.label = number
comUtlSysFsm.fileSysMntrngList.fileSysNm.label = File system name
comUtlSysFsm.fileSysMntrngList.fileSysManageNm.label = File system administration name
comUtlSysFsm.fileSysMntrngList.fileSysMg.label = Size
comUtlSysFsm.fileSysMntrngList.fileSysThrhld.label = Threshold
comUtlSysFsm.fileSysMntrngList.fileSysUsgQty.label = Usage
comUtlSysFsm.fileSysMntrngList.mntrngSttus.label = Status
comUtlSysFsm.fileSysMntrngList.mngrNm.label = Admin-name
comUtlSysFsm.fileSysMntrngList.guideOne=If your OS is Window 10, you need to run Eclipse as an administrator
comUtlSysFsm.fileSysMntrngList.guideTwo=Globals.OsType needs to be changed in globals.properties to suit the environment you are using on UNIX or WINDOWS. <br> The differences in system capacity paths between WINDOW and UNIX are also visible at the top of globals.properties.

comUtlSysFsm.fileSysMntrngLog.logInfo.label = Log information
comUtlSysFsm.fileSysMntrngLog.logId.label = Log ID
comUtlSysFsm.fileSysMntrngLog.fileSysNm.label = File system name
comUtlSysFsm.fileSysMntrngLog.fileSysManageNm.label = File system administration name
comUtlSysFsm.fileSysMntrngLog.fileSysMg.label = File system size
comUtlSysFsm.fileSysMntrngLog.fileSysThrhld.label = File system threshold
comUtlSysFsm.fileSysMntrngLog.fileSysUsgQty.label = File system usage
comUtlSysFsm.fileSysMntrngLog.mntrngSttus.label = Monitoring status
comUtlSysFsm.fileSysMntrngLog.creatDt.label = Creation date and time

comUtlSysFsm.fileSysMntrngLogList.seq.label = number
comUtlSysFsm.fileSysMntrngLogList.fileSysNm.label = File system name
comUtlSysFsm.fileSysMntrngLogList.fileSysManageNm.label = File system administration name
comUtlSysFsm.fileSysMntrngLogList.fileSysMg.label = Size
comUtlSysFsm.fileSysMntrngLogList.fileSysThrhld.label = Threshold
comUtlSysFsm.fileSysMntrngLogList.fileSysUsgQty.label = Usage
comUtlSysFsm.fileSysMntrngLogList.mntrngSttus.label = Status
comUtlSysFsm.fileSysMntrngLogList.creatDt.label = Date created
comUtlSysFsm.fileSysMntrngLogList.mngrNm.label = Administrator name
comUtlSysFsm.fileSysMntrngLogList.validate.bgnDe = Search start date
comUtlSysFsm.fileSysMntrngLogList.validate.endDe = Search end date
comUtlSysFsm.fileSysMntrngLogList.validate.bgnDeHourendDeHour = The search end date can not be earlier than the search start date.
comUtlSysFsm.fileSysMntrngLogList.select = Please select
comUtlSysFsm.fileSysMntrngLogList.list = list

#EgovFileSysMntrngRegist
comUtlSysFsm.fileSysMntrngRegist.notApplicableFileSysTrue = The file system name is invalid.
comUtlSysFsm.fileSysMntrngRegist.fileSysMg = Click on the File System Check button to check the size of the file system.
comUtlSysFsm.fileSysMntrngRegist.fileSysThrhldG = You must enter a file system threshold of 1G or more.
comUtlSysFsm.fileSysMntrngRegist.fileSysThrhldLimit = The file system threshold can not be greater than the file system size.
comUtlSysFsm.fileSysMntrngRegist.selectFileSysMg = Check the file system

#EgovFileSysMntrngUpdt
comUtlSysFsm.fileSysMntrngUpdt.fileSysMg = You must enter a file system size of 1G or more.
comUtlSysFsm.fileSysMntrngUpdt.fileSysThrhldG = You must enter a file system threshold of 1G or more.
comUtlSysFsm.fileSysMntrngUpdt.fileSysThrhldLimit = The file system threshold can not be greater than the file system size.
comUtlSysFsm.fileSysMntrngUpdt.tableSummary = This table is used to modify file system monitoring target information. It consists of file system name, file system management name, file system size, file system threshold, administrator name, and administrator e-mail address information.