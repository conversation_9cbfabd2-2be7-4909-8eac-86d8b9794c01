<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="authorRoleManageDAO">

    <resultMap id="authorRole" type="egovframework.com.sec.ram.service.AuthorRoleManageVO">
        <result property="roleCode" column="ROLE_CODE"/>
        <result property="roleNm" column="ROLE_NM"/>
        <result property="rolePtn" column="ROLE_PTTRN"/>
        <result property="roleDc" column="ROLE_DC"/>
        <result property="roleTyp" column="ROLE_TY"/>
        <result property="roleSort" column="ROLE_SORT"/>
        <result property="authorCode" column="AUTHOR_CODE"/>
        <result property="regYn" column="REG_YN"/>
        <result property="creatDt" column="CREAT_DT"/>
    </resultMap>

    <select id="selectAuthorRoleList" parameterType="egovframework.com.sec.ram.service.AuthorRoleManageVO" resultMap="authorRole">
<![CDATA[  

            SELECT  * 
              FROM  (
            SELECT ROWNUM RNUM, ALL_LIST.* 
              FROM  (

            SELECT A.ROLE_CODE,
                   A.ROLE_NM,
                   A.ROLE_PTTRN,
                   A.ROLE_DC,
                   A.ROLE_TY,
                   A.ROLE_SORT,
                   B.AUTHOR_CODE,
                   B.CREAT_DT,
                   (CASE WHEN B.ROLE_CODE IS NULL THEN 'N'
                         ELSE 'Y' 
                     END) AS REG_YN
              FROM COMTNROLEINFO A 
                   LEFT OUTER JOIN (SELECT AUTHOR_CODE , ROLE_CODE, CREAT_DT FROM COMTNAUTHORROLERELATE WHERE AUTHOR_CODE = #{searchKeyword}) B
                ON A.ROLE_CODE = B.ROLE_CODE        

                    ) ALL_LIST
                    ) Z

             WHERE  RNUM  > #{firstIndex}
               AND  RNUM <= #{firstIndex} + #{recordCountPerPage}
]]>  
    </select>

    <select id="selectAuthorRoleListTotCnt" parameterType="egovframework.com.sec.ram.service.AuthorRoleManageVO" resultType="int">

            SELECT COUNT(*) totcnt
              FROM COMTNROLEINFO A 
                   LEFT OUTER JOIN (SELECT AUTHOR_CODE , ROLE_CODE, CREAT_DT FROM COMTNAUTHORROLERELATE WHERE AUTHOR_CODE = #{searchKeyword}) B
                ON A.ROLE_CODE = B.ROLE_CODE
    </select>

    <insert id="insertAuthorRole" parameterType="egovframework.com.sec.ram.service.AuthorRoleManage">
        
            INSERT INTO COMTNAUTHORROLERELATE 
                  ( AUTHOR_CODE
                  , ROLE_CODE
                  , CREAT_DT)
           VALUES ( #{authorCode}
                  , #{roleCode}
                  , SYSDATETIME)
        
    </insert>

    <delete id="deleteAuthorRole">
        
            DELETE FROM COMTNAUTHORROLERELATE 
             WHERE AUTHOR_CODE=#{authorCode}
               AND ROLE_CODE=#{roleCode}
        
    </delete>
    
</mapper>