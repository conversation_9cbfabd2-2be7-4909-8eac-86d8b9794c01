<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mberManageDAO">

    <resultMap id="stplatMap" type="egovframework.com.uss.umt.service.StplatVO">
        <result property="useStplatId" column="USE_STPLAT_ID"/>
        <result property="useStplatCn" column="USE_STPLAT_CN"/>
        <result property="infoProvdAgeCn" column="INFO_PROVD_AGRE_CN"/>
    </resultMap>
    
	<select id="selectMberList" resultType="egovMap">
	    SELECT 
        ESNTL_ID              as UNIQ_ID,
        'USR01'               as USER_TY,
        MBER_ID               as USER_ID,
        MBER_NM               as USER_NM,
        MBER_EMAIL_ADRES      as EMAIL_ADRES,
        AREA_NO               as AREA_NO,
        MIDDLE_TELNO          as MIDDLE_TELNO,
        END_TELNO             as END_TELNO,
        MBTLNUM               as MOBLPHON_NO,
        GROUP_ID              as GROUP_ID,
        MBER_STTUS            as STTUS,
        SBSCRB_DE             as SBSCRB_DE
	    FROM    COMTNGNRLMBER
        WHERE 1=1
        <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
            AND MBER_STTUS LIKE  #{sbscrbSttus}
        </if>
        <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
        <if test="searchCondition == 0">AND
             MBER_ID LIKE '%' || #{searchKeyword} || '%'
        </if>
        <if test="searchCondition == 1">AND
             MBER_NM LIKE '%' || #{searchKeyword} || '%'
        </if>
        </if>
        ORDER BY SBSCRB_DE DESC
        LIMIT #{recordCountPerPage} OFFSET #{firstIndex}
    </select>
    
    
        <select id="selectMberListTotCnt" resultType="int">
            SELECT COUNT(1) totcnt
            FROM(
            SELECT 
                ESNTL_ID          "uniqId",
                'USR01'           "userTy",
                MBER_ID           "userId",
                MBER_NM           "userNm",
                MBER_EMAIL_ADRES  "emailAdres",
                AREA_NO           "areaNo",
                MIDDLE_TELNO      "middleTelno",
                END_TELNO         "endTelno",
                MBTLNUM           "moblphonNo",
                GROUP_ID          "groupId",
                MBER_STTUS        "sttus",
                SBSCRB_DE         "sbscrbDe"
            FROM    COMTNGNRLMBER
            ) A
        WHERE 1=1
            <if test='sbscrbSttus != null and sbscrbSttus neq "0" and sbscrbSttus neq ""'>
                AND a.sttus LIKE  #{sbscrbSttus}
            </if>
            <if test="@egovframework.com.cmm.util.EgovMybatisUtil@isNotEmpty(searchKeyword)">
            <if test="searchCondition == 0">AND
                 "userId" LIKE '%' || #{searchKeyword} || '%'
            </if>
            <if test="searchCondition == 1">AND
                 "userNm" LIKE '%' || #{searchKeyword} || '%'
            </if>
            </if>
    </select>
    
    <insert id="insertMber_S">
        
            INSERT INTO COMTNGNRLMBER 
                (   
                    ESNTL_ID          ,
                    MBER_ID          ,
                    MBER_NM          ,
                    PASSWORD         ,
                    PASSWORD_HINT    ,
                    PASSWORD_CNSR    ,
                    IHIDNUM          ,
                    SEXDSTN_CODE     ,
                    ZIP              ,
                    ADRES            ,
                    AREA_NO          ,
                    MBER_STTUS       ,
                    DETAIL_ADRES     ,
                    END_TELNO        ,
                    MBTLNUM      ,
                    GROUP_ID         ,
                    MBER_FXNUM       ,
                    MBER_EMAIL_ADRES ,
                    MIDDLE_TELNO     ,
                    SBSCRB_DE        ,
                    CHG_PWD_LAST_PNTTM		)
              VALUES(
                    #{uniqId},
                    #{mberId},
                    #{mberNm},
                    #{password},
                    #{passwordHint},
                    #{passwordCnsr},
                    #{ihidnum},
                    #{sexdstnCode},
                    #{zip},
                    #{adres},
                    #{areaNo},
                    #{mberSttus},
                    #{detailAdres},
                    #{endTelno},
                    #{moblphonNo},
                    #{groupId},
                    #{mberFxnum},
                    #{mberEmailAdres},
                    #{middleTelno},
                    NOW(),
                    NOW()  )
        
    </insert>
    
    <delete id="deleteMber_S">
        
            DELETE FROM COMTNGNRLMBER 
            WHERE ESNTL_ID=#{delId}
        
    </delete>
    
    <select id="selectMber_S" resultType="egovframework.com.uss.umt.service.MberManageVO">
        
            SELECT
                ESNTL_ID          uniqId,
                'USR01'          userTy,
                MBER_ID          mberId,
                MBER_NM          mberNm,
                PASSWORD         "password",
                PASSWORD_HINT    passwordHint,
                PASSWORD_CNSR    passwordCnsr,
                IHIDNUM          ihidnum,
                SEXDSTN_CODE     sexdstnCode,
                ZIP              zip,
                ADRES            adres,
                AREA_NO          areaNo,
                MBER_STTUS       mberSttus,
                DETAIL_ADRES     detailAdres,
                END_TELNO        endTelno,
                MBTLNUM      moblphonNo,
                GROUP_ID         groupId,
                MBER_FXNUM       mberFxnum,
                MBER_EMAIL_ADRES mberEmailAdres,
                MIDDLE_TELNO     middleTelno,
                SBSCRB_DE        sbscrbDe,
                LOCK_AT          lockAt
            FROM COMTNGNRLMBER
            WHERE ESNTL_ID=#{uniqId}
        
    </select>
    
    <update id="updateMber_S">
        
            UPDATE COMTNGNRLMBER 
            SET MBER_ID          = #{mberId},
                MBER_NM          = #{mberNm},
                PASSWORD_HINT    = #{passwordHint},
                PASSWORD_CNSR    = #{passwordCnsr},
                IHIDNUM          = #{ihidnum},
                SEXDSTN_CODE     = #{sexdstnCode},
                ZIP              = #{zip},
                ADRES            = #{adres},
                AREA_NO          = #{areaNo},
                MBER_STTUS       = #{mberSttus},
                DETAIL_ADRES     = #{detailAdres},
                END_TELNO        = #{endTelno},
                MBTLNUM      = #{moblphonNo},
                GROUP_ID         = #{groupId},
                MBER_FXNUM       = #{mberFxnum},
                MBER_EMAIL_ADRES = #{mberEmailAdres},
                MIDDLE_TELNO     = #{middleTelno}
            WHERE ESNTL_ID=#{uniqId}
        
    </update>
    
    <select id="selectStplat_S" resultMap="stplatMap">
        
            SELECT
                USE_STPLAT_ID           ,
                USE_STPLAT_CN           ,
                INFO_PROVD_AGRE_CN      
            FROM COMTNSTPLATINFO
            WHERE USE_STPLAT_ID=#{stplatId}
        
    </select>
    
    <update id="updatePassword_S">
        
            UPDATE COMTNGNRLMBER 
            SET 
                   PASSWORD   =  #{password}
                 , CHG_PWD_LAST_PNTTM = NOW()
            WHERE  ESNTL_ID  = #{uniqId}
        
    </update>
    
    <select id="selectPassword_S" resultType="egovframework.com.uss.umt.service.MberManageVO">
        
            SELECT
                    PASSWORD          "password" 
            FROM    COMTNGNRLMBER
            WHERE   ESNTL_ID=#{uniqId}
        
    </select>

    <update id="updateLockIncorrect">
            UPDATE COMTNGNRLMBER 
 			   SET  LOCK_AT = NULL 
			     ,  LOCK_CNT  = NULL 
			     ,  LOCK_LAST_PNTTM = NULL 
            WHERE  ESNTL_ID  = #{uniqId}
    </update>

</mapper>